{"Serilog": {"MinimumLevel": {"Default": "Debug", "Override": {"Microsoft": "Warning", "System": "Warning", "Microsoft.EntityFrameworkCore.Database.Command": "Information"}}, "Using": ["Serilog.Sinks.Console"], "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "===================={NewLine}{Category}{NewLine}{Application}{NewLine}{Level}{NewLine}{SourceContext}{NewLine}{ClientIp}{NewLine}{Controller}{NewLine}{Action}{NewLine}{RouteTemplate}{NewLine}{CurrentUser}{NewLine}{HttpExtend}{NewLine}[{CorrelationId}]{NewLine}{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz}[{Level}]{NewLine}\t{Message}{NewLine}\t{Exception}"}}], "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId"]}}