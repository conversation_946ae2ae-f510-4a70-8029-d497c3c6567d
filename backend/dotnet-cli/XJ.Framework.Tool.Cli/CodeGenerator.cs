using System.Data;
using System.Text;
using Dapper;
using Microsoft.Data.SqlClient;
using Npgsql;
using MySql.Data.MySqlClient;
using Scriban;
using Scriban.Runtime;
using Scriban.Parsing;
using System.Text.Json;
using Humanizer;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using XJ.Framework.Tool.Cli.Database;

namespace XJ.Framework.Tool.Cli;

public class CodeGenerator : IDisposable
{
    private readonly string _connectionString;
    private readonly string _dbType;
    private readonly string _domainName;
    private readonly string _output;
    private readonly string _schema;
    private readonly bool _initialStructure;
    private readonly bool _includeMgt;
    private readonly bool _override;
    private readonly IDatabaseProvider _databaseProvider;
    private readonly string _templatePath;
    private readonly TemplateLoader _templateLoader;

    public CodeGenerator(string connectionString, string dbType, string schema, string domainName, string outputDir,
        bool initialStructure, bool includeMgt = false, bool @override = false)
    {
        _connectionString = connectionString;
        _dbType = dbType.ToLower();
        _domainName = domainName;
        _output = outputDir;
        _schema = schema;
        _initialStructure = initialStructure;
        _includeMgt = includeMgt;
        _override = @override;
        _templatePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Templates");
        _templateLoader = new TemplateLoader(_templatePath);
        _databaseProvider = DatabaseProviderFactory.Create(dbType, connectionString);
    }

    public async Task GenerateAsync()
    {
        try
        {
            // 创建并打开数据库连接
            var connection = _databaseProvider.CreateConnection();
            connection.Open();

            // 获取所有表信息
            var tables = await _databaseProvider.GetTablesAsync(_schema);
            var tablesData = new List<object>();

            foreach (var table in tables)
            {
                var columns = await _databaseProvider.GetColumnsAsync(table.TableName, _schema);
                var columnsList = columns.ToList();
                var columnNames = columnsList.Select(c => c.ColumnName.ToLower()).ToList();
                var hasAuditFields = HasAuditFields(columnNames);
                var hasSoftDelete = HasSoftDelete(columnNames);

                tablesData.Add(new
                {
                    domain_name = _domainName.Replace("-", ""),
                    domain_name_lower = _domainName.ToLower(),
                    include_mgt = _includeMgt,
                    db_type = _dbType,
                    entity_name = ToPascalCase(table.TableName),
                    table_name = table.TableName,
                    schema = _schema,
                    has_soft_delete = hasSoftDelete,
                    has_audit_fields = hasAuditFields,
                    columns = columnsList.Select(c => new
                    {
                        name = ToPascalCase(c.ColumnName),
                        type = GetCSharpType(c.DataType, c.IsNullable == "YES"),
                        max_length = c.MaxLength
                    }).ToList()
                });

                // 生成每个表的相关文件
                await GenerateFilesForTable(table, columns);
            }

            if (_initialStructure)
            {
                // 复制模板文件
                CopyTemplateFiles();

                // 处理所有项目文件，传入完整的模板数据
                var templateData = new
                {
                    domain_name = _domainName.Replace("-", ""),
                    domain_name_lower = _domainName.ToLower(),
                    include_mgt = _includeMgt,
                    db_type = _dbType,
                    tables = tablesData,
                    port = DateTime.UtcNow.ToString("ffff"),
                    port_mgt = $"1{DateTime.UtcNow:ffff}",
                    connection_string = _connectionString,
                };

                await ProcessProjectFiles(templateData);

                // 查找最近的解决方案文件
                var solutionPath = FindNearestSolutionFile(_output);
                if (solutionPath != null)
                {
                    await AddProjectsToSolutionAsync(solutionPath, _output);
                }
            }
        }
        finally
        {
            Dispose();
        }
    }

    public void Dispose()
    {
        _databaseProvider.Dispose();
    }

    private bool HasAuditFields(IEnumerable<string> columnNames)
    {
        var auditFields = new[] { "created_time", "created_by", "last_modified_time", "last_modified_by" };
        return auditFields.All(field => columnNames.Contains(field.ToLower()));
    }

    private bool HasSoftDelete(IEnumerable<string> columnNames)
    {
        return columnNames.Contains("is_deleted");
    }

    private void CopyTemplateFiles()
    {
        // 复制整个Templates目录到输出目录
        var templatesOutputPath = Path.Combine(_output, "Templates");
        if (Directory.Exists(templatesOutputPath))
        {
            Directory.Delete(templatesOutputPath, true);
        }

        // 创建目标目录
        Directory.CreateDirectory(templatesOutputPath);

        // 复制所有文件
        foreach (string file in Directory.GetFiles(_templatePath))
        {
            string fileName = Path.GetFileName(file);
            string destFile = Path.Combine(templatesOutputPath, fileName);
            File.Copy(file, destFile, true);
        }

        // 递归复制所有子目录
        foreach (string dir in Directory.GetDirectories(_templatePath))
        {
            string dirName = Path.GetFileName(dir);

            // 如果是WebApi.Mgt目录且include-mgt为false，则跳过
            if (dirName.Contains("WebApi.Mgt") && !_includeMgt)
            {
                continue;
            }

            string destDir = Path.Combine(templatesOutputPath, dirName);
            CopyDirectory(dir, destDir);
        }
    }

    private void CopyDirectory(string sourceDir, string destinationDir)
    {
        // 创建目标目录
        Directory.CreateDirectory(destinationDir);

        // 复制所有文件
        foreach (string file in Directory.GetFiles(sourceDir))
        {
            string fileName = Path.GetFileName(file);
            string destFile = Path.Combine(destinationDir, fileName);

            File.Copy(file, destFile, true);
        }

        // 递归复制所有子目录
        foreach (string dir in Directory.GetDirectories(sourceDir))
        {
            string dirName = Path.GetFileName(dir);
            string destDir = Path.Combine(destinationDir, dirName);
            CopyDirectory(dir, destDir);
        }
    }

    private async Task ProcessProjectFiles(object templateData)
    {
        var templatesPath = Path.Combine(_output, "Templates");
        ProcessDirectory(templatesPath, _output, templateData);

        // 处理完成后删除临时的Templates目录
        if (Directory.Exists(templatesPath))
        {
            Directory.Delete(templatesPath, true);
        }

        await Task.CompletedTask;
    }

    private void ProcessDirectory(string templatePath, string outputPath, object templateData)
    {
        // 确保输出目录存在
        Directory.CreateDirectory(outputPath);

        // 处理所有非.scriban文件
        foreach (var file in Directory.GetFiles(templatePath))
        {
            var fileName = Path.GetFileName(file);
            // 跳过.scriban文件和以_开头的文件
            if (fileName.EndsWith(".scriban", StringComparison.OrdinalIgnoreCase) ||
                fileName.StartsWith("_"))
                continue;

            // 处理文件名中的变量
            var outputFileName = RenderTemplate(fileName, templateData);
            var outputFilePath = Path.Combine(outputPath, outputFileName);

            // 如果文件存在且不允许覆盖，则跳过
            if (File.Exists(outputFilePath) && !_override)
            {
                Console.WriteLine($"文件已存在，跳过处理: {outputFilePath}");
                continue;
            }

            // 读取并处理文件内容
            var content = File.ReadAllText(file);
            var processedContent = RenderTemplate(content, templateData);

            // 写入处理后的内容
            File.WriteAllText(outputFilePath, processedContent);
        }

        // 递归处理所有子目录
        foreach (var dir in Directory.GetDirectories(templatePath))
        {
            var dirName = Path.GetFileName(dir);

            // 跳过以_开头的目录
            if (dirName.StartsWith("_"))
                continue;

            // 如果是WebApi.Mgt目录，根据includeMgt参数决定是否处理
            if (dirName.Contains("WebApi.Mgt") && !_includeMgt)
            {
                continue;
            }

            // 处理目录名中的变量
            var outputDirName = RenderTemplate(dirName, templateData);
            var outputDirPath = Path.Combine(outputPath, outputDirName);

            ProcessDirectory(dir, outputDirPath, templateData);
        }
    }

    private string RenderTemplate(string template, object templateData)
    {
        var scribanTemplate = Template.Parse(template);
        if (scribanTemplate.HasErrors)
        {
            throw new Exception($"模板解析错误: {string.Join(Environment.NewLine, scribanTemplate.Messages)}");
        }

        var context = new TemplateContext();
        context.TemplateLoader = _templateLoader;
        var scriptObject = new ScriptObject();
        scriptObject.Import(templateData);
        context.PushGlobal(scriptObject);

        try
        {
            return scribanTemplate.Render(context);
        }
        catch (Exception ex)
        {
            throw new Exception($"模板渲染错误: {ex.Message}", ex);
        }
    }

    private async Task GenerateFilesForTable(TableInfo table, IEnumerable<ColumnInfo> columns)
    {
        var entityName = ToPascalCase(table.TableName);
        var columnsList = columns.ToList();

        // 获取主键列
        var primaryKeyColumn = columnsList.FirstOrDefault(c => c.ColumnName.ToLower() == "id");
        if (primaryKeyColumn == null)
        {
            throw new Exception($"表 {table.TableName} 没有找到主键列 'id'");
        }

        var columnNames = columnsList.Select(c => c.ColumnName.ToLower()).ToList();
        var hasAuditFields = HasAuditFields(columnNames);
        var hasSoftDelete = HasSoftDelete(columnNames);
        var isEditable = hasAuditFields || hasSoftDelete;

        var templateData = new
        {
            domain_name = _domainName.Replace("-", ""),
            domain_name_lower = _domainName.ToLower(),
            entity_name = entityName,
            table_name = table.TableName,
            schema = _schema,
            key_type = GetCSharpType(primaryKeyColumn.DataType, primaryKeyColumn.IsNullable == "YES"),
            columns = columnsList.Select(c => new
            {
                name = ToPascalCase(c.ColumnName),
                original_name = c.ColumnName.ToLower(),
                type = GetCSharpType(c.DataType, c.IsNullable == "YES"),
                nullable = c.IsNullable == "YES",
                comment = string.IsNullOrEmpty(c.Description) ? c.ColumnName : c.Description,
                is_audit = IsAuditField(c.ColumnName),
                max_length = c.MaxLength
            }).ToList(),
            properties = columnsList
                .Where(c => !IsAuditField(c.ColumnName) && c.ColumnName.ToLower() != "id")
                .Select(c => new
                {
                    name = ToPascalCase(c.ColumnName),
                    original_name = c.ColumnName.ToLower(),
                    type = GetCSharpType(c.DataType, c.IsNullable == "YES"),
                    nullable = c.IsNullable == "YES",
                    comment = string.IsNullOrEmpty(c.Description) ? c.ColumnName : c.Description,
                    max_length = c.MaxLength
                }).ToList(),
            has_audit_fields = hasAuditFields,
            has_soft_delete = hasSoftDelete,
            is_editable = isEditable,
            base_class = GetBaseClass(hasAuditFields, hasSoftDelete),
            base_interface = GetBaseInterface(hasAuditFields, hasSoftDelete),
            include_mgt = _includeMgt,
            @override = _override
        };

        // 在fullTemplatePath下搜索所有.scriban文件
        var scribanFiles = Directory.GetFiles(_templatePath, "*.scriban", SearchOption.AllDirectories)
            .Where(f => !Path.GetFileName(f).StartsWith("_"));

        foreach (var file in scribanFiles)
        {
            var mapping = new
            {
                Template = file,
                Output = RenderTemplate($"{Path.GetFileNameWithoutExtension(file)}.cs", templateData),
                Path = RenderTemplate(
                    Path.GetDirectoryName(file)!.Replace(_templatePath, "").Trim(Path.DirectorySeparatorChar),
                    templateData)
            };
            Console.WriteLine($"处理模板: {mapping.Template} -> {mapping.Output} -> {mapping.Path}");
            await GenerateFile(mapping.Template, mapping.Output, mapping.Path, templateData);
        }
    }

    private string GetBaseClass(bool hasAuditFields, bool hasSoftDelete)
    {
        if (hasSoftDelete) return "BaseSoftDeleteEntity";
        if (hasAuditFields) return "BaseAuditEntity";
        return "BaseEntity";
    }

    private string GetBaseInterface(bool hasAuditFields, bool hasSoftDelete)
    {
        if (hasSoftDelete) return "ISoftDeleteRepository";
        if (hasAuditFields) return "IAuditRepository";
        return "IRepository";
    }

    private async Task GenerateFile(string templatePath, string fileName, string outputPath, object templateData)
    {
        try
        {
            Console.WriteLine($"开始处理模板文件: {templatePath}");

            var templateContent = await File.ReadAllTextAsync(templatePath, Encoding.UTF8);
            Console.WriteLine("模板内容已加载");

            // 先验证模板语法
            var parsedTemplate = Template.Parse(templateContent);
            if (parsedTemplate.HasErrors)
            {
                var errors = string.Join("\n", parsedTemplate.Messages);
                throw new Exception($"模板语法错误:\n{errors}");
            }

            Console.WriteLine("模板语法验证通过");

            // 设置模板上下文
            var context = new TemplateContext();
            context.TemplateLoader = _templateLoader;
            var scriptObject = new ScriptObject();
            scriptObject.Import(templateData);
            context.PushGlobal(scriptObject);

            // 渲染模板
            var output = parsedTemplate.Render(context);
            Console.WriteLine("模板渲染完成");

            // 准备输出路径
            var fullOutputPath = Path.Combine(_output, outputPath);
            Directory.CreateDirectory(fullOutputPath);
            var targetFile = Path.Combine(fullOutputPath, fileName);

            // 检查文件是否存在
            if (File.Exists(targetFile) && !_override)
            {
                Console.WriteLine($"文件已存在，跳过生成: {targetFile}");
                return;
            }

            // 写入文件时指定UTF8编码（不带BOM）
            await File.WriteAllTextAsync(targetFile, output, new UTF8Encoding(false));
            Console.WriteLine($"成功生成文件: {targetFile}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"处理模板时发生错误:");
            Console.WriteLine($"模板文件: {templatePath}");
            Console.WriteLine($"目标文件: {fileName}");
            Console.WriteLine($"输出路径: {outputPath}");
            Console.WriteLine($"错误信息: {ex.Message}");
            Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");
            throw;
        }
    }

    private bool IsAuditField(string columnName)
    {
        var auditFields = new[]
        {
            "created_time",
            "created_by",
            "last_modified_time",
            "last_modified_by",
            "is_deleted"
        };

        return auditFields.Contains(columnName.ToLower());
    }

    private string? FindNearestSolutionFile(string startPath)
    {
        var currentDirectory = new DirectoryInfo(startPath);
        while (currentDirectory != null)
        {
            var solutionFiles = currentDirectory.GetFiles("*.sln");
            if (solutionFiles.Length > 0)
            {
                return solutionFiles[0].FullName;
            }

            currentDirectory = currentDirectory.Parent;
        }

        return null;
    }

    private async Task AddProjectsToSolutionAsync(string solutionPath, string projectPath)
    {
        var projectFiles = Directory.GetFiles(projectPath, "*.csproj", SearchOption.AllDirectories);
        var solutionFolder = $"src/domains/{_domainName.ToLower()}";

        foreach (var projectFile in projectFiles)
        {
            var process = new System.Diagnostics.Process
            {
                StartInfo = new System.Diagnostics.ProcessStartInfo
                {
                    FileName = "dotnet",
                    Arguments = $"sln \"{solutionPath}\" add \"{projectFile}\" --solution-folder \"{solutionFolder}\"",
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    UseShellExecute = false,
                    CreateNoWindow = true,
                    WorkingDirectory = Path.GetDirectoryName(solutionPath)!
                }
            };

            process.Start();
            await process.WaitForExitAsync();

            if (process.ExitCode != 0)
            {
                var error = await process.StandardError.ReadToEndAsync();
                throw new Exception($"添加项目到解决方案失败: {error}");
            }
        }
    }

    private string ToPascalCase(string input)
    {
        if (string.IsNullOrEmpty(input)) return input;

        // 先将输入字符串分割成单词
        var words = input.Split(new[] { '_', ' ' }, StringSplitOptions.RemoveEmptyEntries)
            .Select(word => word.ToLower())
            .ToList();

        // 处理特殊前缀
        if (words.Count > 0 && words[0] == "t")
        {
            words.RemoveAt(0);
        }

        // 将每个单词转换为Pascal命名
        var pascalCase = string.Join("", words.Select(word =>
            word.Length > 0
                ? char.ToUpper(word[0]) + word.Substring(1).ToLower()
                : ""
        ));

        // 处理特殊单词，避免被错误地单数化
        var specialWords = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase)
        {
            { "Status", "Status" },
            { "News", "News" },
            { "Settings", "Settings" },
            { "Series", "Series" },
            { "Species", "Species" }
            // 可以根据需要添加更多特殊单词
        };

        foreach (var word in specialWords.Keys)
        {
            if (pascalCase.EndsWith(word, StringComparison.OrdinalIgnoreCase))
            {
                return pascalCase;
            }
        }

        // 将复数形式转换为单数形式，但排除特殊单词
        return pascalCase.Singularize();
    }

    private string GetCSharpType(string dbType, bool isNullable)
    {
        var type = dbType.ToLower() switch
        {
            "bigint" => "long",
            "int" => "int",
            "smallint" => "short",
            "tinyint" => "byte",
            "bit" => "bool",
            "decimal" => "decimal",
            "numeric" => "decimal",
            "money" => "decimal",
            "float" => "float",
            "real" => "float",
            "datetime" => "DateTime",
            "datetime2" => "DateTime",
            "datetimeoffset" => "DateTimeOffset",
            "date" => "DateTime",
            "time" => "TimeSpan",
            "char" => "string",
            "nchar" => "string",
            "varchar" => "string",
            "nvarchar" => "string",
            "text" => "string",
            "ntext" => "string",
            "binary" => "byte[]",
            "varbinary" => "byte[]",
            "uniqueidentifier" => "Guid",
            "boolean" => "bool", // PostgreSQL
            "bool" => "bool", // PostgreSQL

            "tinyint unsigned" => "byte",
            "smallint unsigned" => "ushort",
            "int unsigned" => "uint",
            "bigint unsigned" => "ulong",
            "tinytext" => "string",
            "mediumtext" => "string",
            "longtext" => "string",
            "json" => "string",
            "timestamp" => "DateTime",
            "enum" => "string",
            "set" => "string",
            "double" => "double",
            "mediumint" => "int",
            "year" => "int",

            _ => "object"
        };

        return type;
    }
}

public class TableInfo
{
    public string TableName { get; set; } = null!;
    public string SchemaName { get; set; } = null!;
}

public class ColumnInfo
{
    public string ColumnName { get; set; } = null!;
    public string DataType { get; set; } = null!;
    public string IsNullable { get; set; } = null!;
    
    public bool IsIdentity { get; set; }
    public int? MaxLength { get; set; }
    public string? Description { get; set; }
}

// 添加模板加载器类
public class TemplateLoader : ITemplateLoader
{
    private readonly string _templatePath;
    private readonly Dictionary<string, string> _templateCache = new();

    public TemplateLoader(string templatePath)
    {
        _templatePath = templatePath;
    }

    public string GetPath(TemplateContext context, SourceSpan callerSpan, string templateName)
    {
        var path = Path.Combine(_templatePath, templateName);
        Console.WriteLine($"正在加载模板: {path}");
        return path;
    }

    public string Load(TemplateContext context, SourceSpan callerSpan, string templatePath)
    {
        if (!File.Exists(templatePath))
        {
            throw new FileNotFoundException($"模板文件不存在: {templatePath}");
        }

        if (_templateCache.TryGetValue(templatePath, out var cachedTemplate))
        {
            return cachedTemplate;
        }

        var template = File.ReadAllText(templatePath);
        _templateCache[templatePath] = template;

        // 验证模板语法
        var parsed = Template.Parse(template);
        if (parsed.HasErrors)
        {
            var errors = string.Join("\n", parsed.Messages);
            throw new Exception($"模板 {templatePath} 存在语法错误:\n{errors}");
        }

        return template;
    }

    public ValueTask<string> LoadAsync(TemplateContext context, SourceSpan callerSpan, string templatePath)
    {
        return new ValueTask<string>(Load(context, callerSpan, templatePath));
    }
}
