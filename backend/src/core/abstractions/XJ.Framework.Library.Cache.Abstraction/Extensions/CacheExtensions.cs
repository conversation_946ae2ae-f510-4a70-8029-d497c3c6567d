using System;
using System.Threading.Tasks;

namespace XJ.Framework.Library.Cache.Abstraction.Extensions;

/// <summary>
/// 缓存扩展方法
/// </summary>
public static class CacheExtensions
{
    /// <summary>
    /// 获取或创建缓存项
    /// </summary>
    /// <typeparam name="T">缓存项类型</typeparam>
    /// <param name="cache">缓存接口</param>
    /// <param name="key">缓存键</param>
    /// <param name="factory">缓存值工厂方法</param>
    /// <param name="expiration">过期时间</param>
    /// <returns>缓存项</returns>
    public static async Task<T> GetOrCreateAsync<T>(
        this ICache cache,
        string key,
        Func<Task<T>> factory,
        TimeSpan expiration)
    {
        var value = await cache.GetAsync<T?>(key);
        if (value != null)
        {
            return value;
        }

        value = await factory();
        await cache.SetAsync(key, value, expiration);
        return value;
    }
} 
