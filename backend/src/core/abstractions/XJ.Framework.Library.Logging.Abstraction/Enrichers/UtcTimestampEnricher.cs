using Serilog.Core;
using Serilog.Events;

namespace XJ.Framework.Library.Logging.Abstraction.Enrichers;

public class UtcTimestampEnricher : ILogEventEnricher
{
    public void Enrich(LogEvent logEvent, ILogEventPropertyFactory propertyFactory)
    {
        // 获取原始 LogEvent 的 UTC 时间
        var utcTimestamp = new LogEventProperty(
            "Timestamp", 
            new ScalarValue(logEvent.Timestamp.ToUniversalTime()));
        
        // 直接覆盖原始的 Timestamp 属性
        logEvent.RemovePropertyIfPresent("Timestamp");
        logEvent.AddPropertyIfAbsent(utcTimestamp);
    }
}
