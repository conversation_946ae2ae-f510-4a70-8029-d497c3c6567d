using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Serilog;
using Serilog.Events;
using Serilog.Sinks.MSSqlServer;
using System.Data;
using XJ.Framework.Library.Common.Abstraction.Configuration;

namespace XJ.Framework.Library.Logging.Abstraction.DI;

public static class ServiceCollectionExtensions
{
    public static WebApplication UseLoggingProvider<TWrapper>(this WebApplication app)
        where TWrapper : class
    {
        Serilog.Debugging.SelfLog.Enable(msg => System.Diagnostics.Debug.WriteLine(msg));

        var loggerConfiguration = new ConfigurationBuilder()
            .AddSolutionJsonFile($"database.{app.Environment.EnvironmentName}.json")
            .AddJsonFile("appsettings.json")
            .AddJsonFile(Path.Combine("settings", $"logging.{app.Environment.EnvironmentName}.json"),
                optional: true,
                reloadOnChange: true)
            .AddJsonFile($"appsettings.{app.Environment.EnvironmentName}.json")
            .AddUserSecrets<TWrapper>()
            .Build();

        var loggerConfig = new LoggerConfiguration()
            .ReadFrom.Configuration(loggerConfiguration);


        var usings = loggerConfiguration.GetSection("Serilog").GetSection("Using").Get<List<string>>();
        if (usings?.Contains("Serilog.Sinks.MSSqlServer") ?? false)
        {
            var dbConnections = loggerConfiguration.GetSection("Database")
                .GetChildren()
                .ToDictionary(
                    x => x["Name"]!,
                    x => x["ConnectionString"]!);


            var connectionString = dbConnections.TryGetValue("Logging", out var connection)
                ? connection
                : throw new InvalidOperationException("Logging connection string not found in configuration.");


            var additionalColumns = new List<SqlColumn>
            {
                // 自增主键列
                // new ColumnOptions.IdColumnOptions()
                // {
                //     PropertyName = "id",
                //     ColumnName = "id",
                //     DataType = SqlDbType.BigInt, // 使用BigInt以支持更大的日志量
                // },

                // 应用程序名称
                new SqlColumn("application", SqlDbType.NVarChar, false, 20)
                {
                    PropertyName = "Application"
                },
                // 日志分类
                new SqlColumn("category", SqlDbType.NVarChar, true, 50)
                {
                    PropertyName = "Category"
                },
                // // 日志级别
                // new ColumnOptions.LevelColumnOptions()
                // {
                //     PropertyName = "level",
                //     ColumnName = "level",
                //     DataType = SqlDbType.NVarChar,
                //     AllowNull = false,
                //     StoreAsEnum = false, // 使用字符串存储日志级别
                //     DataLength = 50 // 日志级别通常较短
                // },
                // 客户端IP
                new SqlColumn("client_ip", SqlDbType.NVarChar, true, 50)
                {
                    PropertyName = "ClientIp"
                },
                // 控制器
                new SqlColumn("controller", SqlDbType.NVarChar, true, 50)
                {
                    PropertyName = "Controller"
                },
                // 动作
                new SqlColumn("action", SqlDbType.NVarChar, true, 50)
                {
                    PropertyName = "Action"
                },
                // 路由模板
                new SqlColumn("route_template", SqlDbType.NVarChar, true, 100)
                {
                    PropertyName = "RouteTemplate"
                },
                // 当前用户
                new SqlColumn("current_user", SqlDbType.NVarChar, true, 50)
                {
                    PropertyName = "CurrentUser"
                },
                // 关联ID
                new SqlColumn("correlation_id", SqlDbType.NVarChar, true, 50)
                {
                    PropertyName = "CorrelationId"
                },
                new SqlColumn("source_context", SqlDbType.NVarChar, true, 500)
                {
                    PropertyName = "SourceContext"
                },
                // // 时间戳 (使用DateTime2类型)
                // new ColumnOptions.TimeStampColumnOptions()
                // {
                //     ColumnName = "timestamp",
                //     DataType = SqlDbType.DateTime2,
                //     ConvertToUtc = true
                // },
                // // 日志消息
                // new SqlColumn("message", SqlDbType.NVarChar, false, -1)
                // {
                //     PropertyName = "Message"
                // },
                // // 异常信息
                // new ColumnOptions.ExceptionColumnOptions()
                // {
                //     PropertyName = "exception",
                //     ColumnName = "exception",
                //     DataType = SqlDbType.NVarChar,
                //     AllowNull = true,
                //     DataLength = -1 // 使用-1表示不限制长度
                // }
            };


            loggerConfig
                .Enrich.WithUtcTimestamp(app.Services)
                .WriteTo.MSSqlServer(
                    connectionString: connectionString,
                    sinkOptions: new MSSqlServerSinkOptions()
                    {
                        TableName = "application_log",
                        AutoCreateSqlTable = true
                    },
                    columnOptions: new ColumnOptions()
                    {
                        Store = new List<StandardColumn>
                        {
                            StandardColumn.Level,
                            StandardColumn.TimeStamp,
                            StandardColumn.Message,
                            StandardColumn.Exception
                        },
                        Id = { ColumnName = "id", DataType = SqlDbType.BigInt, AllowNull = false },
                        TimeStamp = { ColumnName = "timestamp", DataType = SqlDbType.DateTimeOffset },
                        Level =
                        {
                            ColumnName = "level", DataType = SqlDbType.NVarChar, AllowNull = false, StoreAsEnum = false,
                            DataLength = 50
                        },
                        Exception =
                        {
                            ColumnName = "exception", DataType = SqlDbType.NVarChar, AllowNull = true, DataLength = -1
                        },
                        Message =
                        {
                            ColumnName = "message", DataType = SqlDbType.NVarChar, AllowNull = false, DataLength = -1
                        },
                        AdditionalColumns = additionalColumns,
                    }
                )
                .Filter.ByExcluding(evt =>
                    evt.Properties.TryGetValue("ExcludeFromDatabase", out var prop) &&
                    prop is ScalarValue { Value: true });
        }


        Log.Logger = loggerConfig.CreateLogger();

        return app;
    }

    public static WebApplicationBuilder AddLoggingProvider(this WebApplicationBuilder builder)
    {
        builder.Host.UseSerilog();
        return builder;
    }
}
