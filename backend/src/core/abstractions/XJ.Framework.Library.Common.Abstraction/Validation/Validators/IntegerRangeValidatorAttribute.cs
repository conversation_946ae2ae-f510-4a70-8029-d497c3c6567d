namespace XJ.Framework.Library.Common.Abstraction.Validation.Validators;

/// <summary>
/// 整形值范围判断校验器属性类
/// </summary>
/// <remarks>
/// 整形值范围判断校验器属性
/// </remarks>
public sealed class IntegerRangeValidatorAttribute : NumberRangeValidatorAttributeBase<int>
{
    /// <summary>
    /// IntegerRangeValidatorAttribute构造函数
    /// </summary>
    /// <param name="lowerBound">整形值下限</param>
    /// <param name="upperBound">整形值上限</param>
    /// <remarks>
    /// <code  source="..\Framework\TestProjects\DeluxeWorks.Library.Test\Validation\HelperClass\Student.cs" region="IntegerRangeValidatorAttributeUsage" lang="cs" title="如何添加整形范围校验器属性"  ></code>
    /// </remarks>
    public IntegerRangeValidatorAttribute(int lowerBound, int upperBound) : base(lowerBound, upperBound)
    {
    }

    /// <summary>
    /// 重载基类的方法，调用IntegerRangeValidator的构造方式创建一个IntegerRangeValidator实例
    /// </summary>
    /// <param name="targetType">目标类型，在本默认实现中未使用到该参数</param>
    /// <returns>IntegerRangeValidator实例</returns>
    protected override Validator DoCreateValidator(Type targetType)
    {
        return new IntegerRangeValidator(this.LowerBound, this.UpperBound, this.MessageTemplate, this.Tag);
    }
}