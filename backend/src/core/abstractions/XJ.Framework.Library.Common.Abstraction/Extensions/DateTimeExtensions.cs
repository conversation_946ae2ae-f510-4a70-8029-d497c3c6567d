using System.Globalization;
using XJ.Framework.Library.Common.Abstraction.Data.DataObjects;

namespace XJ.Framework.Library.Common.Abstraction.Extensions;

public static class DateTimeExtensions
{
    /// <summary>
    /// 转换成Javascript的日期对应的整数（从1970年1月1日开始的毫秒数）
    /// </summary>
    /// <param name="dt"></param>
    /// <returns></returns>
    public static long ToJavascriptDateNumber(this DateTime dt)
    {
        var baseTime = new DateTime(1970, 1, 1, 0, 0, 0, dt.Kind);

        return Convert.ToInt64((dt - baseTime).TotalMilliseconds);
    }

    /// <summary>
    /// 去掉时间中的分秒，仅仅保留到小时
    /// </summary>
    /// <param name="dt"></param>
    /// <param name="sectionInHour">按照几小时划分区间</param>
    /// <returns></returns>
    public static DateTimeOffset RoundToHour(this DateTimeOffset dt, int sectionInHour = 1)
    {
        (sectionInHour > 0)
            .FalseThrow($"sectionInHour的值{sectionInHour}非法，必须大于零");

        var remainder = dt.Hour % sectionInHour;
        var roundedHours = dt.Hour - remainder;

        return new DateTimeOffset(dt.Year, dt.Month, dt.Day, roundedHours, 0, 0, dt.Offset);
    }

    /// <summary>
    /// 去掉时间中的秒，仅仅保留到小时和分钟。
    /// </summary>
    /// <param name="dt"></param>
    /// <param name="sectionInMin">按照几分钟划分区间</param>
    /// <returns></returns>
    public static DateTimeOffset RoundToMinute(this DateTimeOffset dt, int sectionInMin = 1)
    {
        (sectionInMin > 0)
            .FalseThrow($"sectionInMin的值{sectionInMin}非法，必须大于零");

        var remainder = dt.Minute % sectionInMin;
        var roundedMinutes = dt.Minute - remainder;

        return new DateTimeOffset(dt.Year, dt.Month, dt.Day, dt.Hour, roundedMinutes, 0, dt.Offset);
    }

    /// <summary>
    /// 起吊时分秒，按照时区调整后，对齐到天
    /// </summary>
    /// <param name="dt"></param>
    /// <param name="offset"></param>
    /// <param name="sectionInDay"></param>
    /// <returns></returns>
    public static DateTimeOffset RoundToDay(this DateTimeOffset dt, TimeSpan offset, int sectionInDay = 1)
    {
        var dtOffset = dt.ToOffset(offset);

        var remainder = dtOffset.Hour % sectionInDay;
        var roundedDays = dtOffset.Day - remainder;

        return new DateTimeOffset(dtOffset.Year, dtOffset.Month, roundedDays, 0, 0, 0, dtOffset.Offset);
    }

    /// <summary>
    /// 构造一个对齐后的时间区间
    /// </summary>
    /// <param name="dt"></param>
    /// <param name="timeSpan"></param>
    /// <returns></returns>
    public static TimeScope GetTimeScope(this DateTimeOffset dt, TimeSpan timeSpan)
    {
        return new TimeScope(dt, dt.Add(timeSpan));
    }

    /// <summary>
    /// 转换为ISO8601格式
    /// </summary>
    /// <param name="dt"></param>
    /// <returns></returns>
    public static string ToISO8601(this DateTime dt)
    {
        return dt.ToString("o");
    }

    /// <summary>
    /// 将一个日期类型调用ToUniversalTime()转换为UTC，如果该值已经是UTC，则不转换。
    /// 如果该值为DateTime.MinValue或者MaxValue，也不进行转换
    /// </summary>
    /// <param name="dt"></param>
    /// <returns></returns>
    public static DateTime ToUtc(this DateTime dt)
    {
        var result = dt;

        if (dt.Kind != DateTimeKind.Utc && dt != DateTime.MinValue && dt != DateTime.MaxValue)
            result = dt.ToUniversalTime();

        return result;
    }

    /// <summary>
    /// Javascript的日期对应的整数（从1970年1月1日开始的毫秒数）转换成DateTime
    /// </summary>
    /// <param name="jsMilliseconds"></param>
    /// <returns></returns>
    public static DateTime JavascriptDateNumberToDateTime(this long jsMilliseconds)
    {
        var baseTime = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);

        return baseTime.AddMilliseconds(jsMilliseconds).ToLocalTime();
    }

    /// <summary>
    /// 将日期类型转换为一个Dictionary，里面包含Ticks和DateKind，主要是和Json序列化中的类型转换相关
    /// </summary>
    /// <param name="dt"></param>
    /// <returns></returns>
    public static IDictionary<string, object> ToDictionary(this DateTime dt)
    {
        Dictionary<string, object> dict = new Dictionary<string, object>();

        dict["DateValue"] = dt.Ticks;
        dict["DateKind"] = dt.Kind;

        return dict;
    }

    /// <summary>
    /// 将一个Dictionary转换成DateTime，需要这个Dictionaty中有DateValue值(Ticks)和DateKind值(DateTimeKind)。
    /// 如果没有，则返回DateTime.MinValue
    /// </summary>
    /// <param name="dictionary"></param>
    /// <returns></returns>
    public static DateTime ToDateTime(this IDictionary<string, object>? dictionary)
    {
        dictionary.NullCheck(nameof(dictionary));

        var result = DateTime.MinValue;

        var ticks = dictionary.GetValue("DateValue", -1L);
        var kind = dictionary.GetValue("DateKind", DateTimeKind.Local);

        if (ticks != -1)
        {
            result = new DateTime(ticks, kind);

            if (result != DateTime.MinValue && result.Kind == DateTimeKind.Utc)
                result = result.ToLocalTime();
        }

        return result;
    }

    /// <summary>
    /// 如果时间是MinValue，则执行Action
    /// </summary>
    /// <param name="data"></param>
    /// <param name="action"></param>
    /// <returns>返回传入的data</returns>
    public static DateTime IsMinValue(this DateTime data, Action action)
    {
        if (data == DateTime.MinValue && action != null)
            action();

        return data;
    }

    /// <summary>
    /// 如果时间不是MinValue，则执行Action
    /// </summary>
    /// <param name="data"></param>
    /// <param name="action"></param>
    /// <returns>返回传入的data</returns>
    public static DateTime IsNotMinValue(this DateTime data, Action<DateTime> action)
    {
        if (data != DateTime.MinValue && action != null)
            action(data);

        return data;
    }

    /// <summary>
    /// 如果Nullable的值是默认值，则执行Action
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="data"></param>
    /// <param name="action"></param>
    /// <returns>返回</returns>
    public static Nullable<T> IsNullOrDefault<T>(this Nullable<T> data, Action action)
        where T : struct
    {
        var value = data.GetValueOrDefault();
        var defaultValue = default(T);

        if (data == null || value.Equals(defaultValue))
            if (action != null)
                action();

        return data;
    }

    /// <summary>
    /// 如果日期是MinValue，则执行Action
    /// </summary>
    /// <param name="data"></param>
    /// <param name="action"></param>
    /// <returns>返回传入的日期</returns>
    public static DateTimeOffset IsMinValue(this DateTimeOffset data, Action action)
    {
        if (data == DateTimeOffset.MinValue && action != null)
            action();

        return data;
    }

    /// <summary>
    /// 将某个TimeSpan的小时调整为一天之内的时间。算法是+24小时然后在对24取模
    /// </summary>
    /// <param name="ts"></param>
    /// <returns></returns>
    public static TimeSpan NormallizeHourToOneDay(this TimeSpan ts)
    {
        var hour = (ts.Hours + 24) % 24;

        return new TimeSpan(0, ts.Hours, ts.Minutes, ts.Seconds, ts.Milliseconds);
    }

    /// <summary>
    /// 将某个TimeSpan的格式化为小时和分钟的时区格式，且补充 + - 号
    /// 譬如：东八区 +08:00; 西八区 -08:00
    /// </summary>
    /// <param name="timeSpan"></param>
    /// <returns></returns>
    public static string ToTimeZone(this TimeSpan timeSpan)
    {
        var prefix = "+";

        if (timeSpan < TimeSpan.Zero)
        {
            prefix = "-";
        }

        return $"{prefix}{timeSpan.ToString(@$"hh\:mm")}";
    }

    public static DateTime ToDateTime(this string input, bool withTime = true)
    {
        var patterns = withTime
            ? Patterns.DateTimePattern.DATE_TIME_FORMAT_PATTERN
            : Patterns.DateTimePattern.DATE_FORMAT_PATTERN;
        return ToDateTime(input, patterns);
    }

    public static DateTime ToDateTime(this string input, string format)
    {
        return DateTime.ParseExact(input, format, null);
    }

    public static DateTimeOffset ToDateTimeOffset(this string input, bool withTime = true)
    {
        var patterns = withTime
            ? Patterns.DateTimePattern.DATE_TIME_FORMAT_PATTERN
            : Patterns.DateTimePattern.DATE_FORMAT_PATTERN;
        return ToDateTimeOffset(input, patterns);
    }

    public static DateTimeOffset ToDateTimeOffset(this string input, string format)
    {
        // Patterns.DateTimePattern.DATE_TIME_FORMAT_PATTERN
        return System.DateTimeOffset.ParseExact(input, format, CultureInfo.CurrentCulture,
            DateTimeStyles.AssumeLocal);
    }
}