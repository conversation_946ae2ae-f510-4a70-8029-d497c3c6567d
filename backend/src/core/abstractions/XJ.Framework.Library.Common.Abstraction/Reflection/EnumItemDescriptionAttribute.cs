namespace XJ.Framework.Library.Common.Abstraction.Reflection;

/// <summary>
/// 定义了枚举型中每个枚举项附加的属性，这个属性包含了对该枚举项的描述信息
/// </summary>
/// <remarks>
/// 这些描述信息包括：描述信息，ID号，短名。
/// </remarks>
[AttributeUsageAttribute(AttributeTargets.Field, Inherited = false, AllowMultiple = false)]
public class EnumItemDescriptionAttribute : System.Attribute, IComparable<EnumItemDescriptionAttribute>
{
    public EnumItemDescriptionAttribute()
    {
    }

    public EnumItemDescriptionAttribute(string name)
    {
        this.Name = name;
    }

    public EnumItemDescriptionAttribute(string name, int sortId)
    {
        this.Name = name;
        this.SortId = sortId;
    }

    public EnumItemDescriptionAttribute(string name, string shortName, int sortId)
    {
        this.Name = name;
        this.ShortName = shortName;
        this.SortId = sortId;
    }

    /// <summary>
    /// 原始枚举的名称，和EnumValue对应。
    /// </summary>
    public string EnumName {
        get;
        set;
    } = string.Empty;

    /// <summary>
    /// 枚举项排序的ID
    /// </summary>
    /// <remarks>枚举项排序的ID，当对枚举项进行排序时，使用该属性进行排序，该属性是只读的
    /// </remarks>
    public int SortId {
        get;
        set;
    } = int.MinValue;

    /// <summary>
    /// 枚举项的值
    /// </summary>
    /// <remarks>该属性是只读的
    /// </remarks>
    public int EnumValue {
        get;
        set;
    } = int.MinValue;

    /// <summary>
    /// 枚举项的短名
    /// </summary>
    /// <remarks>
    /// </remarks>
    public string ShortName {
        get;
        set;
    } = string.Empty;

    /// <summary>
    /// 分组名称
    /// </summary>
    public string GroupName {
        get;
        set;
    } = string.Empty;

    /// <summary>
    /// 枚举项的描述信息
    /// </summary>
    /// <remarks>
    /// </remarks> 
    public string Description {
        get;
        set;
    } = string.Empty;

    /// <summary>
    /// 类别
    /// </summary>
    public string Category {
        get;
        set;
    } = string.Empty;

    /// <summary>
    /// 枚举项的名字
    /// </summary>
    /// <remarks>
    /// 该属性是只读的
    /// </remarks>
    public string Name {
        get;
        set;
    } = string.Empty;

    /// <summary>
    /// 过滤器（预留的属性）
    /// </summary>
    public string Filter {
        get;
        set;
    } = string.Empty;

    /// <summary>
    /// 标签
    /// </summary>
    public object Tag {
        get;
        set;
    } = string.Empty;

    #region IComparer<EnumItemDescription> 成员

    /// <summary>
    /// 枚举项的比较方法
    /// </summary>
    /// <param name="x">需要比较的枚举项描述类的实例</param>
    /// <param name="y">需要比较的枚举项描述类的实例</param>
    /// <returns>比较结果</returns>
    /// <remarks>枚举项的比较方法,返回值是两个实例的排序号相减的结果
    /// </remarks>
    public int Compare(EnumItemDescriptionAttribute? x, EnumItemDescriptionAttribute? y)
    {
        var xSort = (x != null) ? x.SortId : -1;
        var ySort = (y != null) ? y.SortId : -1;

        return xSort - ySort;
    }

    public int CompareTo(EnumItemDescriptionAttribute? other)
    {
        var otherSort = (other != null) ? other.SortId : int.MinValue;

        return this.SortId - otherSort;
    }

    #endregion
}