using XJ.Framework.Library.Common.Abstraction.Reflection;

namespace XJ.Framework.Library.Common.Abstraction.Expressions;

/// <summary>
/// 表达式中的数据类型
/// </summary>
public enum ExpressionDataType
{
    /// <summary>
    /// 字符串
    /// </summary>
    [EnumItemDescription("字符串", ShortName = "字符串")]
    String,

    /// <summary>
    /// 数字
    /// </summary>
    [EnumItemDescription("数字", ShortName = "数字")]
    Number,

    /// <summary>
    /// 布尔型
    /// </summary>
    [EnumItemDescription("布尔型", ShortName = "布尔型")]
    Boolean,

    /// <summary>
    /// 日期型
    /// </summary>
    [EnumItemDescription("日期型", ShortName = "日期型")]
    DateTime
}