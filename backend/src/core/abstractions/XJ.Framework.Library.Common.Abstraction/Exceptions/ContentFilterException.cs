using XJ.Framework.Library.Common.Abstraction.Constants;

namespace XJ.Framework.Library.Common.Abstraction.Exceptions;

/// <summary>
/// ���ݹ����쳣
/// </summary>
public class ContentFilterException : NonTransientException
{
    public ContentFilterException()
    {
    }

    public ContentFilterException(string message) : base(message)
    {
    }

    public ContentFilterException(string message, string blockerName, string[] reason) : base(message)
    {
        this.BlockerName = blockerName;
        this.Reason = reason;
    }

    public string BlockerName {
        get;
    } = string.Empty;

    public string[] Reason {
        get;
    } = Empty.StringArray;
}