using System.Linq.Expressions;
using XJ.Framework.Library.Domain.Common;
using XJ.Framework.Library.Domain.Entities;

namespace XJ.Framework.Library.Application.Contract.Extensions;

public static class QueryCriteriaExtensions
{
    public static List<OrderbyDirection<TEntity>> BuildOrderExpression<TKey, TEntity, TQueryCriteria>(
        this PagedQueryCriteria<TQueryCriteria> criteria)
        where TKey : struct
        where TEntity : BaseEntity<TKey>
        where TQueryCriteria : BaseQueryCriteria
    {
        if (criteria?.OrderBy == null || !criteria.OrderBy.Any())
            return new List<OrderbyDirection<TEntity>>();

        return criteria.OrderBy.Select(x => Orderby<TEntity>(x)).ToList();
    }

    public static string CapitalizeFirstLetter(string input)
    {
        if (string.IsNullOrEmpty(input)) return input;

        // 判断首字母是否为小写
        if (char.<PERSON>Lower(input[0]))
        {
            // 将首字母转为大写
            return char.ToUpper(input[0]) + input.Substring(1);
        }

        return input;
    }

    private static OrderbyDirection<TEntity> Orderby<TEntity>(IOrderByRequestItem orderByItem)
    {
        var dataField = CapitalizeFirstLetter(orderByItem.DataField);

        var propertyInfo = typeof(TEntity).GetProperty(dataField);
        if (propertyInfo == null)
        {
            throw new InvalidOperationException(
                $"Property '{dataField}' not found on type '{typeof(TEntity).Name}'.");
        }

        var parameter = Expression.Parameter(typeof(TEntity), "e");
        var propertyAccess = Expression.MakeMemberAccess(parameter, propertyInfo);
        var lambda =
            Expression.Lambda<Func<TEntity, object>>(Expression.Convert(propertyAccess, typeof(object)), parameter);

        return new OrderbyDirection<TEntity>(lambda,
            orderByItem.SortDirection == FieldSortDirection.Ascending
                ? SortDirection.Ascending
                : SortDirection.Descending);
    }

    public static Expression<Func<TEntity, bool>> BuildExpression<TKey, TEntity, TQueryCriteria>(
        this TQueryCriteria criteria)
        where TKey : struct
        where TEntity : BaseEntity<TKey>
        where TQueryCriteria : BaseQueryCriteria
    {
        if (criteria == null)
            return DynamicLinqExpressions.True<TEntity>();

        var expression = DynamicLinqExpressions.True<TEntity>();
        var properties = typeof(TQueryCriteria).GetProperties();

        foreach (var property in properties)
        {
            var queryOperator = property.GetCustomAttributes(typeof(QueryOperatorAttribute), true)
                .FirstOrDefault() as QueryOperatorAttribute;

            if (queryOperator == null) continue;

            var propertyName = queryOperator.PropertyName ?? property.Name;
            var propertyValue = property.GetValue(criteria);

            try
            {
                var exp = BuildExpression<TEntity>(queryOperator.Operator, propertyName, propertyValue);
                if (exp != null)
                    expression = expression.And(exp);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException(
                    $"Error building expression for property '{propertyName}' with operator '{queryOperator.Operator}'",
                    ex);
            }
        }

        return expression;
    }

    private static Expression<Func<TEntity, bool>>? BuildExpression<TEntity>(
        QueryOperator queryOperator,
        string propertyName,
        object? operatorValue)
    {
        if (operatorValue == null) return null;

        var entityProperty = typeof(TEntity).GetProperty(propertyName);
        if (entityProperty == null)
            throw new InvalidOperationException(
                $"Property '{propertyName}' not found on type '{typeof(TEntity).Name}'.");

        var parameter = Expression.Parameter(typeof(TEntity), "e");
        var propertyAccess = Expression.Property(parameter, entityProperty);

        // 处理字符串类型
        if (entityProperty.PropertyType == typeof(string))
        {
            return CreateStringExpression<TEntity>(queryOperator, parameter, propertyAccess, operatorValue);
        }

        // 处理集合类型
        if (queryOperator == QueryOperator.In && operatorValue is IEnumerable collection)
        {
            return CreateCollectionExpression<TEntity>(parameter, propertyAccess, collection,
                entityProperty.PropertyType);
        }

        // 处理Between操作符
        if (queryOperator == QueryOperator.Between && operatorValue is IList list)
        {
            return CreateBetweenExpression<TEntity>(parameter, propertyAccess, list, entityProperty.PropertyType);
        }

        // 处理其他类型
        return CreateComparisonExpression<TEntity>(queryOperator, parameter, propertyAccess, operatorValue,
            entityProperty.PropertyType);
    }

    private static Expression<Func<TEntity, bool>> CreateStringExpression<TEntity>(
        QueryOperator queryOperator,
        ParameterExpression parameter,
        MemberExpression propertyAccess,
        object value)
    {
        var lambda = Expression.Lambda<Func<TEntity, string>>(propertyAccess, parameter);


        if (queryOperator == QueryOperator.In && value is IEnumerable collection)
        {
            return CreateCollectionExpression<TEntity>(parameter, propertyAccess, collection, typeof(string));
        }

        var stringValue = value.ToString() ?? string.Empty;
        if (string.IsNullOrEmpty(stringValue))
            return DynamicLinqExpressions.True<TEntity>();

        return queryOperator switch
        {
            QueryOperator.Contains => DynamicLinqExpressions.Contains(lambda, stringValue),
            QueryOperator.Equal => DynamicLinqExpressions.EqualTo(lambda, stringValue),
            QueryOperator.NotEqual => DynamicLinqExpressions.NotEqualTo(lambda, stringValue),
            QueryOperator.StartsWith => DynamicLinqExpressions.StartsWith(lambda, stringValue),
            QueryOperator.EndsWith => DynamicLinqExpressions.EndsWith(lambda, stringValue),
            _ => throw new NotSupportedException($"Operator {queryOperator} is not supported for string type")
        };
    }

    private static Expression<Func<TEntity, bool>> CreateCollectionExpression<TEntity>(
        ParameterExpression parameter,
        MemberExpression propertyAccess,
        IEnumerable collection,
        Type propertyType)
    {
        // 处理空集合
        if (collection == null || !collection.Cast<object>().Any())
        {
            return DynamicLinqExpressions.True<TEntity>();
        }

        try
        {
            // var values = collection.Cast<object>()
            //     .Select(x => DataConverter.ChangeType(x?.GetType() ?? typeof(object), x, propertyType))
            //     .Where(x => x != null)
            //     .ToList();

            var targetListType = typeof(List<>).MakeGenericType(propertyType);
            var values = (IList)Activator.CreateInstance(targetListType)!;

            foreach (var item in collection)
            {
                var convertedItem = DataConverter.ChangeType(item?.GetType() ?? typeof(object), item, propertyType);
                if (convertedItem != null)
                {
                    values.Add(convertedItem);
                }
            }

            // 使用正确的泛型类型创建 lambda
            var lambda = Expression.Lambda(
                propertyAccess,
                parameter);

            var method = typeof(DynamicLinqExpressions)
                .GetMethod(nameof(DynamicLinqExpressions.In))
                ?.MakeGenericMethod(typeof(TEntity), propertyType);

            if (method == null)
                throw new InvalidOperationException("In method not found");

            return (Expression<Func<TEntity, bool>>)method.Invoke(null, new object[] { lambda, values })!;
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"Error creating In expression for type {propertyType.Name}", ex);
        }
    }

    private static Expression<Func<TEntity, bool>> CreateBetweenExpression<TEntity>(
        ParameterExpression parameter,
        MemberExpression propertyAccess,
        IList values,
        Type propertyType)
    {
        // 验证输入
        if (values == null || values.Count != 2)
        {
            throw new ArgumentException(
                $"Between operator requires exactly two values, got {values?.Count ?? 0} values");
        }

        try
        {
            var lowerValue = DataConverter.ChangeType(values[0]?.GetType() ?? typeof(object), values[0], propertyType);
            var upperValue = DataConverter.ChangeType(values[1]?.GetType() ?? typeof(object), values[1], propertyType);

            if (lowerValue == null || upperValue == null)
                throw new InvalidOperationException($"Cannot convert values to type {propertyType.Name}");

            // 确保下限值小于等于上限值
            if (Comparer<object>.Default.Compare(lowerValue, upperValue) > 0)
            {
                (lowerValue, upperValue) = (upperValue, lowerValue);
            }

            // 使用正确的泛型类型创建 lambda
            var lambda = Expression.Lambda(
                propertyAccess,
                parameter);

            var method = typeof(DynamicLinqExpressions)
                .GetMethod(nameof(DynamicLinqExpressions.Between))
                ?.MakeGenericMethod(typeof(TEntity), propertyType);

            if (method == null)
                throw new InvalidOperationException("Between method not found");

            return (Expression<Func<TEntity, bool>>)
                method.Invoke(null, new object[] { lambda, lowerValue, upperValue })!;
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"Error creating Between expression for type {propertyType.Name}", ex);
        }
    }

    private static Expression<Func<TEntity, bool>> CreateComparisonExpression<TEntity>(
        QueryOperator queryOperator,
        ParameterExpression parameter,
        MemberExpression propertyAccess,
        object value,
        Type propertyType)
    {
        try
        {
            var typedValue = DataConverter.ChangeType(value?.GetType() ?? typeof(object), value, propertyType);
            if (typedValue == null)
                throw new InvalidOperationException($"Cannot convert value to type {propertyType.Name}");

            // 使用正确的泛型类型创建 lambda
            var lambda = Expression.Lambda(
                propertyAccess,
                parameter);

            var method = queryOperator switch
            {
                QueryOperator.Equal => nameof(DynamicLinqExpressions.EqualTo),
                QueryOperator.NotEqual => nameof(DynamicLinqExpressions.NotEqualTo),
                QueryOperator.GreaterThan => nameof(DynamicLinqExpressions.GreaterThan),
                QueryOperator.GreaterThanOrEqual => nameof(DynamicLinqExpressions.GreaterThanOrEqual),
                QueryOperator.LessThan => nameof(DynamicLinqExpressions.LessThan),
                QueryOperator.LessThanOrEqual => nameof(DynamicLinqExpressions.LessThanOrEqual),
                _ => throw new NotSupportedException($"Operator {queryOperator} is not supported")
            };

            var genericMethod = typeof(DynamicLinqExpressions)
                .GetMethod(method)
                ?.MakeGenericMethod(typeof(TEntity), propertyType);

            if (genericMethod == null)
                throw new InvalidOperationException($"{method} method not found");

            return (Expression<Func<TEntity, bool>>)genericMethod.Invoke(null, new object[] { lambda, typedValue })!;
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException(
                $"Error creating comparison expression for type {propertyType.Name} with operator {queryOperator}", ex);
        }
    }
}
