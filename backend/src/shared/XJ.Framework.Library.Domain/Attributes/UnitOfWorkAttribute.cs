using System.Data;

namespace XJ.Framework.Library.Domain.Attributes;

/// <summary>
/// 工作单元特性
/// 用于标记需要事务的方法
/// </summary>
[AttributeUsage(AttributeTargets.Method)]
public class UnitOfWorkAttribute : Attribute
{
    /// <summary>
    /// 是否启用事务
    /// </summary>
    public bool IsTransactional { get; set; }

    /// <summary>
    /// 事务超时时间（秒）
    /// </summary>
    public int Timeout { get; set; } = 30;

    /// <summary>
    /// 事务隔离级别
    /// </summary>
    public IsolationLevel IsolationLevel { get; set; } = IsolationLevel.ReadCommitted;

    /// <summary>
    /// 初始化工作单元特性
    /// </summary>
    public UnitOfWorkAttribute()
    {
        IsTransactional = true;
    }

    /// <summary>
    /// 初始化工作单元特性
    /// </summary>
    /// <param name="isTransactional">是否启用事务</param>
    public UnitOfWorkAttribute(bool isTransactional)
    {
        IsTransactional = isTransactional;
    }

    /// <summary>
    /// 初始化工作单元特性
    /// </summary>
    /// <param name="isTransactional">是否启用事务</param>
    /// <param name="timeout">事务超时时间（秒）</param>
    public UnitOfWorkAttribute(bool isTransactional, int timeout)
        : this(isTransactional)
    {
        Timeout = timeout;
    }

    /// <summary>
    /// 初始化工作单元特性
    /// </summary>
    /// <param name="isTransactional">是否启用事务</param>
    /// <param name="timeout">事务超时时间（秒）</param>
    /// <param name="isolationLevel">事务隔离级别</param>
    public UnitOfWorkAttribute(bool isTransactional, int timeout, IsolationLevel isolationLevel)
        : this(isTransactional, timeout)
    {
        IsolationLevel = isolationLevel;
    }
} 