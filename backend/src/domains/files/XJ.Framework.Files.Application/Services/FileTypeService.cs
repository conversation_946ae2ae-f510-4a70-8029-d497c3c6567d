namespace XJ.Framework.Files.Application.Services;

/// <summary>
/// FileType 服务实现
/// </summary>
public sealed class FileTypeService :
    BaseEditableAppService<long, FileTypeEntity, FileTypeDto, FileTypeOperationDto, IFileTypeRepository,
        FileTypeQueryCriteria>,
    IFileTypeService
{
    public FileTypeService(IFileTypeRepository repository, IMapper mapper, IUnitOfWork unitOfWork,
        IKeyGenerator<long> keyGenerator, ICurrentUserContext currentUserContext) : base(repository, mapper, unitOfWork,
        keyGenerator, currentUserContext)
    {
    }

    public async Task<FileTypeDto?> GetByCodeAsync(string requestFileTypeCode)
    {
        return await GetDtoAsync(await Repository.GetAsync(q =>
            q.TypeCode.ToLower().Equals(requestFileTypeCode.ToLower())));
    }
}