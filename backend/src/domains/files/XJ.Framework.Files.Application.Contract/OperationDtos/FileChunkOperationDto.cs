using XJ.Framework.Files.Domain.Shared.Enums;

namespace XJ.Framework.Files.Application.Contract.OperationDtos;

/// <summary>
/// FileChunk 操作 DTO
/// </summary>
public class FileChunkOperationDto : BaseOperationDto
{
    /// <summary>
    /// 文件ID
    /// </summary>
    public long FileId { get; set; }

    /// <summary>
    /// 存储信息code
    /// </summary>
    public string StorageCode { get; set; } = null!;

    /// <summary>
    /// 分块序号（从0开始）
    /// </summary>
    public int ChunkIndex { get; set; }

    /// <summary>
    /// 分块hash值
    /// </summary>
    public string ChunkHash { get; set; } = null!;

    /// <summary>
    /// 分块大小（字节）
    /// </summary>
    public long ChunkSize { get; set; }

    /// <summary>
    /// 状态（0-未上传，1-已上传）
    /// </summary>
    public FileChunkStatus Status { get; set; }

    /// <summary>
    /// 分块存储路径
    /// </summary>
    public string StoragePath { get; set; } = null!;

    /// <summary>
    /// 分块文件名
    /// </summary>
    public string FileName { get; set; } = null!;
}