using XJ.Framework.Files.Domain.Entities;
namespace XJ.Framework.Files.Application.Contract.Interfaces;

/// <summary>
/// FileChunk 服务接口
/// </summary>
public interface IFileChunkService :
    IAppService<long, FileChunkDto, FileChunkQueryCriteria>,
    IEditableAppService<long, FileChunkOperationDto>
{
    Task<FileUploadResponseDto> UploadChunkAsync(FileChunkUploadDto request, Stream stream,
        CancellationToken cancellationToken = default);

    Task<FileUploadResponseDto> GetUploadProgressAsync(Guid fileId);

    /// <summary>
    /// 获取文件流（支持分块、限流、断点续传）
    /// </summary>
    /// <param name="fileId">文件id</param>
    /// <param name="start">获取的开始位置</param>
    /// <param name="end">获取的结束</param>
    /// <param name="chunkSize">每次分块大小，默认64KB</param>
    /// <param name="delayMs">每块之间延迟，默认0</param>
    /// <param name="cancellationToken"></param>
    /// <returns>异步分块流</returns>
    IAsyncEnumerable<byte[]> GetFileStreamAsync(
        Guid fileId,
        long? start = null,
        long? end = null,
        int chunkSize = 64 * 1024,
        int delayMs = 0,
        CancellationToken cancellationToken = default);

}