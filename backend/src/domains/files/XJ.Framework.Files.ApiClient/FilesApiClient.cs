using XJ.Framework.Library.Common.Abstraction.Contexts;
using XJ.Framework.Library.Domain.Shared.Interfaces;
using HttpMethod = System.Net.Http.HttpMethod;

namespace XJ.Framework.Files.ApiClient;

public class FilesApiClient : BaseApiClient
{
    private readonly string _baseUrl;

    public FilesApiClient(HttpClient httpClient, IOptions<EndpointOption> endpointOption, ILogger<BaseApiClient> logger,
        IAuthInfoGetter authInfoGetter, IContextContainer contextContainer, ICurrentUserContext currentUserContext)
        : base(httpClient, logger, authInfoGetter, contextContainer, currentUserContext)
    {
        _baseUrl = endpointOption.Value["Files"]!.Url.TrimEnd('/');
    }

    public async Task<byte[]> DownloadFileAsync(string fileId, string fileName,
        CancellationToken cancellationToken)
    {
        var url = $"{_baseUrl}/FileInfo/{fileId}/{fileName}";
        return await GetByteArrayAsync(url: url, cancellationToken: cancellationToken);
    }
}
