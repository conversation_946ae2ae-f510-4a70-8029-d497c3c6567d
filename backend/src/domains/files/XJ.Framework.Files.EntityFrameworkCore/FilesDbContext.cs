namespace XJ.Framework.Files.EntityFrameworkCore;

public class FilesDbContext : BaseDbContext
{
    public FilesDbContext(DbContextOptions options, IConfiguration configuration,
        IOptions<DatabaseOption> databaseOptions) : base(options, databaseOptions)
    {
    }

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    {
        optionsBuilder
            .UseSqlServer(DatabaseOptions.Value["Files"]!.ConnectionString, (option) =>
            {
                
            });
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        modelBuilder.Initialize();

        modelBuilder.AddPropertyComments();

        modelBuilder.ApplyIndexAttributes();

        modelBuilder.Seed();

        // 配置枚举转换
        modelBuilder.ConfigureEnumToIntConversions();
    }
}