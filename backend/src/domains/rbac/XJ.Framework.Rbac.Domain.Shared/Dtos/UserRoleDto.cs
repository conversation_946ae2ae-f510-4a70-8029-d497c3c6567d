
namespace XJ.Framework.Rbac.Domain.Shared.Dtos;

/// <summary>
/// UserRole DTO
/// </summary>
public class UserRoleDto : BaseDto<long>
{
    /// <summary>
    /// 用户ID
    /// </summary>
    public long UserId { get; set; }

    /// <summary>
    /// 角色ID
    /// </summary>
    public long RoleId { get; set; }

    /// <summary>
    /// 组织ID（岗位角色必填）
    /// </summary>
    public long? OrganizationId { get; set; }

    /// <summary>
    /// 岗位ID（岗位角色必填）
    /// </summary>
    public long? PositionId { get; set; }

} 