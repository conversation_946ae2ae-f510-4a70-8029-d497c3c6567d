using Microsoft.Extensions.Logging;
using System;
using System.Text.Json;
using System.Threading.Tasks;
using XJ.Framework.Library.Common.Abstraction.Extensions;
using XJ.Framework.Library.Domain.Id;
using XJ.Framework.Library.Domain.Shared.Enums;
using XJ.Framework.Library.Logging.Abstraction.DI;
using XJ.Framework.Messaging.Domain.Shared.Dtos;
using XJ.Framework.Rbac.Domain;
using XJ.Framework.Rbac.Domain.Shared.Dtos.Auth;
using XJ.Framework.Rbac.Domain.Shared.Enums;
using ChangePasswordRequestDto = XJ.Framework.Rbac.Domain.Shared.Dtos.Auth.ChangePasswordRequestDto;
using ForgotPasswordRequestDto = XJ.Framework.Rbac.Domain.Shared.Dtos.Auth.ForgotPasswordRequestDto;
using ResetPasswordRequestDto = XJ.Framework.Rbac.Domain.Shared.Dtos.Auth.ResetPasswordRequestDto;

namespace XJ.Framework.Rbac.Application.Services;

public partial class AuthService
{
    /// <summary>
    /// 用户注册
    /// </summary>
    public async Task<RegisterResultDto> RegisterAsync(RegisterRequestDto request)
    {
        var createRequest = new UserOperationDto()
        {
            Username = request.Username,
            RealName = request.RealName,
            Email = request.Email,
            Mobile = request.PhoneNumber,
            // Avatar = request.,
            UserType = UserType.External,
            Status = UserStatus.Enabled
        };
        var userKey = await _userService.CreateUserAsync(createRequest, request.Password);
        return new RegisterResultDto()
        {
            UserId = userKey.ToString(),
            Username = request.Username
        };
    }


    public async Task<bool> ChangePasswordAsync(ChangePasswordRequestDto request)
    {
        var user = await _userRepository.GetAsync(_currentUserContext.GetCurrentUserId()!.Value);
        if (user == null)
        {
            throw new ValidationException("用户不存在/ User does not exist");
        }

        // 验证原密码
        if (!PasswordHelper.VerifyPassword(request.OldPassword, user.PasswordSalt, user.PasswordHash))
        {
            throw new ValidationException("当前密码错误/ Current password is incorrect");
        }

        // 更新密码
        var salt = PasswordHelper.GenerateSalt();
        var passwordHash = PasswordHelper.HashPassword(request.NewPassword, salt);

        user.PasswordHash = passwordHash;
        user.PasswordSalt = salt;

        await _userRepository.UpdateAsync(user);

        // 更新密码后，注销该用户的所有令牌
        await _tokenService.RevokeAllUserTokensAsync(user.Key);

        return true;
    }

    public async Task<bool> ResetPasswordAsync(ResetPasswordRequestDto request)
    {
        try
        {
            // 根据重置方式选择不同的验证逻辑
            if (!string.IsNullOrEmpty(request.Username) && !string.IsNullOrEmpty(request.PhoneNumber) &&
                !string.IsNullOrEmpty(request.Code))
            {
                // 通过手机验证码重置密码
                var user = await _userRepository.FindByUsernameAsync(request.Username);
                if (user == null)
                {
                    throw new ValidationException("找不到该用户/ User not found");
                }

                // 验证手机号是否匹配
                if (!string.Equals(user.Mobile, request.PhoneNumber, StringComparison.OrdinalIgnoreCase))
                {
                    throw new ValidationException("手机号不匹配/ Phone number does not match");
                }

                // 验证手机验证码
                if (!await _tokenService.ValidateMobileTokenAsync(user.Key, request.Code))
                {
                    throw new ValidationException("验证码错误或已过期/ Verification code is incorrect or expired");
                }

                // 更新密码
                var salt = PasswordHelper.GenerateSalt();
                var passwordHash = PasswordHelper.HashPassword(request.NewPassword, salt);

                user.PasswordHash = passwordHash;
                user.PasswordSalt = salt;
                user.PasswordErrorCount = 0;
                user.PasswordUpdateTime = DateTimeOffset.UtcNow;

                await _userRepository.UpdateAsync(user);

                // 更新密码后，注销该用户的所有令牌
                await _tokenService.RevokeAllUserTokensAsync(user.Key);

                _logger.LogInformation("用户 {UserId} 通过手机验证码重置密码成功", user.Key);

                return true;
            }
            else if (!string.IsNullOrEmpty(request.Username) && !string.IsNullOrEmpty(request.Email) &&
                     !string.IsNullOrEmpty(request.Code))
            {
                // 通过邮箱验证码重置密码
                var user = await _userRepository.FindByUsernameAsync(request.Username);
                if (user == null)
                {
                    throw new ValidationException("找不到该用户/ User not found");
                }

                // 验证邮箱是否匹配
                if (!string.Equals(user.Email, request.Email, StringComparison.OrdinalIgnoreCase))
                {
                    throw new ValidationException("邮箱不匹配/ Email does not match");
                }

                // 验证邮箱验证码
                if (!await _tokenService.ValidateEmailTokenAsync(user.Key, request.Code))
                {
                    throw new ValidationException("验证码错误或已过期/ Verification code is incorrect or expired");
                }

                // 更新密码
                var salt = PasswordHelper.GenerateSalt();
                var passwordHash = PasswordHelper.HashPassword(request.NewPassword, salt);

                user.PasswordHash = passwordHash;
                user.PasswordSalt = salt;
                user.PasswordErrorCount = 0;
                user.PasswordUpdateTime = DateTimeOffset.UtcNow;

                await _userRepository.UpdateAsync(user);

                // 更新密码后，注销该用户的所有令牌
                await _tokenService.RevokeAllUserTokensAsync(user.Key);

                _logger.LogInformation("用户 {UserId} 通过邮箱验证码重置密码成功", user.Key);

                return true;
            }
            else
            {
                throw new ValidationException("无效的重置密码请求/ Invalid reset password request");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "重置密码失败");
            throw;
        }
    }


    public async Task<bool> ForgotPasswordAsync(ForgotPasswordRequestDto request)
    {
        try
        {
            // 1. 校验图片验证码
            if (!string.IsNullOrEmpty(request.CaptchaId) && !string.IsNullOrEmpty(request.CaptchaCode))
            {
                if (!await _captchaService.ValidateCaptchaAsync(request.CaptchaId, request.CaptchaCode))
                {
                    throw new ValidationException("图片验证码不正确/ Image captcha code is incorrect");
                }
            }
            else
            {
                throw new ValidationException("图片验证码不能为空/ Image captcha code cannot be empty");
            }

            // 2. 根据用户名查找用户
            var user = await _userRepository.FindByUsernameAsync(request.Username);

            if (user == null)
            {
                throw new ValidationException("找不到该用户/ User not found");
            }

            // 3. 验证用户是否有对应的验证方式
            if (request.VerifyMethod == "email" && string.IsNullOrEmpty(user.Email))
            {
                throw new ValidationException("该用户未绑定邮箱/ This user has not bound an email address");
            }
            else if (request.VerifyMethod == "phone" && string.IsNullOrEmpty(user.Mobile))
            {
                throw new ValidationException("该用户未绑定手机号/ This user has not bound a phone number");
            }

            // 4. 限流检查：同一用户名在指定时间内只能请求一次
            var userLimitKey = $"forgot_password_limit:user:{request.Username}";
            if (await _cache.ExistsAsync(userLimitKey))
            {
                throw new ValidationException("请求过于频繁，请稍后再试/ Request too frequent, please try again later");
            }

            // 全局限流检查
            var globalLimitKey = "forgot_password_limit:global";
            var globalLimitCount = await _cache.GetAsync<int?>(globalLimitKey) ?? 0;

            // 全局每分钟最多允许20次请求
            if (globalLimitCount >= 20)
            {
                throw new ValidationException("系统繁忙，请稍后再试/ System is busy, please try again later");
            }

            // 增加全局计数器
            await _cache.SetAsync(globalLimitKey, globalLimitCount + 1, TimeSpan.FromMinutes(1));

            // 5. 设置限流标记，30分钟内不能再次请求
            await _cache.SetAsync<string>(userLimitKey, "1", TimeSpan.FromMinutes(30));

            int minutes = 5;

            // 6. 根据验证方式发送重置密码邮件或短信
            if (request.VerifyMethod == "email")
            {
                // 生成邮箱验证码
                var code = await _tokenService.GenerateEmailTokenAsync(user.Key, minutes);

                // 发送重置密码邮件
                _logger.LoggingInformation("reset-password-message", "为用户 {UserId} 生成邮箱验证码并发送到邮箱 {Email}: {Code}",
                    user.Key, user.Email!, code);
                // await _emailService.SendPasswordResetEmailAsync(user.Email, code);

                await _messagingApplicationApiClient.SendAsync(new MessageSendRequestDto()
                {
                    Target = user.Email!,
                    TemplateCode = "EMAIL_RESET_PASSWORD_VERIFICATION_CODE",
                    VariablesJson = JsonSerializer.Serialize(new { CODE = code, MINUTES = minutes }),
                    MessageType = "email",
                });
            }
            else if (request.VerifyMethod == "phone")
            {
                // 生成6位数字验证码
                var code = await _tokenService.GenerateMobileTokenAsync(user.Key, minutes);

                // 发送重置密码短信
                _logger.LoggingInformation("reset-password-message", "为用户 {UserId} 生成6位验证码并发送到手机 {Mobile}: {Code}",
                    user.Key, user.Mobile!, code);
                
                await _messagingApplicationApiClient.SendAsync(new MessageSendRequestDto()
                {
                    Target = user.Email!,
                    TemplateCode = "SMS_RESET_PASSWORD_VERIFICATION_CODE",
                    VariablesJson = JsonSerializer.Serialize(new { code = code, minutes = minutes }),
                    MessageType = "sms",
                });
                // await _smsService.SendPasswordResetSmsAsync(user.Mobile, code);
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "忘记密码处理失败: {Username}/ Operation failed. Please try again.", request.Username);
            throw;
        }
    }


    /// <summary>
    /// 更新用户最后登录信息
    /// </summary>
    public async Task<bool> UpdateLastLoginInfoAsync(long userId, string? ip, string? deviceInfo)
    {
        return await _userRepository.UpdateLastLoginInfoAsync(userId, ip, deviceInfo);
    }

    /// <summary>
    /// 重置密码错误次数
    /// </summary>
    public async Task<bool> ResetPasswordErrorCountAsync(long userId)
    {
        return await _userRepository.ResetPasswordErrorCountAsync(userId);
    }

    /// <summary>
    /// 增加密码错误次数
    /// </summary>
    public async Task<int> IncrementPasswordErrorCountAsync(long userId)
    {
        return await _userRepository.IncrementPasswordErrorCountAsync(userId);
    }

    /// <summary>
    /// 更新用户状态
    /// </summary>
    public async Task<bool> UpdateUserStatusAsync(long userId, UserStatus status)
    {
        var user = await _userRepository.GetAsync(userId);
        if (user!.UserType == UserType.System && status == UserStatus.Disabled)
        {
            throw new ValidationException("系统用户不能被禁用/ System users cannot be disabled");
        }

        return await _userRepository.UpdateStatusAsync(userId, status);
    }

    /// <summary>
    /// 修改用户基本信息
    /// </summary>
    public async Task<bool> UpdateProfileAsync(UpdateProfileRequestDto request)
    {
        var userId = _currentUserContext.GetCurrentUserId();
        if (userId == null)
        {
            throw new ValidationException("用户未登录/ User not logged in");
        }

        var user = await _userRepository.GetAsync(userId.Value);
        if (user == null)
        {
            throw new ValidationException("用户不存在/ User does not exist");
        }

        var userExt = await _userExtRepository.GetAsync(x => x.UserId == userId.Value);

        if (!string.IsNullOrEmpty(request.RealName))
        {
            user.RealName = request.RealName;
        }

        if (request.Avatar != null)
        {
            user.Avatar = JsonSerializer.Serialize(request.Avatar);
        }

        bool hasExt = userExt == null;
        if (hasExt)
        {
            userExt = new UserExtEntity()
            {
                Key = IdGenerator.NextId(),
                UserId = userId.Value
            };
        }

        if (request.ContactAddress != null)
        {
            userExt.ContactAddress = request.ContactAddress;
        }

        if (request.Country != null)
        {
            userExt.Country = request.Country;
        }

        if (request.Gender != null)
        {
            userExt.Gender = request.Gender;
        }

        if (request.Telephone != null)
        {
            userExt.Telephone = request.Telephone;
        }

        if (request.Unit != null)
        {
            userExt.Unit = request.Unit;
        }

        // 可扩展更多字段

        await _userRepository.UpdateAsync(user);
        if (hasExt)
            await _userExtRepository.InsertAsync(userExt);
        else
            await _userExtRepository.UpdateAsync(userExt);
        return true;
    }

    public async Task<bool> SendEmailConfirmationCodeAsync(SendEmailConfirmationCodeRequestDto request)
    {
        // 1. 校验图片验证码
        if (!await _captchaService.ValidateCaptchaAsync(request.CaptchaId, request.CaptchaCode))
        {
            throw new ValidationException("图片验证码不正确/ Image captcha code is incorrect");
        }

        // 2. 限流：5分钟只能请求一次
        var limitKey = $"email_confirm_limit:{request.Email}";
        if (await _cache.ExistsAsync(limitKey))
        {
            throw new ValidationException("请求过于频繁，请稍后再试/ Request too frequent, please try again later");
        }

        // 3. 查找用户
        var user = await _userRepository.FindByEmailAsync(request.Email);
        if (user == null)
        {
            throw new ValidationException("该邮箱未注册/ This email is not registered");
        }

        var minutes = 5;
        // 4. 生成验证码
        var code = await _tokenService.GenerateEmailTokenAsync(user.Key, minutes);

        // 5. 设置限流标记
        await _cache.SetAsync<string>(limitKey, "1", TimeSpan.FromMinutes(minutes));

        // 6. 发送邮件（实际应调用邮件服务，这里日志模拟）
        _logger.LoggingInformation("email-confirm", "[邮箱验证码] 向 {Email} 发送验证码: {Code}", request.Email, code);
        // await _emailService.SendEmailAsync(request.Email, "邮箱验证", $"您的验证码是：{code}");

        await _messagingApplicationApiClient.SendAsync(new MessageSendRequestDto()
        {
            Target = user.Email!,
            TemplateCode = "EMAIL_CONFIRM_EMAIL_VERIFICATION_CODE",
            VariablesJson = JsonSerializer.Serialize(new { CODE = code, MINUTES = minutes }),
            MessageType = "email",
        });
        return true;
    }

    public async Task<bool> SendPhoneConfirmationCodeAsync(SendPhoneConfirmationCodeRequestDto request)
    {
        // 1. 校验图片验证码
        if (!await _captchaService.ValidateCaptchaAsync(request.CaptchaId, request.CaptchaCode))
        {
            throw new ValidationException("图片验证码不正确/ Image captcha code is incorrect");
        }

        // 2. 限流：5分钟只能请求一次
        var limitKey = $"phone_confirm_limit:{request.PhoneNumber}";
        if (await _cache.ExistsAsync(limitKey))
        {
            throw new ValidationException("请求过于频繁，请稍后再试/ Request too frequent, please try again later");
        }

        // 3. 查找用户
        var user = await _userRepository.FindByPhoneNumberAsync(request.PhoneNumber);
        if (user == null)
        {
            throw new ValidationException("该手机号未注册/ This phone number is not registered");
        }

        var minutes = 5;

        // 4. 生成验证码
        var code = await _tokenService.GenerateMobileTokenAsync(user.Key, minutes);

        // 5. 设置限流标记
        await _cache.SetAsync<string>(limitKey, "1", TimeSpan.FromMinutes(minutes));

        // 6. 发送短信（实际应调用短信服务，这里日志模拟）
        _logger.LoggingInformation("mobile-confirm", "[手机验证码] 向 {PhoneNumber} 发送验证码: {Code}", request.PhoneNumber,
            code);
        // await _smsService.SendAsync(request.PhoneNumber, $"您的验证码是：{code}");

        return true;
    }

    /// <summary>
    /// 确认邮箱
    /// </summary>
    public async Task<bool> ConfirmEmailAsync(ConfirmEmailRequestDto request)
    {
        var userId = _currentUserContext.GetCurrentUserId();
        if (userId == null)
        {
            throw new ValidationException("用户未登录/ User not logged in");
        }

        var user = await _userRepository.GetAsync(userId.Value);
        if (user == null)
        {
            throw new ValidationException("用户不存在/ User does not exist");
        }

        if (!string.Equals(user.Email, request.Email, StringComparison.OrdinalIgnoreCase))
        {
            throw new ValidationException("邮箱不匹配/ Email does not match");
        }

        // 验证码校验
        if (!await _tokenService.ValidateEmailTokenAsync(user.Key, request.Code))
        {
            throw new ValidationException("验证码错误或已过期/ Verification code is incorrect or expired");
        }

        user.EmailConfirmed = true;
        user.EmailConfirmedTime = DateTimeOffset.UtcNow;
        await _userRepository.UpdateAsync(user);
        return true;
    }

    /// <summary>
    /// 确认手机号
    /// </summary>
    public async Task<bool> ConfirmPhoneNumberAsync(ConfirmPhoneNumberRequestDto request)
    {
        var userId = _currentUserContext.GetCurrentUserId();
        if (userId == null)
        {
            throw new ValidationException("用户未登录/ User not logged in");
        }

        var user = await _userRepository.GetAsync(userId.Value);
        if (user == null)
        {
            throw new ValidationException("用户不存在/ User does not exist");
        }

        if (!string.Equals(user.Mobile, request.PhoneNumber, StringComparison.OrdinalIgnoreCase))
        {
            throw new ValidationException("手机号不匹配/ Phone number does not match");
        }

        // 验证码校验
        if (!await _tokenService.ValidateMobileTokenAsync(user.Key, request.Code))
        {
            throw new ValidationException("验证码错误或已过期/ Verification code is incorrect or expired");
        }

        user.MobileConfirmed = true;
        user.MobileConfirmedTime = DateTimeOffset.UtcNow;
        await _userRepository.UpdateAsync(user);
        return true;
    }

    // public async Task<bool> ValidateDeviceIdAsync(string deviceId)
    // {
    //     return await _authProvider.ValidateUserDeviceIdAsync(deviceId, _currentUserContext.GetAccessToken());
    // }
}
