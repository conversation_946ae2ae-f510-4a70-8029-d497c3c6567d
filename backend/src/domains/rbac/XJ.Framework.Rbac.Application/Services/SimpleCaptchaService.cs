using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Text;
using XJ.Framework.Library.Application.Contract.Interfaces;
using XJ.Framework.Library.Application.Options;
using XJ.Framework.Library.Cache.Abstraction;
using XJ.Framework.Library.Image;

namespace XJ.Framework.Rbac.Application.Services;

/// <summary>
/// 简单验证码服务
/// </summary>
public class SimpleCaptchaService : ICaptchaService
{
    private readonly ICache _cache;
    private readonly ILogger<SimpleCaptchaService> _logger;
    private readonly CaptchaOptions _options;
    private readonly Random _random = new();
    private const int length = 4; // 验证码长度

    /// <summary>
    /// 构造函数
    /// </summary>
    public SimpleCaptchaService(
        ICache cache,
        IOptions<CaptchaOptions> options,
        ILogger<SimpleCaptchaService> logger)
    {
        _cache = cache;
        _options = options.Value;
        _logger = logger;
    }

    public async Task<int> GetExpirationInSecondsAsync()
    {
        return await Task.FromResult(_options.ExpirationInSeconds);
    }

    public async Task<byte[]> GenerateCaptchaImageAsync(string captchaCode)
    {
        // 生成简单的文本验证码图片
        var captchaHelper = new CaptchaHelper(captchaCode);
        return await Task.FromResult(captchaHelper.GetVerifyCodeImage());
    }


    private string GetVerifyCodeText(bool addUpperLetter, bool addLowerLetter)
    {
        var verifyCodeText = string.Empty;

        var objStringBuilder = new StringBuilder();

        //加入数字1-9
        for (var i = 1; i <= 9; i++)
        {
            objStringBuilder.Append(i.ToString());
        }

        //加入大写字母A-Z，不包括O
        if (addUpperLetter)
        {
            var temp = ' ';

            for (var i = 0; i < 26; i++)
            {
                temp = Convert.ToChar(i + 65);

                //如果生成的字母不是'O'
                if (!temp.Equals('O'))
                {
                    objStringBuilder.Append(temp);
                }
            }
        }

        //加入小写字母a-z，不包括o
        if (addLowerLetter)
        {
            var temp = ' ';

            for (var i = 0; i < 26; i++)
            {
                temp = Convert.ToChar(i + 97);

                //如果生成的字母不是'o'
                if (!temp.Equals('o'))
                {
                    objStringBuilder.Append(temp);
                }
            }
        }

        //生成验证码字符串
        {
            var index = 0;

            for (var i = 0; i < length; i++)
            {
                index = _random.Next(0, objStringBuilder.Length);

                verifyCodeText += objStringBuilder[index];

                objStringBuilder.Remove(index, 1);
            }
        }
        return verifyCodeText;
    }

    public KeyValuePair<string, string> GenerateCaptchaContent()
    {
        var result = GetVerifyCodeText(true, true);
        return new KeyValuePair<string, string>(result, result);
    }


    /// <summary>
    /// 存储验证码结果到缓存
    /// </summary>
    public async Task StoreCaptchaAsync(string captchaId, string captchaContent)
    {
        // 设置验证码缓存，过期时间为配置中的秒数
        await _cache.SetAsync(
            GetCacheKey(captchaId),
            captchaContent,
            TimeSpan.FromSeconds(_options.ExpirationInSeconds)
        );
    }

    public async Task<string> GetCaptchaAsync(string captchaId)
    {
        // 从缓存中获取验证码
        var captchaContent = await _cache.GetAsync<string>(GetCacheKey(captchaId));

        if (string.IsNullOrEmpty(captchaContent))
        {
            throw new Exception("验证码不存在或已过期/ The captcha does not exist or has expired.");
        }

        return captchaContent;
    }

    public async Task RemoveCaptchaAsync(string captchaId)
    {
        await _cache.RemoveAsync(GetCacheKey(captchaId));
    }


    /// <summary>
    /// 获取缓存键
    /// </summary>
    private static string GetCacheKey(string captchaId)
    {
        return $"Captcha:{captchaId}";
    }
}