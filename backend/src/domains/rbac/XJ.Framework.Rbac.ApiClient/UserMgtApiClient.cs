using XJ.Framework.Library.Common.Abstraction.Contexts;
using XJ.Framework.Library.Domain.Shared.Interfaces;
using XJ.Framework.Rbac.Domain.Shared.Dtos;
using HttpMethod = System.Net.Http.HttpMethod;

namespace XJ.Framework.Rbac.ApiClient;

public class UserMgtApiClient : BaseApiClient
{
    private readonly string _baseUrl;

    public UserMgtApiClient(HttpClient httpClient, IOptions<EndpointOption> endpointOption, ILogger<BaseApiClient> logger,
        IAuthInfoGetter authInfoGetter, IContextContainer contextContainer, ICurrentUserContext currentUserContext)
        : base(httpClient, logger, authInfoGetter, contextContainer, currentUserContext)
    {
        _baseUrl = endpointOption.Value["RbacMgt"]!.Url.TrimEnd('/');
    }

    public async Task<List<UserDto>> GetUsersByCommonRoleCodeAsync(string roleCode)
    {
        return await InternalGetAsync<List<UserDto>>($"{_baseUrl}/user/role-in-common/{roleCode}");
    }
    
    
    public async Task<List<OrganizationUserDto>> GetManagedPositionUsersAsync(string positionCode)
    {
        return await InternalGetAsync<List<OrganizationUserDto>>($"{_baseUrl}/user/position-in-user-managed/{positionCode}");
    }
}
