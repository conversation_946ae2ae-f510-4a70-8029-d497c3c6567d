using System.Threading.Tasks;
using XJ.Framework.Library.Domain.Shared.Dtos;
using XJ.Framework.Rbac.Domain.Shared.Enums;

namespace XJ.Framework.Rbac.Application.Contract.Interfaces;

/// <summary>
/// User 服务接口
/// </summary>
public interface IUserExtService :
    IAppService<long, UserExtDto, UserExtQueryCriteria>,
    IEditableAppService<long, UserExtOperationDto>
{
    Task<bool> RegisterUserExt(RegisterOperationDto request,long userkey);

    Task<UserExtDto> GetUserExtAsync(long userkey);
}