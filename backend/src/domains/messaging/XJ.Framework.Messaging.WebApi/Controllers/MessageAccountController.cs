
namespace XJ.Framework.Messaging.WebApi.Controllers;

/// <summary>
/// MessageAccount 控制器
/// </summary>

public class MessageAccountController : BaseAppController<long, MessageAccountDto, IMessageAccountService, MessageAccountQueryCriteria>
{
    public MessageAccountController(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }
    
    

    [HttpGet("{id:long}")]
    public async Task<MessageAccountDto?> GetAsync(long id)
    {
        return await Service.GetByIdAsync(id);
    }

    [HttpGet("page")]
    public async Task<PageDtoData<long, MessageAccountDto>> GetPageAsync([FromQuery] PagedQueryCriteria<MessageAccountQueryCriteria> criteria)
    {
        return await Service.GetPageAsync(criteria);
    }

    [HttpGet]
    public async Task<IEnumerable<MessageAccountDto>> GetListAsync([FromQuery] MessageAccountQueryCriteria criteria)
    {
        return await Service.GetListAsync(criteria);
    }
}
