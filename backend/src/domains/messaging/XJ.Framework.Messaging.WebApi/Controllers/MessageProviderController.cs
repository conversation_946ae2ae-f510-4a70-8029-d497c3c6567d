
namespace XJ.Framework.Messaging.WebApi.Controllers;

/// <summary>
/// MessageProvider 控制器
/// </summary>

public class MessageProviderController : BaseAppController<long, MessageProviderDto, IMessageProviderService, MessageProviderQueryCriteria>
{
    public MessageProviderController(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }
    
    

    [HttpGet("{id:long}")]
    public async Task<MessageProviderDto?> GetAsync(long id)
    {
        return await Service.GetByIdAsync(id);
    }

    [HttpGet("page")]
    public async Task<PageDtoData<long, MessageProviderDto>> GetPageAsync([FromQuery] PagedQueryCriteria<MessageProviderQueryCriteria> criteria)
    {
        return await Service.GetPageAsync(criteria);
    }

    [HttpGet]
    public async Task<IEnumerable<MessageProviderDto>> GetListAsync([FromQuery] MessageProviderQueryCriteria criteria)
    {
        return await Service.GetListAsync(criteria);
    }
}
