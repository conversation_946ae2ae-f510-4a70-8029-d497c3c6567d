
namespace XJ.Framework.Messaging.WebApi.Controllers;

/// <summary>
/// MessageSendArchive 控制器
/// </summary>

public class MessageSendArchiveController : BaseAppController<long, MessageSendArchiveDto, IMessageSendArchiveService, MessageSendArchiveQueryCriteria>
{
    public MessageSendArchiveController(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }
    
    

    [HttpGet("{id:long}")]
    public async Task<MessageSendArchiveDto?> GetAsync(long id)
    {
        return await Service.GetByIdAsync(id);
    }

    [HttpGet("page")]
    public async Task<PageDtoData<long, MessageSendArchiveDto>> GetPageAsync([FromQuery] PagedQueryCriteria<MessageSendArchiveQueryCriteria> criteria)
    {
        return await Service.GetPageAsync(criteria);
    }

    [HttpGet]
    public async Task<IEnumerable<MessageSendArchiveDto>> GetListAsync([FromQuery] MessageSendArchiveQueryCriteria criteria)
    {
        return await Service.GetListAsync(criteria);
    }
}
