
namespace XJ.Framework.Messaging.WebApi.Controllers;

/// <summary>
/// MessageAccountUsage 控制器
/// </summary>

public class MessageAccountUsageController : BaseAppController<long, MessageAccountUsageDto, IMessageAccountUsageService, MessageAccountUsageQueryCriteria>
{
    public MessageAccountUsageController(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }
    
    

    [HttpGet("{id:long}")]
    public async Task<MessageAccountUsageDto?> GetAsync(long id)
    {
        return await Service.GetByIdAsync(id);
    }

    [HttpGet("page")]
    public async Task<PageDtoData<long, MessageAccountUsageDto>> GetPageAsync([FromQuery] PagedQueryCriteria<MessageAccountUsageQueryCriteria> criteria)
    {
        return await Service.GetPageAsync(criteria);
    }

    [HttpGet]
    public async Task<IEnumerable<MessageAccountUsageDto>> GetListAsync([FromQuery] MessageAccountUsageQueryCriteria criteria)
    {
        return await Service.GetListAsync(criteria);
    }
}
