
namespace XJ.Framework.Messaging.WebApi.Controllers;

/// <summary>
/// MessageProviderDefault 控制器
/// </summary>

public class MessageProviderDefaultController : BaseAppController<long, MessageProviderDefaultDto, IMessageProviderDefaultService, MessageProviderDefaultQueryCriteria>
{
    public MessageProviderDefaultController(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }
    
    

    [HttpGet("{id:long}")]
    public async Task<MessageProviderDefaultDto?> GetAsync(long id)
    {
        return await Service.GetByIdAsync(id);
    }

    [HttpGet("page")]
    public async Task<PageDtoData<long, MessageProviderDefaultDto>> GetPageAsync([FromQuery] PagedQueryCriteria<MessageProviderDefaultQueryCriteria> criteria)
    {
        return await Service.GetPageAsync(criteria);
    }

    [HttpGet]
    public async Task<IEnumerable<MessageProviderDefaultDto>> GetListAsync([FromQuery] MessageProviderDefaultQueryCriteria criteria)
    {
        return await Service.GetListAsync(criteria);
    }
}
