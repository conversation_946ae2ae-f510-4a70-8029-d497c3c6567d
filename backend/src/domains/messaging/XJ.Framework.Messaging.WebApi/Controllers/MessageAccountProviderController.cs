
namespace XJ.Framework.Messaging.WebApi.Controllers;

/// <summary>
/// MessageAccountProvider 控制器
/// </summary>

public class MessageAccountProviderController : BaseAppController<long, MessageAccountProviderDto, IMessageAccountProviderService, MessageAccountProviderQueryCriteria>
{
    public MessageAccountProviderController(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }
    
    

    [HttpGet("{id:long}")]
    public async Task<MessageAccountProviderDto?> GetAsync(long id)
    {
        return await Service.GetByIdAsync(id);
    }

    [HttpGet("page")]
    public async Task<PageDtoData<long, MessageAccountProviderDto>> GetPageAsync([FromQuery] PagedQueryCriteria<MessageAccountProviderQueryCriteria> criteria)
    {
        return await Service.GetPageAsync(criteria);
    }

    [HttpGet]
    public async Task<IEnumerable<MessageAccountProviderDto>> GetListAsync([FromQuery] MessageAccountProviderQueryCriteria criteria)
    {
        return await Service.GetListAsync(criteria);
    }
}
