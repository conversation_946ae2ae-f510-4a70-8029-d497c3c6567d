
namespace XJ.Framework.Messaging.WebApi.Controllers;

/// <summary>
/// MessageTemplate 控制器
/// </summary>

public class MessageTemplateController : BaseAppController<long, MessageTemplateDto, IMessageTemplateService, MessageTemplateQueryCriteria>
{
    public MessageTemplateController(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }
    
    

    [HttpGet("{id:long}")]
    public async Task<MessageTemplateDto?> GetAsync(long id)
    {
        return await Service.GetByIdAsync(id);
    }

    [HttpGet("page")]
    public async Task<PageDtoData<long, MessageTemplateDto>> GetPageAsync([FromQuery] PagedQueryCriteria<MessageTemplateQueryCriteria> criteria)
    {
        return await Service.GetPageAsync(criteria);
    }

    [HttpGet]
    public async Task<IEnumerable<MessageTemplateDto>> GetListAsync([FromQuery] MessageTemplateQueryCriteria criteria)
    {
        return await Service.GetListAsync(criteria);
    }
}
