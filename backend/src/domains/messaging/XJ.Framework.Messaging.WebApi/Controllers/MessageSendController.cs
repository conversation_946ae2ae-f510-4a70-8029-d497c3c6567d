
using XJ.Framework.Messaging.Domain.Shared.Dtos;

namespace XJ.Framework.Messaging.WebApi.Controllers;

/// <summary>
/// MessageSend 控制器
/// </summary>

public class MessageSendController : BaseAppController<long, MessageSendDto, IMessageSendService, MessageSendQueryCriteria>
{
    public MessageSendController(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }

    /// <summary>
    /// 发送消息
    /// </summary>
    /// <param name="request">消息发送请求</param>
    /// <returns>发送结果</returns>
    [HttpPost("send")]
    public async Task<MessageSendResultDto> SendMessageAsync([FromBody] MessageSendRequestDto request)
    {
        return await Service.SendMessageAsync(request);
    }

    /// <summary>
    /// 获取发送状态
    /// </summary>
    /// <param name="sendId">发送记录ID</param>
    /// <returns>发送状态</returns>
    [HttpGet("status/{sendId:long}")]
    public async Task<MessageSendResultDto?> GetSendStatusAsync(long sendId)
    {
        return await Service.GetSendStatusAsync(sendId);
    }

    [HttpGet("{id:long}")]
    public async Task<MessageSendDto?> GetAsync(long id)
    {
        return await Service.GetByIdAsync(id);
    }

    [HttpGet("page")]
    public async Task<PageDtoData<long, MessageSendDto>> GetPageAsync([FromQuery] PagedQueryCriteria<MessageSendQueryCriteria> criteria)
    {
        return await Service.GetPageAsync(criteria);
    }

    [HttpGet]
    public async Task<IEnumerable<MessageSendDto>> GetListAsync([FromQuery] MessageSendQueryCriteria criteria)
    {
        return await Service.GetListAsync(criteria);
    }
}
