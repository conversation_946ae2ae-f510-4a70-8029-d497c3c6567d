
namespace XJ.Framework.Messaging.WebApi.Controllers;

/// <summary>
/// MessageSend 控制器
/// </summary>

public class MessageSendController : BaseAppController<long, MessageSendDto, IMessageSendService, MessageSendQueryCriteria>
{
    public MessageSendController(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }
    
    

    [HttpGet("{id:long}")]
    public async Task<MessageSendDto?> GetAsync(long id)
    {
        return await Service.GetByIdAsync(id);
    }

    [HttpGet("page")]
    public async Task<PageDtoData<long, MessageSendDto>> GetPageAsync([FromQuery] PagedQueryCriteria<MessageSendQueryCriteria> criteria)
    {
        return await Service.GetPageAsync(criteria);
    }

    [HttpGet]
    public async Task<IEnumerable<MessageSendDto>> GetListAsync([FromQuery] MessageSendQueryCriteria criteria)
    {
        return await Service.GetListAsync(criteria);
    }
}
