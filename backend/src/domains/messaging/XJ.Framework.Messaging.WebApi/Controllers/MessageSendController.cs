using XJ.Framework.Messaging.Domain.Shared.Dtos;

namespace XJ.Framework.Messaging.WebApi.Controllers;

/// <summary>
/// MessageSend 控制器
/// </summary>
public class
    MessageSendController : BaseAppController<long, MessageSendDto, IMessageSendService, MessageSendQueryCriteria>
{
    public MessageSendController(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }

    /// <summary>
    /// 发送消息
    /// </summary>
    /// <param name="request">消息发送请求</param>
    /// <returns>发送结果</returns>
    [HttpPost("send")]
    public async Task<MessageSendResultDto> SendMessageAsync([FromBody] MessageSendRequestDto request)
    {
        return await Service.SendMessageAsync(request);
    }

    /// <summary>
    /// 获取发送状态
    /// </summary>
    /// <param name="sendId">发送记录ID</param>
    /// <returns>发送状态</returns>
    [HttpGet("status/{sendId:long}")]
    public async Task<MessageSendResultDto?> GetSendStatusAsync(long sendId)
    {
        return await Service.GetSendStatusAsync(sendId);
    }
}
