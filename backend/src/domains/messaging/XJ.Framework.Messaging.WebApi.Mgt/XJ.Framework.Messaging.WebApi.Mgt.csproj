<Project Sdk="Microsoft.NET.Sdk.Web">

    <Import Project="..\..\..\..\Common.Secrets.props"/>
    <Import Project="..\..\..\..\Common.props"/>

    <ItemGroup>
        <ProjectReference Include="..\..\..\shared\XJ.Framework.Library.WebApi.Mgt\XJ.Framework.Library.WebApi.Mgt.csproj" />
        <ProjectReference Include="..\..\..\shared\XJ.Framework.Library.WebApi\XJ.Framework.Library.WebApi.csproj"/>
        <ProjectReference Include="..\XJ.Framework.Messaging.Application\XJ.Framework.Messaging.Application.csproj"/>
        <ProjectReference Include="..\XJ.Framework.Messaging.Domain.Shared\XJ.Framework.Messaging.Domain.Shared.csproj"/>
    </ItemGroup>

    <ItemGroup>
        <Folder Include="Controllers\" />
    </ItemGroup>

    <ItemGroup>
      <PackageReference Include="Microsoft.Data.SqlClient" />
    </ItemGroup>

</Project> 