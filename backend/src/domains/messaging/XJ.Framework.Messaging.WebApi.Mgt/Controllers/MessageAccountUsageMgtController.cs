namespace XJ.Framework.Messaging.WebApi.Mgt.Controllers;

/// <summary>
/// MessageAccountUsage 控制器
/// </summary>
public class MessageAccountUsageController : BaseEditableAppController<long, MessageAccountUsageDto,
    MessageAccountUsageOperationDto, IMessageAccountUsageService, MessageAccountUsageQueryCriteria>
{
    public MessageAccountUsageController(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }


    [HttpGet("page")]
    public async Task<PageDtoData<long, MessageAccountUsageDto>> GetPageAsync(
        [FromQuery] PagedQueryCriteria<MessageAccountUsageQueryCriteria> criteria)
    {
        return await Service.GetPageAsync(criteria);
    }
}
