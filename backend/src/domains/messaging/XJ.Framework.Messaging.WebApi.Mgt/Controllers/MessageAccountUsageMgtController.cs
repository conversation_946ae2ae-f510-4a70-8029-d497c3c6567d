
namespace XJ.Framework.Messaging.WebApi.Mgt.Controllers;

/// <summary>
/// MessageAccountUsage 控制器
/// </summary>

public class MessageAccountUsageController : BaseEditableAppController<long, MessageAccountUsageDto, MessageAccountUsageOperationDto, IMessageAccountUsageService, MessageAccountUsageQueryCriteria>
{
    public MessageAccountUsageController(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }
    
    
    [UnitOfWork]
    [HttpPost]
    public async Task<bool> InsertAsync(MessageAccountUsageOperationDto dto)
    {
        return await Service.CreateAsync(dto);
    }

    [HttpGet("{id:long}")]
    public async Task<MessageAccountUsageDto?> GetAsync(long id)
    {
        return await Service.GetByIdAsync(id);
    }

    [UnitOfWork]
    [HttpPut("{id:long}")]
    public async Task<bool> UpdateAsync(long id, MessageAccountUsageOperationDto dto)
    {
        return await Service.UpdateAsync(id, dto);
    }

    [UnitOfWork]
    [HttpDelete]
    public async Task<bool> DeleteAsync(long id)
    {
        return await Service.DeleteAsync(id);
    }

    [HttpGet("page")]
    public async Task<PageDtoData<long, MessageAccountUsageDto>> GetPageAsync([FromQuery] PagedQueryCriteria<MessageAccountUsageQueryCriteria> criteria)
    {
        return await Service.GetPageAsync(criteria);
    }

    [HttpGet]
    public async Task<IEnumerable<MessageAccountUsageDto>> GetListAsync([FromQuery] MessageAccountUsageQueryCriteria criteria)
    {
        return await Service.GetListAsync(criteria);
    }
}
