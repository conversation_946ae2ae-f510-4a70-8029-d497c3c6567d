using XJ.Framework.Messaging.Domain.Shared.Dtos;

namespace XJ.Framework.Messaging.WebApi.Mgt.Controllers;

/// <summary>
/// MessageProviderDefault 管理控制器
/// </summary>
public class MessageProviderDefaultMgtController : BaseAppController<long, MessageProviderDefaultDto, IMessageProviderDefaultService, MessageProviderDefaultQueryCriteria>
{
    public MessageProviderDefaultMgtController(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }

    [HttpGet("page")]
    public async Task<PageDtoData<long, MessageProviderDefaultDto>> GetPageAsync(
        [FromQuery] PagedQueryCriteria<MessageProviderDefaultQueryCriteria> criteria)
    {
        return await Service.GetPageAsync(criteria);
    }

    [HttpGet]
    public async Task<IEnumerable<MessageProviderDefaultDto>> GetListAsync(
        [FromQuery] MessageProviderDefaultQueryCriteria criteria)
    {
        return await Service.GetListAsync(criteria);
    }

    /// <summary>
    /// 根据应用编码和消息类型获取默认服务商
    /// </summary>
    /// <param name="appCode">应用编码</param>
    /// <param name="messageType">消息类型</param>
    /// <returns>默认服务商配置</returns>
    [HttpGet("default")]
    public async Task<MessageProviderDefaultDto?> GetDefaultProviderAsync([FromQuery] string appCode,
        [FromQuery] string messageType)
    {
        return await Service.GetDefaultProviderAsync(appCode, messageType);
    }

    /// <summary>
    /// 根据应用编码获取所有默认服务商配置
    /// </summary>
    /// <param name="appCode">应用编码</param>
    /// <returns>默认服务商配置列表</returns>
    [HttpGet("by-app/{appCode}")]
    public async Task<List<MessageProviderDefaultDto>> GetByAppCodeAsync(string appCode)
    {
        return await Service.GetByAppCodeAsync(appCode);
    }

    /// <summary>
    /// 设置应用的默认服务商
    /// </summary>
    /// <param name="request">设置请求</param>
    /// <returns>设置结果</returns>
    [HttpPost("set-default")]
    public async Task<bool> SetDefaultProviderAsync([FromBody] SetDefaultProviderRequestDto request)
    {
        return await Service.SetDefaultProviderAsync(request.AppCode, request.MessageType, request.ProviderCode,
            request.Priority);
    }

    /// <summary>
    /// 删除应用的默认服务商配置
    /// </summary>
    /// <param name="appCode">应用编码</param>
    /// <param name="messageType">消息类型</param>
    /// <returns>删除结果</returns>
    [HttpDelete("remove-default")]
    public async Task<bool> RemoveDefaultProviderAsync([FromQuery] string appCode, [FromQuery] string messageType)
    {
        return await Service.RemoveDefaultProviderAsync(appCode, messageType);
    }

    /// <summary>
    /// 获取消息类型的所有默认服务商（按优先级排序）
    /// </summary>
    /// <param name="appCode">应用编码</param>
    /// <param name="messageType">消息类型</param>
    /// <returns>默认服务商列表</returns>
    [HttpGet("by-type")]
    public async Task<List<MessageProviderDefaultDto>> GetDefaultProvidersByTypeAsync([FromQuery] string appCode,
        [FromQuery] string messageType)
    {
        return await Service.GetDefaultProvidersByTypeAsync(appCode, messageType);
    }
}
