
namespace XJ.Framework.Messaging.WebApi.Controllers;

/// <summary>
/// MessageProviderDefault 控制器
/// </summary>

public class MessageProviderDefaultController : BaseEditableAppController<long, MessageProviderDefaultDto, MessageProviderDefaultOperationDto, IMessageProviderDefaultService, MessageProviderDefaultQueryCriteria>
{
    public MessageProviderDefaultController(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }
    
    
    [UnitOfWork]
    [HttpPost]
    public async Task<bool> InsertAsync(MessageProviderDefaultOperationDto dto)
    {
        return await Service.CreateAsync(dto);
    }

    [HttpGet("{id:long}")]
    public async Task<MessageProviderDefaultDto?> GetAsync(long id)
    {
        return await Service.GetByIdAsync(id);
    }

    [UnitOfWork]
    [HttpPut("{id:long}")]
    public async Task<bool> UpdateAsync(long id, MessageProviderDefaultOperationDto dto)
    {
        return await Service.UpdateAsync(id, dto);
    }

    [UnitOfWork]
    [HttpDelete]
    public async Task<bool> DeleteAsync(long id)
    {
        return await Service.DeleteAsync(id);
    }

    [HttpGet("page")]
    public async Task<PageDtoData<long, MessageProviderDefaultDto>> GetPageAsync([FromQuery] PagedQueryCriteria<MessageProviderDefaultQueryCriteria> criteria)
    {
        return await Service.GetPageAsync(criteria);
    }

    [HttpGet]
    public async Task<IEnumerable<MessageProviderDefaultDto>> GetListAsync([FromQuery] MessageProviderDefaultQueryCriteria criteria)
    {
        return await Service.GetListAsync(criteria);
    }
}
