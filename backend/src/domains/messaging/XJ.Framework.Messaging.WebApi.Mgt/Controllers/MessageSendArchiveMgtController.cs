
namespace XJ.Framework.Messaging.WebApi.Mgt.Controllers;

/// <summary>
/// MessageSendArchive 控制器
/// </summary>

public class MessageSendArchiveController : BaseEditableAppController<long, MessageSendArchiveDto, MessageSendArchiveOperationDto, IMessageSendArchiveService, MessageSendArchiveQueryCriteria>
{
    public MessageSendArchiveController(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }
    
    
    [UnitOfWork]
    [HttpPost]
    public async Task<bool> InsertAsync(MessageSendArchiveOperationDto dto)
    {
        return await Service.CreateAsync(dto);
    }

    [HttpGet("{id:long}")]
    public async Task<MessageSendArchiveDto?> GetAsync(long id)
    {
        return await Service.GetByIdAsync(id);
    }

    [UnitOfWork]
    [HttpPut("{id:long}")]
    public async Task<bool> UpdateAsync(long id, MessageSendArchiveOperationDto dto)
    {
        return await Service.UpdateAsync(id, dto);
    }

    [UnitOfWork]
    [HttpDelete]
    public async Task<bool> DeleteAsync(long id)
    {
        return await Service.DeleteAsync(id);
    }

    [HttpGet("page")]
    public async Task<PageDtoData<long, MessageSendArchiveDto>> GetPageAsync([FromQuery] PagedQueryCriteria<MessageSendArchiveQueryCriteria> criteria)
    {
        return await Service.GetPageAsync(criteria);
    }

    [HttpGet]
    public async Task<IEnumerable<MessageSendArchiveDto>> GetListAsync([FromQuery] MessageSendArchiveQueryCriteria criteria)
    {
        return await Service.GetListAsync(criteria);
    }
}
