
namespace XJ.Framework.Messaging.WebApi.Mgt.Controllers;

/// <summary>
/// MessageSend 管理控制器（只读查询）
/// </summary>
public class MessageSendMgtController : BaseAppController<long, MessageSendDto, IMessageSendService, MessageSendQueryCriteria>
{
    public MessageSendMgtController(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }

    [HttpGet("{id:long}")]
    public async Task<MessageSendDto?> GetAsync(long id)
    {
        return await Service.GetByIdAsync(id);
    }

    [HttpGet("page")]
    public async Task<PageDtoData<long, MessageSendDto>> GetPageAsync([FromQuery] PagedQueryCriteria<MessageSendQueryCriteria> criteria)
    {
        return await Service.GetPageAsync(criteria);
    }

}
