
namespace XJ.Framework.Messaging.WebApi.Controllers;

/// <summary>
/// MessageSend 控制器
/// </summary>

public class MessageSendController : BaseEditableAppController<long, MessageSendDto, MessageSendOperationDto, IMessageSendService, MessageSendQueryCriteria>
{
    public MessageSendController(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }
    
    
    [UnitOfWork]
    [HttpPost]
    public async Task<bool> InsertAsync(MessageSendOperationDto dto)
    {
        return await Service.CreateAsync(dto);
    }

    [HttpGet("{id:long}")]
    public async Task<MessageSendDto?> GetAsync(long id)
    {
        return await Service.GetByIdAsync(id);
    }

    [UnitOfWork]
    [HttpPut("{id:long}")]
    public async Task<bool> UpdateAsync(long id, MessageSendOperationDto dto)
    {
        return await Service.UpdateAsync(id, dto);
    }

    [UnitOfWork]
    [HttpDelete]
    public async Task<bool> DeleteAsync(long id)
    {
        return await Service.DeleteAsync(id);
    }

    [HttpGet("page")]
    public async Task<PageDtoData<long, MessageSendDto>> GetPageAsync([FromQuery] PagedQueryCriteria<MessageSendQueryCriteria> criteria)
    {
        return await Service.GetPageAsync(criteria);
    }

    [HttpGet]
    public async Task<IEnumerable<MessageSendDto>> GetListAsync([FromQuery] MessageSendQueryCriteria criteria)
    {
        return await Service.GetListAsync(criteria);
    }
}
