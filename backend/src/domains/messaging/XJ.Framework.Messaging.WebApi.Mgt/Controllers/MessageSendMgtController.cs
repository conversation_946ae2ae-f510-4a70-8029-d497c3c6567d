
namespace XJ.Framework.Messaging.WebApi.Mgt.Controllers;

/// <summary>
/// MessageSend 管理控制器（只读查询）
/// </summary>
public class MessageSendController : BaseAppController<long, MessageSendDto, IMessageSendService, MessageSendQueryCriteria>
{
    public MessageSendController(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }

    [HttpGet("{id:long}")]
    public async Task<MessageSendDto?> GetAsync(long id)
    {
        return await Service.GetByIdAsync(id);
    }

    [HttpGet("page")]
    public async Task<PageDtoData<long, MessageSendDto>> GetPageAsync([FromQuery] PagedQueryCriteria<MessageSendQueryCriteria> criteria)
    {
        return await Service.GetPageAsync(criteria);
    }

}
