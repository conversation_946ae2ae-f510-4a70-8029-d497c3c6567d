
using XJ.Framework.Messaging.Domain.Shared.Dtos;

namespace XJ.Framework.Messaging.WebApi.Mgt.Controllers;

/// <summary>
/// MessageAccountProvider 管理控制器
/// </summary>
public class MessageAccountProviderController : BaseAppController<long, MessageAccountProviderDto, IMessageAccountProviderService, MessageAccountProviderQueryCriteria>
{
    public MessageAccountProviderController(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }

    [HttpGet("page")]
    public async Task<PageDtoData<long, MessageAccountProviderDto>> GetPageAsync([FromQuery] PagedQueryCriteria<MessageAccountProviderQueryCriteria> criteria)
    {
        return await Service.GetPageAsync(criteria);
    }
    /// <summary>
    /// 根据账户编码获取账户-服务商配置
    /// </summary>
    /// <param name="accountCode">账户编码</param>
    /// <returns>账户-服务商配置列表</returns>
    [HttpGet("by-account/{accountCode}")]
    public async Task<List<MessageAccountProviderDto>> GetByAccountCodeAsync(string accountCode)
    {
        return await Service.GetByAccountCodeAsync(accountCode);
    }

    /// <summary>
    /// 根据服务商编码获取账户-服务商配置
    /// </summary>
    /// <param name="providerCode">服务商编码</param>
    /// <returns>账户-服务商配置列表</returns>
    [HttpGet("by-provider/{providerCode}")]
    public async Task<List<MessageAccountProviderDto>> GetByProviderCodeAsync(string providerCode)
    {
        return await Service.GetByProviderCodeAsync(providerCode);
    }

    /// <summary>
    /// 根据账户编码和服务商编码获取配置
    /// </summary>
    /// <param name="accountCode">账户编码</param>
    /// <param name="providerCode">服务商编码</param>
    /// <returns>账户-服务商配置</returns>
    [HttpGet("by-account-provider")]
    public async Task<MessageAccountProviderDto?> GetByAccountAndProviderAsync([FromQuery] string accountCode, [FromQuery] string providerCode)
    {
        return await Service.GetByAccountAndProviderAsync(accountCode, providerCode);
    }

    /// <summary>
    /// 创建账户-服务商关联
    /// </summary>
    /// <param name="request">关联请求</param>
    /// <returns>创建结果</returns>
    [HttpPost("create-relation")]
    public async Task<bool> CreateAccountProviderAsync([FromBody] CreateAccountProviderRequestDto request)
    {
        return await Service.CreateAccountProviderAsync(request);
    }

    /// <summary>
    /// 更新账户-服务商配置
    /// </summary>
    /// <param name="id">配置ID</param>
    /// <param name="request">更新请求</param>
    /// <returns>更新结果</returns>
    [HttpPut("{id:long}/update-config")]
    public async Task<bool> UpdateAccountProviderAsync(long id, [FromBody] UpdateAccountProviderRequestDto request)
    {
        return await Service.UpdateAccountProviderAsync(id, request);
    }

    /// <summary>
    /// 启用/禁用账户-服务商配置
    /// </summary>
    /// <param name="id">配置ID</param>
    /// <param name="isEnabled">是否启用</param>
    /// <returns>操作结果</returns>
    [HttpPut("{id:long}/enabled")]
    public async Task<bool> SetEnabledAsync(long id, [FromBody] bool isEnabled)
    {
        return await Service.SetEnabledAsync(id, isEnabled);
    }

    /// <summary>
    /// 删除账户-服务商关联
    /// </summary>
    /// <param name="accountCode">账户编码</param>
    /// <param name="providerCode">服务商编码</param>
    /// <returns>删除结果</returns>
    [HttpDelete("remove-relation")]
    public async Task<bool> RemoveAccountProviderAsync([FromQuery] string accountCode, [FromQuery] string providerCode)
    {
        return await Service.RemoveAccountProviderAsync(accountCode, providerCode);
    }

    /// <summary>
    /// 获取账户的可用服务商列表（有剩余额度的）
    /// </summary>
    /// <param name="accountCode">账户编码</param>
    /// <param name="messageType">消息类型</param>
    /// <returns>可用服务商列表</returns>
    [HttpGet("available-providers")]
    public async Task<List<MessageAccountProviderDto>> GetAvailableProvidersAsync([FromQuery] string accountCode, [FromQuery] string messageType)
    {
        return await Service.GetAvailableProvidersAsync(accountCode, messageType);
    }
}
