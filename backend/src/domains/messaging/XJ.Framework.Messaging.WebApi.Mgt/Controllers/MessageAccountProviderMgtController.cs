
namespace XJ.Framework.Messaging.WebApi.Controllers;

/// <summary>
/// MessageAccountProvider 控制器
/// </summary>

public class MessageAccountProviderController : BaseEditableAppController<long, MessageAccountProviderDto, MessageAccountProviderOperationDto, IMessageAccountProviderService, MessageAccountProviderQueryCriteria>
{
    public MessageAccountProviderController(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }
    
    
    [UnitOfWork]
    [HttpPost]
    public async Task<bool> InsertAsync(MessageAccountProviderOperationDto dto)
    {
        return await Service.CreateAsync(dto);
    }

    [HttpGet("{id:long}")]
    public async Task<MessageAccountProviderDto?> GetAsync(long id)
    {
        return await Service.GetByIdAsync(id);
    }

    [UnitOfWork]
    [HttpPut("{id:long}")]
    public async Task<bool> UpdateAsync(long id, MessageAccountProviderOperationDto dto)
    {
        return await Service.UpdateAsync(id, dto);
    }

    [UnitOfWork]
    [HttpDelete]
    public async Task<bool> DeleteAsync(long id)
    {
        return await Service.DeleteAsync(id);
    }

    [HttpGet("page")]
    public async Task<PageDtoData<long, MessageAccountProviderDto>> GetPageAsync([FromQuery] PagedQueryCriteria<MessageAccountProviderQueryCriteria> criteria)
    {
        return await Service.GetPageAsync(criteria);
    }

    [HttpGet]
    public async Task<IEnumerable<MessageAccountProviderDto>> GetListAsync([FromQuery] MessageAccountProviderQueryCriteria criteria)
    {
        return await Service.GetListAsync(criteria);
    }
}
