
namespace XJ.Framework.Messaging.WebApi.Controllers;

/// <summary>
/// MessageProvider 控制器
/// </summary>

public class MessageProviderController : BaseEditableAppController<long, MessageProviderDto, MessageProviderOperationDto, IMessageProviderService, MessageProviderQueryCriteria>
{
    public MessageProviderController(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }
    
    
    [UnitOfWork]
    [HttpPost]
    public async Task<bool> InsertAsync(MessageProviderOperationDto dto)
    {
        return await Service.CreateAsync(dto);
    }

    [HttpGet("{id:long}")]
    public async Task<MessageProviderDto?> GetAsync(long id)
    {
        return await Service.GetByIdAsync(id);
    }

    [UnitOfWork]
    [HttpPut("{id:long}")]
    public async Task<bool> UpdateAsync(long id, MessageProviderOperationDto dto)
    {
        return await Service.UpdateAsync(id, dto);
    }

    [UnitOfWork]
    [HttpDelete]
    public async Task<bool> DeleteAsync(long id)
    {
        return await Service.DeleteAsync(id);
    }

    [HttpGet("page")]
    public async Task<PageDtoData<long, MessageProviderDto>> GetPageAsync([FromQuery] PagedQueryCriteria<MessageProviderQueryCriteria> criteria)
    {
        return await Service.GetPageAsync(criteria);
    }

    [HttpGet]
    public async Task<IEnumerable<MessageProviderDto>> GetListAsync([FromQuery] MessageProviderQueryCriteria criteria)
    {
        return await Service.GetListAsync(criteria);
    }
}
