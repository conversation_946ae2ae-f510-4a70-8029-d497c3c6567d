
namespace XJ.Framework.Messaging.WebApi.Mgt.Controllers;

/// <summary>
/// MessageProvider 管理控制器
/// </summary>
public class MessageProviderController : BaseAppController<long, MessageProviderDto, IMessageProviderService, MessageProviderQueryCriteria>
{
    public MessageProviderController(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }

    [HttpGet("page")]
    public async Task<PageDtoData<long, MessageProviderDto>> GetPageAsync([FromQuery] PagedQueryCriteria<MessageProviderQueryCriteria> criteria)
    {
        return await Service.GetPageAsync(criteria);
    }

}
