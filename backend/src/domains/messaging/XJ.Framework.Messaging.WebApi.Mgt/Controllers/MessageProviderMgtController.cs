
namespace XJ.Framework.Messaging.WebApi.Mgt.Controllers;

/// <summary>
/// MessageProvider 管理控制器
/// </summary>
public class MessageProviderController : BaseAppController<long, MessageProviderDto, IMessageProviderService, MessageProviderQueryCriteria>
{
    public MessageProviderController(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }

    [UnitOfWork]
    [HttpPost]
    public async Task<bool> InsertAsync(MessageProviderOperationDto dto)
    {
        return await Service.CreateAsync(dto);
    }

    [HttpGet("{id:long}")]
    public async Task<MessageProviderDto?> GetAsync(long id)
    {
        return await Service.GetByIdAsync(id);
    }

    [UnitOfWork]
    [HttpPut("{id:long}")]
    public async Task<bool> UpdateAsync(long id, MessageProviderOperationDto dto)
    {
        return await Service.UpdateAsync(id, dto);
    }

    [UnitOfWork]
    [HttpDelete("{id:long}")]
    public async Task<bool> DeleteAsync(long id)
    {
        return await Service.DeleteAsync(id);
    }

    [HttpGet("page")]
    public async Task<PageDtoData<long, MessageProviderDto>> GetPageAsync([FromQuery] PagedQueryCriteria<MessageProviderQueryCriteria> criteria)
    {
        return await Service.GetPageAsync(criteria);
    }

    [HttpGet]
    public async Task<IEnumerable<MessageProviderDto>> GetListAsync([FromQuery] MessageProviderQueryCriteria criteria)
    {
        return await Service.GetListAsync(criteria);
    }

    /// <summary>
    /// 根据服务商编码获取服务商
    /// </summary>
    /// <param name="providerCode">服务商编码</param>
    /// <returns>服务商信息</returns>
    [HttpGet("by-code/{providerCode}")]
    public async Task<MessageProviderDto?> GetByCodeAsync(string providerCode)
    {
        return await Service.GetByCodeAsync(providerCode);
    }

    /// <summary>
    /// 根据服务商类型获取服务商列表
    /// </summary>
    /// <param name="providerType">服务商类型</param>
    /// <returns>服务商列表</returns>
    [HttpGet("by-type/{providerType}")]
    public async Task<List<MessageProviderDto>> GetByTypeAsync(string providerType)
    {
        return await Service.GetByTypeAsync(providerType);
    }

    /// <summary>
    /// 启用/禁用服务商
    /// </summary>
    /// <param name="id">服务商ID</param>
    /// <param name="isEnabled">是否启用</param>
    /// <returns>操作结果</returns>
    [HttpPut("{id:long}/enabled")]
    public async Task<bool> SetEnabledAsync(long id, [FromBody] bool isEnabled)
    {
        return await Service.SetEnabledAsync(id, isEnabled);
    }

    /// <summary>
    /// 获取所有启用的服务商
    /// </summary>
    /// <returns>启用的服务商列表</returns>
    [HttpGet("enabled")]
    public async Task<List<MessageProviderDto>> GetEnabledProvidersAsync()
    {
        return await Service.GetEnabledProvidersAsync();
    }

}
