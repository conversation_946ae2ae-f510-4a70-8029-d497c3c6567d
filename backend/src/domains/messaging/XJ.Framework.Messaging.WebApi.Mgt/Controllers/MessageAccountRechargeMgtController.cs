namespace XJ.Framework.Messaging.WebApi.Mgt.Controllers;

/// <summary>
/// MessageAccountRecharge 控制器
/// </summary>
public class MessageAccountRechargeController : BaseEditableAppController<long, MessageAccountRechargeDto,
    MessageAccountRechargeOperationDto, IMessageAccountRechargeService, MessageAccountRechargeQueryCriteria>
{
    public MessageAccountRechargeController(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }


    [HttpGet("{id:long}")]
    public async Task<MessageAccountRechargeDto?> GetAsync(long id)
    {
        return await Service.GetByIdAsync(id);
    }


    [HttpGet("page")]
    public async Task<PageDtoData<long, MessageAccountRechargeDto>> GetPageAsync(
        [FromQuery] PagedQueryCriteria<MessageAccountRechargeQueryCriteria> criteria)
    {
        return await Service.GetPageAsync(criteria);
    }

    [HttpGet]
    public async Task<IEnumerable<MessageAccountRechargeDto>> GetListAsync(
        [FromQuery] MessageAccountRechargeQueryCriteria criteria)
    {
        return await Service.GetListAsync(criteria);
    }
}
