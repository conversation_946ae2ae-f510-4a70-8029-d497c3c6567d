
namespace XJ.Framework.Messaging.WebApi.Controllers;

/// <summary>
/// MessageAccountRecharge 控制器
/// </summary>

public class MessageAccountRechargeController : BaseEditableAppController<long, MessageAccountRechargeDto, MessageAccountRechargeOperationDto, IMessageAccountRechargeService, MessageAccountRechargeQueryCriteria>
{
    public MessageAccountRechargeController(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }
    
    
    [UnitOfWork]
    [HttpPost]
    public async Task<bool> InsertAsync(MessageAccountRechargeOperationDto dto)
    {
        return await Service.CreateAsync(dto);
    }

    [HttpGet("{id:long}")]
    public async Task<MessageAccountRechargeDto?> GetAsync(long id)
    {
        return await Service.GetByIdAsync(id);
    }

    [UnitOfWork]
    [HttpPut("{id:long}")]
    public async Task<bool> UpdateAsync(long id, MessageAccountRechargeOperationDto dto)
    {
        return await Service.UpdateAsync(id, dto);
    }

    [UnitOfWork]
    [HttpDelete]
    public async Task<bool> DeleteAsync(long id)
    {
        return await Service.DeleteAsync(id);
    }

    [HttpGet("page")]
    public async Task<PageDtoData<long, MessageAccountRechargeDto>> GetPageAsync([FromQuery] PagedQueryCriteria<MessageAccountRechargeQueryCriteria> criteria)
    {
        return await Service.GetPageAsync(criteria);
    }

    [HttpGet]
    public async Task<IEnumerable<MessageAccountRechargeDto>> GetListAsync([FromQuery] MessageAccountRechargeQueryCriteria criteria)
    {
        return await Service.GetListAsync(criteria);
    }
}
