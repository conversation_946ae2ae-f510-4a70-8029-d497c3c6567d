
namespace XJ.Framework.Messaging.WebApi.Mgt.Controllers;

/// <summary>
/// MessageTemplate 管理控制器
/// </summary>
public class MessageTemplateController : BaseAppController<long, MessageTemplateDto, IMessageTemplateService, MessageTemplateQueryCriteria>
{
    public MessageTemplateController(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }

    [HttpGet("page")]
    public async Task<PageDtoData<long, MessageTemplateDto>> GetPageAsync([FromQuery] PagedQueryCriteria<MessageTemplateQueryCriteria> criteria)
    {
        return await Service.GetPageAsync(criteria);
    }


    /// <summary>
    /// 根据模板编码和应用编码获取模板
    /// </summary>
    /// <param name="templateCode">模板编码</param>
    /// <param name="appCode">应用编码</param>
    /// <returns>模板信息</returns>
    [HttpGet("by-code")]
    public async Task<MessageTemplateDto?> GetByCodeAsync([FromQuery] string templateCode, [FromQuery] string appCode)
    {
        return await Service.GetByCodeAsync(templateCode, appCode);
    }

    /// <summary>
    /// 根据应用编码获取模板列表
    /// </summary>
    /// <param name="appCode">应用编码</param>
    /// <returns>模板列表</returns>
    [HttpGet("by-app/{appCode}")]
    public async Task<List<MessageTemplateDto>> GetByAppCodeAsync(string appCode)
    {
        return await Service.GetByAppCodeAsync(appCode);
    }

    /// <summary>
    /// 启用/禁用模板
    /// </summary>
    /// <param name="id">模板ID</param>
    /// <param name="isEnabled">是否启用</param>
    /// <returns>操作结果</returns>
    [HttpPut("{id:long}/enabled")]
    public async Task<bool> SetEnabledAsync(long id, [FromBody] bool isEnabled)
    {
        return await Service.SetEnabledAsync(id, isEnabled);
    }
}
