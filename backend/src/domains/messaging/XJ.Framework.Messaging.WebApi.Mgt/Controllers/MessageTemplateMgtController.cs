
namespace XJ.Framework.Messaging.WebApi.Mgt.Controllers;

/// <summary>
/// MessageTemplate 控制器
/// </summary>

public class MessageTemplateController : BaseEditableAppController<long, MessageTemplateDto, MessageTemplateOperationDto, IMessageTemplateService, MessageTemplateQueryCriteria>
{
    public MessageTemplateController(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }
    
    
    [UnitOfWork]
    [HttpPost]
    public async Task<bool> InsertAsync(MessageTemplateOperationDto dto)
    {
        return await Service.CreateAsync(dto);
    }

    [HttpGet("{id:long}")]
    public async Task<MessageTemplateDto?> GetAsync(long id)
    {
        return await Service.GetByIdAsync(id);
    }

    [UnitOfWork]
    [HttpPut("{id:long}")]
    public async Task<bool> UpdateAsync(long id, MessageTemplateOperationDto dto)
    {
        return await Service.UpdateAsync(id, dto);
    }

    [UnitOfWork]
    [HttpDelete]
    public async Task<bool> DeleteAsync(long id)
    {
        return await Service.DeleteAsync(id);
    }

    [HttpGet("page")]
    public async Task<PageDtoData<long, MessageTemplateDto>> GetPageAsync([FromQuery] PagedQueryCriteria<MessageTemplateQueryCriteria> criteria)
    {
        return await Service.GetPageAsync(criteria);
    }

    [HttpGet]
    public async Task<IEnumerable<MessageTemplateDto>> GetListAsync([FromQuery] MessageTemplateQueryCriteria criteria)
    {
        return await Service.GetListAsync(criteria);
    }
}
