
namespace XJ.Framework.Messaging.WebApi.Controllers;

/// <summary>
/// MessageAccount 控制器
/// </summary>

public class MessageAccountController : BaseEditableAppController<long, MessageAccountDto, MessageAccountOperationDto, IMessageAccountService, MessageAccountQueryCriteria>
{
    public MessageAccountController(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }
    
    
    [UnitOfWork]
    [HttpPost]
    public async Task<bool> InsertAsync(MessageAccountOperationDto dto)
    {
        return await Service.CreateAsync(dto);
    }

    [HttpGet("{id:long}")]
    public async Task<MessageAccountDto?> GetAsync(long id)
    {
        return await Service.GetByIdAsync(id);
    }

    [UnitOfWork]
    [HttpPut("{id:long}")]
    public async Task<bool> UpdateAsync(long id, MessageAccountOperationDto dto)
    {
        return await Service.UpdateAsync(id, dto);
    }

    [UnitOfWork]
    [HttpDelete]
    public async Task<bool> DeleteAsync(long id)
    {
        return await Service.DeleteAsync(id);
    }

    [HttpGet("page")]
    public async Task<PageDtoData<long, MessageAccountDto>> GetPageAsync([FromQuery] PagedQueryCriteria<MessageAccountQueryCriteria> criteria)
    {
        return await Service.GetPageAsync(criteria);
    }

    [HttpGet]
    public async Task<IEnumerable<MessageAccountDto>> GetListAsync([FromQuery] MessageAccountQueryCriteria criteria)
    {
        return await Service.GetListAsync(criteria);
    }
}
