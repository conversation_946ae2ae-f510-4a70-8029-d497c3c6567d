using XJ.Framework.Library.Domain.Shared.Interfaces;
using XJ.Framework.Messaging.Domain.Shared.Dtos;

namespace XJ.Framework.Messaging.WebApi.Mgt.Controllers;

/// <summary>
/// MessageAccount 管理控制器
/// </summary>
public class MessageAccountMgtController : BaseAppController<long, MessageAccountDto, IMessageAccountService,
    MessageAccountQueryCriteria>
{
    private readonly ICurrentUserContext _currentUserContext;

    public MessageAccountMgtController(IServiceProvider serviceProvider, ICurrentUserContext currentUserContext) : base(
        serviceProvider)
    {
        _currentUserContext = currentUserContext;
    }

    [HttpGet("page")]
    public async Task<PageDtoData<long, MessageAccountDto>> GetPageAsync(
        [FromQuery] PagedQueryCriteria<MessageAccountQueryCriteria> criteria)
    {
        return await Service.GetPageAsync(criteria);
    }

    /// <summary>
    /// 根据账户编码获取账户
    /// </summary>
    /// <param name="accountCode">账户编码</param>
    /// <returns>账户信息</returns>
    [HttpGet("by-code/{accountCode}")]
    public async Task<MessageAccountDto?> GetByCodeAsync(string accountCode)
    {
        return await Service.GetByCodeAsync(accountCode);
    }

    /// <summary>
    /// 根据应用编码获取账户列表
    /// </summary>
    /// <param name="appCode">应用编码</param>
    /// <returns>账户列表</returns>
    [HttpGet("by-app/{appCode}")]
    public async Task<List<MessageAccountDto>> GetByAppCodeAsync(string appCode)
    {
        return await Service.GetByAppCodeAsync(appCode);
    }

    /// <summary>
    /// 启用/禁用账户
    /// </summary>
    /// <param name="id">账户ID</param>
    /// <param name="isEnabled">是否启用</param>
    /// <returns>操作结果</returns>
    [HttpPut("{id:long}/enabled")]
    public async Task<bool> SetEnabledAsync(long id, [FromBody] bool isEnabled)
    {
        return await Service.SetEnabledAsync(id, isEnabled);
    }

    /// <summary>
    /// 账户-服务商充值
    /// </summary>
    /// <param name="request">充值请求</param>
    /// <returns>充值结果</returns>
    [HttpPost("recharge")]
    public async Task<bool> RechargeAsync([FromBody] AccountProviderRechargeRequestDto request)
    {
        // 获取当前用户ID（这里需要根据实际的用户上下文获取）
        var operatorId = _currentUserContext.GetCurrentUserId() ?? 0;
        return await Service.RechargeAsync(request.AccountCode, request.ProviderCode, request.Amount, operatorId, request.Remark);
    }

    /// <summary>
    /// 获取账户余额（按服务商分组）
    /// </summary>
    /// <param name="accountCode">账户编码</param>
    /// <returns>余额信息列表</returns>
    [HttpGet("balance/{accountCode}")]
    public async Task<List<AccountProviderBalanceDto>> GetBalanceByProviderAsync(string accountCode)
    {
        return await Service.GetBalanceByProviderAsync(accountCode);
    }

    /// <summary>
    /// 获取账户在特定服务商的余额
    /// </summary>
    /// <param name="accountCode">账户编码</param>
    /// <param name="providerCode">服务商编码</param>
    /// <returns>余额信息</returns>
    [HttpGet("balance/{accountCode}/{providerCode}")]
    public async Task<AccountProviderBalanceDto?> GetBalanceAsync(string accountCode, string providerCode)
    {
        return await Service.GetBalanceAsync(accountCode, providerCode);
    }

    /// <summary>
    /// 获取账户总体余额汇总
    /// </summary>
    /// <param name="accountCode">账户编码</param>
    /// <returns>汇总余额信息</returns>
    [HttpGet("balance-summary/{accountCode}")]
    public async Task<AccountBalanceSummaryDto?> GetBalanceSummaryAsync(string accountCode)
    {
        return await Service.GetBalanceSummaryAsync(accountCode);
    }
}
