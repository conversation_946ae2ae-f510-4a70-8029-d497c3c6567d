using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using XJ.Framework.Library.Domain.Id;
using XJ.Framework.Library.Domain.UOW;
using XJ.Framework.Messaging.Application.Contract.Interfaces;
using XJ.Framework.Messaging.Domain.Repositories.Interfaces;
using XJ.Framework.Messaging.Domain.Entities;

namespace XJ.Framework.Messaging.WebApi.Mgt.Workers;

/// <summary>
/// 消息发送后台Worker
/// </summary>
public class MessageSendWorker : BackgroundService
{
    private readonly ILogger<MessageSendWorker> _logger;
    private readonly IServiceProvider _serviceProvider;
    private readonly TimeSpan _pollingInterval = TimeSpan.FromSeconds(10); // 轮询间隔
    private readonly int _batchSize = 50; // 每次处理的消息数量
    private readonly int _maxRetryTimes = 3; // 最大重试次数

    public MessageSendWorker(ILogger<MessageSendWorker> logger, IServiceProvider serviceProvider)
    {
        _logger = logger;
        _serviceProvider = serviceProvider;
    }

    protected async override Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("消息发送Worker已启动");

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await ProcessPendingMessagesAsync(stoppingToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理待发送消息时发生异常");
            }

            // 等待下次轮询
            await Task.Delay(_pollingInterval, stoppingToken);
        }

        _logger.LogInformation("消息发送Worker已停止");
    }

    /// <summary>
    /// 处理待发送消息
    /// </summary>
    private async Task ProcessPendingMessagesAsync(CancellationToken cancellationToken)
    {
        using var scope = _serviceProvider.CreateScope();
        var messageSendRepository = scope.ServiceProvider.GetRequiredService<IMessageSendRepository>();
        var messageProviderFactory = scope.ServiceProvider.GetRequiredService<IMessageProviderFactory>();
        var messageTemplateService = scope.ServiceProvider.GetRequiredService<IMessageTemplateService>();
        var messageAccountUsageService = scope.ServiceProvider.GetRequiredService<IMessageAccountUsageService>();

        try
        {
            // 获取待发送的消息（状态为0且重试次数未超限）
            var pendingMessages = await GetPendingMessagesAsync(messageSendRepository);

            if (!pendingMessages.Any())
            {
                _logger.LogDebug("没有待发送的消息");
                return;
            }

            _logger.LogInformation("找到 {Count} 条待发送消息", pendingMessages.Count);

            // 并行处理消息（限制并发数）
            var semaphore = new SemaphoreSlim(Environment.ProcessorCount);
            var tasks = pendingMessages.Select(async message =>
            {
                await semaphore.WaitAsync(cancellationToken);
                try
                {
                    await ProcessSingleMessageAsync(message, messageProviderFactory, messageTemplateService,
                        messageAccountUsageService, messageSendRepository);
                }
                finally
                {
                    semaphore.Release();
                }
            });

            await Task.WhenAll(tasks);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "批量处理消息时发生异常");
        }
    }

    /// <summary>
    /// 获取待发送消息
    /// </summary>
    private async Task<List<MessageSendEntity>> GetPendingMessagesAsync(IMessageSendRepository repository)
    {
        return await repository.GetPendingMessagesAsync(_batchSize, _maxRetryTimes);
    }

    /// <summary>
    /// 处理单条消息
    /// </summary>
    private async Task ProcessSingleMessageAsync(
        MessageSendEntity message,
        IMessageProviderFactory providerFactory,
        IMessageTemplateService templateService,
        IMessageAccountUsageService usageService,
        IMessageSendRepository repository)
    {
        try
        {
            _logger.LogInformation("开始处理消息，ID：{MessageId}，目标：{Target}", message.Key, message.Target);

            // 验证消息基本信息
            var validationResult = await ValidateMessageAsync(message);
            if (!validationResult.IsValid)
            {
                await HandleSendFailureAsync(message, validationResult.ErrorMessage, repository);
                return;
            }

            // 检查账户额度
            var quotaCheckResult = await CheckAccountQuotaAsync(message);
            if (!quotaCheckResult.IsValid)
            {
                await HandleSendFailureAsync(message, quotaCheckResult.ErrorMessage, repository);
                return;
            }

            // 更新状态为发送中
            message.Status = 1; // 发送中
            message.SendTime = DateTimeOffset.Now;
            await repository.UpdateAsync(message);

            // 获取Provider
            var provider = providerFactory.GetProvider(message.MessageType);
            if (provider == null)
            {
                await HandleSendFailureAsync(message, $"不支持的消息类型：{message.MessageType}", repository);
                return;
            }

            // 渲染消息内容
            var content = await RenderMessageContentAsync(message, templateService);
            if (string.IsNullOrEmpty(content))
            {
                await HandleSendFailureAsync(message, "消息内容渲染失败", repository);
                return;
            }

            if (!provider.ValidateTarget(message.Target))
            {
                await HandleSendFailureAsync(message, "目标格式不正确", repository);
                return;
            }

            // 发送消息
            var result = await provider.SendAsync(message.Target, content, message.ProviderCode);

            if (result.Success)
            {
                // 发送成功
                await HandleSendSuccessAsync(message, result, usageService, repository);
            }
            else
            {
                // 发送失败
                await HandleSendFailureAsync(message, result.ErrorMessage ?? "发送失败", repository);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理消息时发生异常，消息ID：{MessageId}", message.Key);
            await HandleSendFailureAsync(message, $"处理异常：{ex.Message}", repository);
        }
    }

    /// <summary>
    /// 渲染消息内容
    /// </summary>
    private async Task<string?> RenderMessageContentAsync(MessageSendEntity message,
        IMessageTemplateService templateService)
    {
        try
        {
            // 获取模板
            var template = await GetTemplateByCodeAsync(templateService, message.TemplateCode, message.AppCode);
            if (template == null)
            {
                _logger.LogWarning("未找到模板，模板编码：{TemplateCode}，应用编码：{AppCode}",
                    message.TemplateCode, message.AppCode);
                return null;
            }

            // 检查模板是否启用
            if (!template.IsEnabled)
            {
                _logger.LogWarning("模板已禁用，模板编码：{TemplateCode}，应用编码：{AppCode}",
                    message.TemplateCode, message.AppCode);
                return null;
            }

            // 检查模板类型是否匹配
            if (!string.Equals(template.TemplateType, message.MessageType, StringComparison.OrdinalIgnoreCase))
            {
                _logger.LogWarning("模板类型不匹配，模板类型：{TemplateType}，消息类型：{MessageType}",
                    template.TemplateType, message.MessageType);
                return null;
            }

            var content = template.Content;

            // 解析并替换变量
            if (!string.IsNullOrEmpty(message.VariablesJson))
            {
                content = await ReplaceVariablesAsync(content, message.VariablesJson);
            }

            return content;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "渲染消息内容时发生异常，模板编码：{TemplateCode}", message.TemplateCode);
            return null;
        }
    }

    /// <summary>
    /// 替换模板变量
    /// </summary>
    /// <param name="content">模板内容</param>
    /// <param name="variablesJson">变量JSON</param>
    /// <returns>替换后的内容</returns>
    private async Task<string> ReplaceVariablesAsync(string content, string variablesJson)
    {
        try
        {
            // 解析JSON变量
            var variables = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(variablesJson);
            if (variables == null || !variables.Any())
            {
                return content;
            }

            var result = content;

            // 简单的变量替换（支持 {{变量名}} 格式）
            foreach (var variable in variables)
            {
                var placeholder = $"{{{{{variable.Key}}}}}";
                var value = variable.Value?.ToString() ?? "";
                result = result.Replace(placeholder, value);
            }

            // 也支持 ${变量名} 格式
            foreach (var variable in variables)
            {
                var placeholder = $"${{{variable.Key}}}";
                var value = variable.Value?.ToString() ?? "";
                result = result.Replace(placeholder, value);
            }

            return await Task.FromResult(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "替换模板变量时发生异常，变量JSON：{VariablesJson}", variablesJson);
            return content; // 返回原始内容
        }
    }

    /// <summary>
    /// 根据模板编码获取模板
    /// </summary>
    private async Task<MessageTemplateDto?> GetTemplateByCodeAsync(IMessageTemplateService templateService,
        string templateCode, string appCode)
    {
        return await templateService.GetByCodeAsync(templateCode, appCode);
    }

    /// <summary>
    /// 处理发送成功
    /// </summary>
    private async Task HandleSendSuccessAsync(
        MessageSendEntity message,
        MessageProviderResult result,
        IMessageAccountUsageService usageService,
        IMessageSendRepository repository)
    {
        try
        {
            // 更新消息状态
            message.Status = 2; // 发送成功
            message.SendTime = DateTimeOffset.Now;
            message.ErrorInfo = null;
            await repository.UpdateAsync(message);

            // 记录用量
            await RecordUsageAsync(message, usageService);

            _logger.LogInformation("消息发送成功，ID：{MessageId}，目标：{Target}，服务商消息ID：{ProviderMessageId}",
                message.Key, message.Target, result.ProviderMessageId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理发送成功时发生异常，消息ID：{MessageId}", message.Key);
        }
    }

    /// <summary>
    /// 处理发送失败
    /// </summary>
    private async Task HandleSendFailureAsync(
        MessageSendEntity message,
        string errorMessage,
        IMessageSendRepository repository)
    {
        try
        {
            message.TryTime++;
            message.ErrorInfo = errorMessage;

            if (message.TryTime >= _maxRetryTimes)
            {
                // 超过最大重试次数，标记为失败
                message.Status = 3; // 发送失败
                _logger.LogWarning("消息发送失败，已达最大重试次数，ID：{MessageId}，错误：{Error}",
                    message.Key, errorMessage);
            }
            else
            {
                // 重置为待发送状态，等待重试
                message.Status = 0; // 待发送
                _logger.LogWarning("消息发送失败，将重试，ID：{MessageId}，重试次数：{TryTime}，错误：{Error}",
                    message.Key, message.TryTime, errorMessage);
            }

            await repository.UpdateAsync(message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理发送失败时发生异常，消息ID：{MessageId}", message.Key);
        }
    }

    /// <summary>
    /// 记录用量
    /// </summary>
    private async Task RecordUsageAsync(MessageSendEntity message, IMessageAccountUsageService usageService)
    {
        try
        {
            using var scope = _serviceProvider.CreateScope();
            var accountProviderRepository =
                scope.ServiceProvider.GetRequiredService<IMessageAccountProviderRepository>();
            var usageRepository = scope.ServiceProvider.GetRequiredService<IMessageAccountUsageRepository>();
            var unitOfWork = scope.ServiceProvider.GetRequiredService<IUnitOfWork>();

            // 获取账户-服务商配置
            var accountProvider = await GetAccountProviderForMessageAsync(message, accountProviderRepository);
            if (accountProvider == null)
            {
                _logger.LogWarning("未找到账户-服务商配置，无法记录用量，消息ID：{MessageId}", message.Key);
                return;
            }

            await unitOfWork.BeginTransactionAsync();

            // 检查额度是否足够
            var remainingQuota = (accountProvider.QuotaTotal ?? 0) - (accountProvider.QuotaUsed ?? 0);
            if (remainingQuota <= 0)
            {
                _logger.LogWarning("账户额度不足，无法扣减用量，账户：{AccountCode}，服务商：{ProviderCode}",
                    accountProvider.AccountCode, accountProvider.ProviderCode);
                return;
            }

            // 扣减额度
            accountProvider.QuotaUsed = (accountProvider.QuotaUsed ?? 0) + 1;
            await accountProviderRepository.UpdateAsync(accountProvider);

            // 记录用量
            var usageRecord = new MessageAccountUsageEntity
            {
                AccountCode = accountProvider.AccountCode,
                ProviderCode = accountProvider.ProviderCode,
                MessageType = message.MessageType,
                Amount = 1,
                BalanceAfter = (accountProvider.QuotaTotal ?? 0) - (accountProvider.QuotaUsed ?? 0),


                Key = scope.ServiceProvider.GetRequiredService<IKeyGenerator<long>>().GenerateKey(),
                AccountProviderId = accountProvider.Key,
                MessageSendId = message.Key,
                UsageTime = DateTimeOffset.Now,
                UsageType = message.MessageType,
                Target = message.Target,
                TemplateCode = message.TemplateCode,
                AppCode = message.AppCode
            };

            await usageRepository.InsertAsync(usageRecord);

            await unitOfWork.CommitAsync();

            _logger.LogInformation("用量记录成功，消息ID：{MessageId}，账户：{AccountCode}，服务商：{ProviderCode}，剩余额度：{RemainingQuota}",
                message.Key, accountProvider.AccountCode, accountProvider.ProviderCode,
                (accountProvider.QuotaTotal ?? 0) - (accountProvider.QuotaUsed ?? 0));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "记录用量时发生异常，消息ID：{MessageId}", message.Key);
        }
    }

    /// <summary>
    /// 获取消息对应的账户-服务商配置
    /// </summary>
    private async Task<MessageAccountProviderEntity?> GetAccountProviderForMessageAsync(
        MessageSendEntity message,
        IMessageAccountProviderRepository accountProviderRepository)
    {
        try
        {
            // 如果指定了服务商编码，优先使用指定的服务商
            if (!string.IsNullOrEmpty(message.ProviderCode))
            {
                var specificProvider = await accountProviderRepository.GetByAccountAndProviderAsync(
                    message.AppCode, message.ProviderCode);
                if (specificProvider != null && specificProvider.IsEnabled)
                {
                    return specificProvider;
                }
            }

            // 获取应用的所有账户-服务商配置
            var accountProviders = await accountProviderRepository.GetByAccountCodeAsync(message.AppCode);

            // 筛选启用的、类型匹配的配置
            var availableProviders = accountProviders
                .Where(x => x.IsEnabled &&
                            string.Equals(x.ProviderType, message.MessageType, StringComparison.OrdinalIgnoreCase) &&
                            (x.QuotaTotal ?? 0) > (x.QuotaUsed ?? 0)) // 有剩余额度
                .OrderByDescending(x => x.Priority) // 按优先级排序
                .ThenBy(x => x.QuotaUsed) // 然后按已用额度升序排序（优先使用用量少的）
                .ToList();

            return availableProviders.FirstOrDefault();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取账户-服务商配置时发生异常，消息ID：{MessageId}", message.Key);
            return null;
        }
    }

    /// <summary>
    /// 验证消息基本信息
    /// </summary>
    private async Task<ValidationResult> ValidateMessageAsync(MessageSendEntity message)
    {
        try
        {
            // 验证必填字段
            if (string.IsNullOrWhiteSpace(message.AppCode))
            {
                return ValidationResult.Failure("应用编码不能为空");
            }

            if (string.IsNullOrWhiteSpace(message.Target))
            {
                return ValidationResult.Failure("发送目标不能为空");
            }

            if (string.IsNullOrWhiteSpace(message.TemplateCode))
            {
                return ValidationResult.Failure("模板编码不能为空");
            }

            if (string.IsNullOrWhiteSpace(message.MessageType))
            {
                return ValidationResult.Failure("消息类型不能为空");
            }

            // // 验证目标格式
            // var targetValidationResult = ValidateTarget(message.Target, message.MessageType);
            // if (!targetValidationResult.IsValid)
            // {
            //     return targetValidationResult;
            // }

            return ValidationResult.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "验证消息时发生异常，消息ID：{MessageId}", message.Key);
            return ValidationResult.Failure($"验证异常：{ex.Message}");
        }
    }

    // /// <summary>
    // /// 验证发送目标格式
    // /// </summary>
    // private ValidationResult ValidateTarget(string target, string messageType)
    // {
    //     switch (messageType.ToLower())
    //     {
    //         case "sms":
    //             if (!IsValidPhoneNumber(target))
    //             {
    //                 return ValidationResult.Failure("无效的手机号格式");
    //             }
    //
    //             break;
    //         case "email":
    //             if (!IsValidEmail(target))
    //             {
    //                 return ValidationResult.Failure("无效的邮箱地址格式");
    //             }
    //
    //             break;
    //         default:
    //             // 其他类型暂不验证
    //             break;
    //     }
    //
    //     return ValidationResult.Success();
    // }


    /// <summary>
    /// 检查账户额度
    /// </summary>
    private async Task<ValidationResult> CheckAccountQuotaAsync(MessageSendEntity message)
    {
        try
        {
            using var scope = _serviceProvider.CreateScope();
            var accountProviderRepository =
                scope.ServiceProvider.GetRequiredService<IMessageAccountProviderRepository>();

            var accountProvider = await GetAccountProviderForMessageAsync(message, accountProviderRepository);
            if (accountProvider == null)
            {
                return ValidationResult.Failure("未找到可用的账户-服务商配置");
            }

            var remainingQuota = (accountProvider.QuotaTotal ?? 0) - (accountProvider.QuotaUsed ?? 0);
            if (remainingQuota <= 0)
            {
                return ValidationResult.Failure($"账户额度不足，剩余额度：{remainingQuota}");
            }

            return ValidationResult.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查账户额度时发生异常，消息ID：{MessageId}", message.Key);
            return ValidationResult.Failure($"额度检查异常：{ex.Message}");
        }
    }

    /// <summary>
    /// 验证结果
    /// </summary>
    private class ValidationResult
    {
        public bool IsValid { get; private set; }
        public string ErrorMessage { get; private set; } = string.Empty;

        private ValidationResult(bool isValid, string errorMessage = "")
        {
            IsValid = isValid;
            ErrorMessage = errorMessage;
        }

        public static ValidationResult Success() => new(true);
        public static ValidationResult Failure(string errorMessage) => new(false, errorMessage);
    }
}
