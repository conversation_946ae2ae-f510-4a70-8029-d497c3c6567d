using System.Collections.Concurrent;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using XJ.Framework.Library.Domain.Id;
using XJ.Framework.Library.Domain.UOW;
using XJ.Framework.Messaging.Domain.Repositories.Interfaces;
using XJ.Framework.Messaging.Domain.Entities;
using XJ.Framework.Messaging.Domain.Shared.Enums;

namespace XJ.Framework.Messaging.WebApi.Mgt.Workers;

/// <summary>
/// 消息发送后台Worker
/// </summary>
public class MessageSendWorker : BackgroundService
{
    private readonly ILogger<MessageSendWorker> _logger;
    private readonly IServiceProvider _serviceProvider;

    // 账户级别的并发控制：每个账户一个信号量，确保同一账户的消息串行处理用量扣减
    private readonly ConcurrentDictionary<string, SemaphoreSlim> _accountSemaphores = new();

    // 清理不再使用的信号量的定时器
    private readonly Timer _cleanupTimer;
    private readonly TimeSpan _pollingInterval = TimeSpan.FromSeconds(10); // 轮询间隔
    private readonly int _batchSize = 50; // 每次处理的消息数量
    private readonly int _maxRetryTimes = 3; // 最大重试次数

    public MessageSendWorker(ILogger<MessageSendWorker> logger, IServiceProvider serviceProvider)
    {
        _logger = logger;
        _serviceProvider = serviceProvider;

        // 初始化清理定时器，每5分钟清理一次不再使用的账户信号量
        _cleanupTimer = new Timer(CleanupAccountSemaphores, null, TimeSpan.FromMinutes(5), TimeSpan.FromMinutes(5));
    }

    protected async override Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("消息发送Worker已启动");

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await ProcessPendingMessagesAsync(stoppingToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理待发送消息时发生异常");
            }

            // 等待下次轮询
            await Task.Delay(_pollingInterval, stoppingToken);
        }

        _logger.LogInformation("消息发送Worker已停止");
    }

    /// <summary>
    /// 处理待发送消息
    /// </summary>
    private async Task ProcessPendingMessagesAsync(CancellationToken cancellationToken)
    {
        using var scope = _serviceProvider.CreateScope();
        var messageSendRepository = scope.ServiceProvider.GetRequiredService<IMessageSendRepository>();
        var messageProviderFactory = scope.ServiceProvider.GetRequiredService<IMessageProviderFactory>();
        var messageTemplateService = scope.ServiceProvider.GetRequiredService<IMessageTemplateService>();
        var messageAccountUsageService = scope.ServiceProvider.GetRequiredService<IMessageAccountUsageService>();

        try
        {
            // 获取待发送的消息（状态为0且重试次数未超限）
            var pendingMessages = await GetPendingMessagesAsync(messageSendRepository);

            if (!pendingMessages.Any())
            {
                Console.WriteLine(@"没有待发送的消息");

                return;
            }

            _logger.LogInformation("找到 {Count} 条待发送消息", pendingMessages.Count);

            // 按账户分组处理消息，确保同一账户的消息串行处理用量扣减
            await ProcessMessagesByAccountAsync(pendingMessages, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "批量处理消息时发生异常");
        }
    }

    /// <summary>
    /// 获取待发送消息
    /// </summary>
    private async Task<List<MessageSendEntity>> GetPendingMessagesAsync(IMessageSendRepository repository)
    {
        return await repository.GetPendingMessagesAsync(_batchSize, _maxRetryTimes);
    }

    /// <summary>
    /// 处理单条消息
    /// </summary>
    private async Task ProcessSingleMessageAsync(
        MessageSendEntity message,
        IMessageAccountRepository accountRepository,
        IMessageAccountProviderRepository accountProviderRepository,
        IMessageProviderRepository providerRepository,
        IMessageProviderFactory providerFactory,
        IMessageTemplateService templateService,
        IMessageAccountUsageService usageService,
        IMessageSendRepository repository)
    {
        try
        {
            _logger.LogInformation("开始处理消息，ID：{MessageId}，目标：{Target}", message.Key, message.Target);

            // 验证消息基本信息
            var validationResult = await ValidateMessageAsync(message);
            if (!validationResult.IsValid)
            {
                await HandleSendFailureAsync(message, validationResult.ErrorMessage, repository);
                return;
            }

            // 检查账户额度
            var quotaCheckResult = await CheckAccountQuotaAsync(message);
            if (!quotaCheckResult.IsValid)
            {
                await HandleSendFailureAsync(message, quotaCheckResult.ErrorMessage, repository);
                return;
            }

            // 更新状态为发送中
            message.Status = 1; // 发送中
            message.SendTime = DateTimeOffset.Now;
            await repository.UpdateAsync(message);

            var accountProvider =
                await GetAccountProviderForMessageAsync(message, accountProviderRepository, accountRepository);
            
            if(accountProvider == null)
            {
                await HandleSendFailureAsync(message, "未找到账户-服务商配置", repository);
                return;
            }

            var providerEntity = await providerRepository.GetByCodeAsync(accountProvider!.ProviderCode);
            if (providerEntity == null)
            {
                await HandleSendFailureAsync(message, "未找到服务商", repository);
                return;
            }

            // 获取Provider
            var provider = providerFactory.GetProvider(message.MessageType, providerEntity!.ProviderInstance);

            if (provider == null)
            {
                await HandleSendFailureAsync(message, $"不支持的消息类型：{message.MessageType}", repository);
                return;
            }

            //渲染消息标题
            var replaced = await RenderMessageContentAsync(message, templateService);
            if (replaced == null)
            {
                await HandleSendFailureAsync(message, "消息渲染失败", repository);
                return;
            }

            if (!provider.ValidateTarget(message.Target))
            {
                await HandleSendFailureAsync(message, "目标格式不正确", repository);
                return;
            }


            // 发送消息
            var result = await provider.SendAsync(message.Target, replaced.Value.title, replaced.Value.content,
                providerEntity!.Config);

            if (result.Success)
            {
                // 发送成功
                await HandleSendSuccessAsync(message, result, usageService, repository);
            }
            else
            {
                // 发送失败
                await HandleSendFailureAsync(message, result.ErrorMessage ?? "发送失败", repository);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理消息时发生异常，消息ID：{MessageId}", message.Key);
            await HandleSendFailureAsync(message, $"处理异常：{ex.Message}", repository);
        }
    }

    /// <summary>
    /// 渲染消息内容
    /// </summary>
    private async Task<(string title, string content)?> RenderMessageContentAsync(MessageSendEntity message,
        IMessageTemplateService templateService)
    {
        try
        {
            // 获取模板
            var template = await GetTemplateByCodeAsync(templateService, message.TemplateCode, message.AppCode);
            if (template == null)
            {
                _logger.LogWarning("未找到模板，模板编码：{TemplateCode}，应用编码：{AppCode}",
                    message.TemplateCode, message.AppCode);
                return null;
            }

            // 检查模板是否启用
            if (!template.IsEnabled)
            {
                _logger.LogWarning("模板已禁用，模板编码：{TemplateCode}，应用编码：{AppCode}",
                    message.TemplateCode, message.AppCode);
                return null;
            }

            // 检查模板类型是否匹配
            if (!string.Equals(template.TemplateType, message.MessageType, StringComparison.OrdinalIgnoreCase))
            {
                _logger.LogWarning("模板类型不匹配，模板类型：{TemplateType}，消息类型：{MessageType}",
                    template.TemplateType, message.MessageType);
                return null;
            }

            var title = template.Title ?? string.Empty;

            var content = template.Content;

            // 解析并替换变量
            if (!string.IsNullOrEmpty(message.VariablesJson))
            {
                title = await ReplaceVariablesAsync(title, message.VariablesJson);
                content = await ReplaceVariablesAsync(content, message.VariablesJson);
            }

            return (title, content);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "渲染消息内容时发生异常，模板编码：{TemplateCode}", message.TemplateCode);
            return null;
        }
    }

    /// <summary>
    /// 替换模板变量
    /// </summary>
    /// <param name="content">模板内容</param>
    /// <param name="variablesJson">变量JSON</param>
    /// <returns>替换后的内容</returns>
    private async Task<string> ReplaceVariablesAsync(string content, string variablesJson)
    {
        try
        {
            // 解析JSON变量
            var variables = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(variablesJson);
            if (variables == null || !variables.Any())
            {
                return content;
            }

            var result = content;

            // 简单的变量替换（支持 {{变量名}} 格式）
            foreach (var variable in variables)
            {
                var placeholder = $"{{{{{variable.Key}}}}}";
                var value = variable.Value?.ToString() ?? "";
                result = result.Replace(placeholder, value);
            }

            // 也支持 ${变量名} 格式
            foreach (var variable in variables)
            {
                var placeholder = $"${{{variable.Key}}}";
                var value = variable.Value?.ToString() ?? "";
                result = result.Replace(placeholder, value);
            }

            return await Task.FromResult(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "替换模板变量时发生异常，变量JSON：{VariablesJson}", variablesJson);
            return content; // 返回原始内容
        }
    }

    /// <summary>
    /// 根据模板编码获取模板
    /// </summary>
    private async Task<MessageTemplateDto?> GetTemplateByCodeAsync(IMessageTemplateService templateService,
        string templateCode, string appCode)
    {
        return await templateService.GetByCodeAsync(templateCode, appCode);
    }

    /// <summary>
    /// 处理发送成功
    /// </summary>
    private async Task HandleSendSuccessAsync(
        MessageSendEntity message,
        MessageProviderResult result,
        IMessageAccountUsageService usageService,
        IMessageSendRepository repository)
    {
        try
        {
            // 更新消息状态
            message.Status = 2; // 发送成功
            message.SendTime = DateTimeOffset.Now;
            message.ErrorInfo = null;
            await repository.UpdateAsync(message);

            // 记录用量
            await RecordUsageAsync(message, usageService);

            _logger.LogInformation("消息发送成功，ID：{MessageId}，目标：{Target}，服务商消息ID：{ProviderMessageId}",
                message.Key, message.Target, result.ProviderMessageId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理发送成功时发生异常，消息ID：{MessageId}", message.Key);
        }
    }

    /// <summary>
    /// 处理发送失败
    /// </summary>
    private async Task HandleSendFailureAsync(
        MessageSendEntity message,
        string errorMessage,
        IMessageSendRepository repository)
    {
        try
        {
            message.TryTime++;
            message.ErrorInfo = errorMessage;

            if (message.TryTime >= _maxRetryTimes)
            {
                // 超过最大重试次数，标记为失败
                message.Status = 3; // 发送失败
                _logger.LogWarning("消息发送失败，已达最大重试次数，ID：{MessageId}，错误：{Error}",
                    message.Key, errorMessage);
            }
            else
            {
                // 重置为待发送状态，等待重试
                message.Status = 0; // 待发送
                _logger.LogWarning("消息发送失败，将重试，ID：{MessageId}，重试次数：{TryTime}，错误：{Error}",
                    message.Key, message.TryTime, errorMessage);
            }

            await repository.UpdateAsync(message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理发送失败时发生异常，消息ID：{MessageId}", message.Key);
        }
    }

    /// <summary>
    /// 记录用量
    /// </summary>
    private async Task RecordUsageAsync(MessageSendEntity message, IMessageAccountUsageService usageService)
    {
        try
        {
            using var scope = _serviceProvider.CreateScope();
            var accountProviderRepository =
                scope.ServiceProvider.GetRequiredService<IMessageAccountProviderRepository>();
            var accountRepository =
                scope.ServiceProvider.GetRequiredService<IMessageAccountRepository>();
            var usageRepository = scope.ServiceProvider.GetRequiredService<IMessageAccountUsageRepository>();
            var unitOfWork = scope.ServiceProvider.GetRequiredService<IUnitOfWork>();

            // 获取账户-服务商配置
            var accountProvider =
                await GetAccountProviderForMessageAsync(message, accountProviderRepository, accountRepository);
            if (accountProvider == null)
            {
                _logger.LogWarning("未找到账户-服务商配置，无法记录用量，消息ID：{MessageId}", message.Key);
                return;
            }

            await unitOfWork.BeginTransactionAsync();

            // 使用行级锁重新获取账户-服务商配置，确保并发安全
            var lockedAccountProvider = await GetAccountProviderWithLockAsync(
                accountProvider.AccountCode, accountProvider.ProviderCode, accountProviderRepository);

            if (lockedAccountProvider == null)
            {
                _logger.LogWarning("无法获取账户-服务商配置的锁，消息ID：{MessageId}", message.Key);
                await unitOfWork.RollbackAsync();
                return;
            }

            // 检查额度是否足够（使用锁定后的最新数据）
            var remainingQuota = (lockedAccountProvider.QuotaTotal ?? 0) - (lockedAccountProvider.QuotaUsed ?? 0);
            if (remainingQuota <= 0)
            {
                _logger.LogWarning("账户额度不足，无法扣减用量，账户：{AccountCode}，服务商：{ProviderCode}，剩余额度：{RemainingQuota}",
                    lockedAccountProvider.AccountCode, lockedAccountProvider.ProviderCode, remainingQuota);
                await unitOfWork.RollbackAsync();
                return;
            }

            // 扣减额度（使用锁定后的数据）
            lockedAccountProvider.QuotaUsed = (lockedAccountProvider.QuotaUsed ?? 0) + 1;
            await accountProviderRepository.UpdateAsync(lockedAccountProvider);

            // 记录用量（使用锁定后的数据）
            var usageRecord = new MessageAccountUsageEntity
            {
                AccountCode = lockedAccountProvider.AccountCode,
                ProviderCode = lockedAccountProvider.ProviderCode,
                MessageType = message.MessageType,
                Amount = 1,
                BalanceAfter = (lockedAccountProvider.QuotaTotal ?? 0) - (lockedAccountProvider.QuotaUsed ?? 0),
                Key = scope.ServiceProvider.GetRequiredService<IKeyGenerator<long>>().GenerateKey(),
                AccountProviderId = lockedAccountProvider.Key,
                MessageSendId = message.Key,
                UsageTime = DateTimeOffset.Now,
                UsageType = message.MessageType,
                Target = message.Target,
                TemplateCode = message.TemplateCode,
                AppCode = message.AppCode
            };

            await usageRepository.InsertAsync(usageRecord);

            await unitOfWork.CommitAsync();

            _logger.LogInformation(
                "用量记录成功，消息ID：{MessageId}，账户：{AccountCode}，服务商：{ProviderCode}，扣减前剩余额度：{BeforeQuota}，扣减后剩余额度：{AfterQuota}",
                message.Key, lockedAccountProvider.AccountCode, lockedAccountProvider.ProviderCode,
                remainingQuota, usageRecord.BalanceAfter);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "记录用量时发生异常，消息ID：{MessageId}", message.Key);
        }
    }

    /// <summary>
    /// 获取消息对应的账户-服务商配置
    /// </summary>
    private async Task<MessageAccountProviderEntity?> GetAccountProviderForMessageAsync(
        MessageSendEntity message,
        IMessageAccountProviderRepository accountProviderRepository,
        IMessageAccountRepository accountRepository
    )
    {
        try
        {
            // 如果指定了服务商编码，优先使用指定的服务商
            if (!string.IsNullOrEmpty(message.ProviderCode))
            {
                var specificProvider = await accountProviderRepository.GetByAccountAndProviderAsync(
                    message.AppCode, message.ProviderCode);
                if (specificProvider is { IsEnabled: true })
                {
                    return specificProvider;
                }
            }

            var account = await accountRepository.GetByAppCodeAsync(message.AppCode);
            if (account == null)
            {
                _logger.LogError("通过AppCode：{AppCode}未找到有效账户", message.AppCode);
                return null;
            }

            // 获取应用的所有账户-服务商配置
            var accountProviders = await accountProviderRepository.GetByAccountCodeAsync(account.AccountCode);

            // 筛选启用的、类型匹配的配置
            var availableProviders = accountProviders
                .Where(x => x.IsEnabled &&
                            string.Equals(x.ProviderType, message.MessageType, StringComparison.OrdinalIgnoreCase) &&
                            (x.QuotaTotal ?? 0) > (x.QuotaUsed ?? 0)) // 有剩余额度
                .OrderBy(x => x.Priority) // 按优先级排序
                .ThenBy(x => x.QuotaUsed) // 然后按已用额度升序排序（优先使用用量少的）
                .ToList();

            return availableProviders.FirstOrDefault();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取账户-服务商配置时发生异常，消息ID：{MessageId}", message.Key);
            return null;
        }
    }

    /// <summary>
    /// 验证消息基本信息
    /// </summary>
    private async Task<ValidationResult> ValidateMessageAsync(MessageSendEntity message)
    {
        try
        {
            // 验证必填字段
            if (string.IsNullOrWhiteSpace(message.AppCode))
            {
                return ValidationResult.Failure("应用编码不能为空");
            }

            if (string.IsNullOrWhiteSpace(message.Target))
            {
                return ValidationResult.Failure("发送目标不能为空");
            }

            if (string.IsNullOrWhiteSpace(message.TemplateCode))
            {
                return ValidationResult.Failure("模板编码不能为空");
            }

            if (string.IsNullOrWhiteSpace(message.MessageType))
            {
                return ValidationResult.Failure("消息类型不能为空");
            }

            // // 验证目标格式
            // var targetValidationResult = ValidateTarget(message.Target, message.MessageType);
            // if (!targetValidationResult.IsValid)
            // {
            //     return targetValidationResult;
            // }

            return await Task.FromResult(ValidationResult.Success());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "验证消息时发生异常，消息ID：{MessageId}", message.Key);
            return ValidationResult.Failure($"验证异常：{ex.Message}");
        }
    }

    // /// <summary>
    // /// 验证发送目标格式
    // /// </summary>
    // private ValidationResult ValidateTarget(string target, string messageType)
    // {
    //     switch (messageType.ToLower())
    //     {
    //         case "sms":
    //             if (!IsValidPhoneNumber(target))
    //             {
    //                 return ValidationResult.Failure("无效的手机号格式");
    //             }
    //
    //             break;
    //         case "email":
    //             if (!IsValidEmail(target))
    //             {
    //                 return ValidationResult.Failure("无效的邮箱地址格式");
    //             }
    //
    //             break;
    //         default:
    //             // 其他类型暂不验证
    //             break;
    //     }
    //
    //     return ValidationResult.Success();
    // }


    /// <summary>
    /// 检查账户额度
    /// </summary>
    private async Task<ValidationResult> CheckAccountQuotaAsync(MessageSendEntity message)
    {
        try
        {
            using var scope = _serviceProvider.CreateScope();
            var accountProviderRepository =
                scope.ServiceProvider.GetRequiredService<IMessageAccountProviderRepository>();

            var accountRepository =
                scope.ServiceProvider.GetRequiredService<IMessageAccountRepository>();

            var accountProvider =
                await GetAccountProviderForMessageAsync(message, accountProviderRepository, accountRepository);
            if (accountProvider == null)
            {
                return ValidationResult.Failure("未找到可用的账户-服务商配置");
            }

            var remainingQuota = (accountProvider.QuotaTotal ?? 0) - (accountProvider.QuotaUsed ?? 0);
            if (remainingQuota <= 0)
            {
                return ValidationResult.Failure($"账户额度不足，剩余额度：{remainingQuota}");
            }

            return ValidationResult.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查账户额度时发生异常，消息ID：{MessageId}", message.Key);
            return ValidationResult.Failure($"额度检查异常：{ex.Message}");
        }
    }

    /// <summary>
    /// 使用行级锁获取账户-服务商配置
    /// </summary>
    private async Task<MessageAccountProviderEntity?> GetAccountProviderWithLockAsync(
        string accountCode,
        string providerCode,
        IMessageAccountProviderRepository repository)
    {
        try
        {
            // 使用 FOR UPDATE 锁定行，确保并发安全
            var accountProvider = await repository.GetByAccountAndProviderWithLockAsync(accountCode, providerCode);

            if (accountProvider == null)
            {
                _logger.LogWarning("未找到账户-服务商配置，账户：{AccountCode}，服务商：{ProviderCode}",
                    accountCode, providerCode);
                return null;
            }

            if (!accountProvider.IsEnabled)
            {
                _logger.LogWarning("账户-服务商配置已禁用，账户：{AccountCode}，服务商：{ProviderCode}",
                    accountCode, providerCode);
                return null;
            }

            return accountProvider;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取账户-服务商配置锁时发生异常，账户：{AccountCode}，服务商：{ProviderCode}",
                accountCode, providerCode);
            return null;
        }
    }

    /// <summary>
    /// 按账户分组处理消息，确保同一账户的消息串行处理用量扣减
    /// </summary>
    private async Task ProcessMessagesByAccountAsync(List<MessageSendEntity> messages,
        CancellationToken cancellationToken)
    {
        // 按账户编码分组
        var messagesByAccount = messages.GroupBy(m => m.AppCode).ToList();

        _logger.LogInformation("按账户分组处理消息，共 {AccountCount} 个账户，{MessageCount} 条消息",
            messagesByAccount.Count, messages.Count);

        // 并行处理不同账户的消息，但同一账户内的消息串行处理
        var accountTasks = messagesByAccount.Select(async accountGroup =>
        {
            var accountCode = accountGroup.Key;
            var accountMessages = accountGroup.ToList();

            // 获取或创建该账户的信号量（确保同一账户的消息串行处理）
            var accountSemaphore = GetAccountSemaphore(accountCode);

            await accountSemaphore.WaitAsync(cancellationToken);
            try
            {
                _logger.LogDebug("开始处理账户 {AccountCode} 的 {MessageCount} 条消息", accountCode, accountMessages.Count);

                // 同一账户内的消息串行处理，确保用量扣减的准确性
                foreach (var message in accountMessages)
                {
                    if (cancellationToken.IsCancellationRequested)
                        break;

                    try
                    {
                        // 为每个消息创建独立的服务作用域
                        using var messageScope = _serviceProvider.CreateScope();
                        var messageSendRepository =
                            messageScope.ServiceProvider.GetRequiredService<IMessageSendRepository>();
                        var providerRepository =
                            messageScope.ServiceProvider.GetRequiredService<IMessageProviderRepository>();
                        var messageProviderFactory =
                            messageScope.ServiceProvider.GetRequiredService<IMessageProviderFactory>();
                        var messageTemplateService =
                            messageScope.ServiceProvider.GetRequiredService<IMessageTemplateService>();
                        var messageAccountUsageService =
                            messageScope.ServiceProvider.GetRequiredService<IMessageAccountUsageService>();

                        var messageAccountProviderRepository =
                            messageScope.ServiceProvider.GetRequiredService<IMessageAccountProviderRepository>();
                        var messageAccountRepository =
                            messageScope.ServiceProvider.GetRequiredService<IMessageAccountRepository>();

                        await ProcessSingleMessageAsync(message, messageAccountRepository,
                            messageAccountProviderRepository, providerRepository, messageProviderFactory,
                            messageTemplateService,
                            messageAccountUsageService, messageSendRepository);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "处理消息 {MessageId} 时发生异常", message.Key);
                    }
                }

                _logger.LogDebug("完成处理账户 {AccountCode} 的消息", accountCode);
            }
            finally
            {
                accountSemaphore.Release();
            }
        });

        await Task.WhenAll(accountTasks);
    }

    /// <summary>
    /// 获取或创建账户级别的信号量
    /// </summary>
    private SemaphoreSlim GetAccountSemaphore(string accountCode)
    {
        return _accountSemaphores.GetOrAdd(accountCode, _ => new SemaphoreSlim(1, 1));
    }

    /// <summary>
    /// 清理不再使用的账户信号量
    /// </summary>
    private void CleanupAccountSemaphores(object? state)
    {
        try
        {
            var keysToRemove = new List<string>();

            foreach (var kvp in _accountSemaphores)
            {
                var semaphore = kvp.Value;

                // 如果信号量当前可用（没有被占用），则可以安全移除
                if (semaphore.CurrentCount > 0)
                {
                    keysToRemove.Add(kvp.Key);
                }
            }

            foreach (var key in keysToRemove)
            {
                if (_accountSemaphores.TryRemove(key, out var removedSemaphore))
                {
                    removedSemaphore.Dispose();
                }
            }

            if (keysToRemove.Count > 0)
            {
                _logger.LogDebug("清理了 {Count} 个不再使用的账户信号量", keysToRemove.Count);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "清理账户信号量时发生异常");
        }
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public override void Dispose()
    {
        _cleanupTimer?.Dispose();

        // 释放所有账户信号量
        foreach (var semaphore in _accountSemaphores.Values)
        {
            semaphore.Dispose();
        }

        _accountSemaphores.Clear();

        base.Dispose();
    }

    /// <summary>
    /// 验证结果
    /// </summary>
    private class ValidationResult
    {
        public bool IsValid { get; private set; }
        public string ErrorMessage { get; private set; }

        private ValidationResult(bool isValid, string errorMessage = "")
        {
            IsValid = isValid;
            ErrorMessage = errorMessage;
        }

        public static ValidationResult Success()
        {
            return new ValidationResult(true);
        }

        public static ValidationResult Failure(string errorMessage)
        {
            return new ValidationResult(false, errorMessage);
        }
    }
}
