namespace XJ.Framework.Messaging.Application.Contract.OperationDtos;

/// <summary>
/// MessageProvider 操作 DTO
/// </summary>
public class MessageProviderOperationDto : BaseOperationDto
{
    /// <summary>
    /// 服务商编码
    /// </summary>
    public string ProviderCode { get; set; } = null!;

    /// <summary>
    /// 服务商名称
    /// </summary>
    public string ProviderName { get; set; } = null!;

    /// <summary>
    /// 服务商类型
    /// </summary>
    public string ProviderType { get; set; } = null!;

    /// <summary>
    /// 服务商配置
    /// </summary>
    public string Config { get; set; } = null!;

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; set; }
}
