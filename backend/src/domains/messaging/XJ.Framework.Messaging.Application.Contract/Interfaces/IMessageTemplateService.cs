
using XJ.Framework.Messaging.Domain.Shared.Dtos;

namespace XJ.Framework.Messaging.Application.Contract.Interfaces;

/// <summary>
/// MessageTemplate 服务接口
/// </summary>
public interface IMessageTemplateService :
    IAppService<long, MessageTemplateDto, MessageTemplateQueryCriteria>,
    IEditableAppService<long, MessageTemplateOperationDto>
{
    /// <summary>
    /// 根据模板编码和应用编码获取模板
    /// </summary>
    /// <param name="templateCode">模板编码</param>
    /// <param name="appCode">应用编码</param>
    /// <returns>模板信息</returns>
    Task<MessageTemplateDto?> GetByCodeAsync(string templateCode, string appCode);

    /// <summary>
    /// 根据应用编码获取模板列表
    /// </summary>
    /// <param name="appCode">应用编码</param>
    /// <returns>模板列表</returns>
    Task<List<MessageTemplateDto>> GetByAppCodeAsync(string appCode);

    /// <summary>
    /// 启用/禁用模板
    /// </summary>
    /// <param name="id">模板ID</param>
    /// <param name="isEnabled">是否启用</param>
    /// <returns>操作结果</returns>
    Task<bool> SetEnabledAsync(long id, bool isEnabled);
}
