
using XJ.Framework.Messaging.Domain.Shared.Dtos;

namespace XJ.Framework.Messaging.Application.Contract.Interfaces;

/// <summary>
/// MessageProvider 服务接口
/// </summary>
public interface IMessageProviderService :
    IAppService<long, MessageProviderDto, MessageProviderQueryCriteria>,
    IEditableAppService<long, MessageProviderOperationDto>
{
    /// <summary>
    /// 根据服务商编码获取服务商
    /// </summary>
    /// <param name="providerCode">服务商编码</param>
    /// <returns>服务商信息</returns>
    Task<MessageProviderDto?> GetByCodeAsync(string providerCode);

    /// <summary>
    /// 根据服务商类型获取服务商列表
    /// </summary>
    /// <param name="providerType">服务商类型</param>
    /// <returns>服务商列表</returns>
    Task<List<MessageProviderDto>> GetByTypeAsync(string providerType);

    /// <summary>
    /// 启用/禁用服务商
    /// </summary>
    /// <param name="id">服务商ID</param>
    /// <param name="isEnabled">是否启用</param>
    /// <returns>操作结果</returns>
    Task<bool> SetEnabledAsync(long id, bool isEnabled);

    /// <summary>
    /// 获取所有启用的服务商
    /// </summary>
    /// <returns>启用的服务商列表</returns>
    Task<List<MessageProviderDto>> GetEnabledProvidersAsync();
}
