namespace XJ.Framework.Messaging.Application.Contract.Interfaces;

/// <summary>
/// 消息发送Provider接口
/// </summary>
public interface IMessageProvider
{
    /// <summary>
    /// Provider类型（sms、email等）
    /// </summary>
    string ProviderType { get; }

    /// <summary>
    /// 发送消息
    /// </summary>
    /// <param name="target">发送目标（手机号、邮箱等）</param>
    /// <param name="content">消息内容</param>
    /// <param name="providerConfig">服务商配置（JSON格式）</param>
    /// <returns>发送结果</returns>
    Task<MessageProviderResult> SendAsync(string target, string content, string? providerConfig = null);

    /// <summary>
    /// 验证配置是否有效
    /// </summary>
    /// <param name="providerConfig">服务商配置</param>
    /// <returns>是否有效</returns>
    bool ValidateConfig(string? providerConfig);
}

/// <summary>
/// 消息发送结果
/// </summary>
public class MessageProviderResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 服务商返回的消息ID
    /// </summary>
    public string? ProviderMessageId { get; set; }

    /// <summary>
    /// 额外信息
    /// </summary>
    public string? AdditionalInfo { get; set; }

    /// <summary>
    /// 创建成功结果
    /// </summary>
    public static MessageProviderResult CreateSuccess(string? providerMessageId = null, string? additionalInfo = null)
    {
        return new MessageProviderResult
        {
            Success = true,
            ProviderMessageId = providerMessageId,
            AdditionalInfo = additionalInfo
        };
    }

    /// <summary>
    /// 创建失败结果
    /// </summary>
    public static MessageProviderResult CreateFailure(string errorMessage, string? additionalInfo = null)
    {
        return new MessageProviderResult
        {
            Success = false,
            ErrorMessage = errorMessage,
            AdditionalInfo = additionalInfo
        };
    }
}
