
using XJ.Framework.Messaging.Domain.Shared.Dtos;

namespace XJ.Framework.Messaging.Application.Contract.Interfaces;

/// <summary>
/// MessageSend 服务接口
/// </summary>
public interface IMessageSendService :
    IAppService<long, MessageSendDto, MessageSendQueryCriteria>,
    IEditableAppService<long, MessageSendOperationDto>
{
    /// <summary>
    /// 发送消息
    /// </summary>
    /// <param name="request">消息发送请求</param>
    /// <returns>发送结果</returns>
    Task<MessageSendResultDto> SendMessageAsync(MessageSendRequestDto request);

    /// <summary>
    /// 获取发送状态
    /// </summary>
    /// <param name="sendId">发送记录ID</param>
    /// <returns>发送状态信息</returns>
    Task<MessageSendResultDto?> GetSendStatusAsync(long sendId);
}
