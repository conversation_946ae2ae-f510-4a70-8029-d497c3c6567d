using XJ.Framework.Messaging.Domain.Shared.Dtos;

namespace XJ.Framework.Messaging.Application.Contract.Interfaces;

/// <summary>
/// MessageProviderDefault 服务接口
/// </summary>
public interface IMessageProviderDefaultService :
    IAppService<long, MessageProviderDefaultDto, MessageProviderDefaultQueryCriteria>,
    IEditableAppService<long, MessageProviderDefaultOperationDto>
{
    /// <summary>
    /// 根据应用编码和消息类型获取默认服务商
    /// </summary>
    /// <param name="appCode">应用编码</param>
    /// <param name="messageType">消息类型</param>
    /// <returns>默认服务商配置</returns>
    Task<MessageProviderDefaultDto?> GetDefaultProviderAsync(string appCode, string messageType);

    /// <summary>
    /// 根据应用编码获取所有默认服务商配置
    /// </summary>
    /// <param name="appCode">应用编码</param>
    /// <returns>默认服务商配置列表</returns>
    Task<List<MessageProviderDefaultDto>> GetByAppCodeAsync(string appCode);

    /// <summary>
    /// 设置应用的默认服务商（同一个appCode+messageType只能有一个默认）
    /// </summary>
    /// <param name="appCode">应用编码</param>
    /// <param name="messageType">消息类型</param>
    /// <param name="providerCode">服务商编码</param>
    /// <returns>设置结果</returns>
    Task<bool> SetDefaultProviderAsync(string appCode, string messageType, string providerCode);

    /// <summary>
    /// 删除应用的默认服务商配置
    /// </summary>
    /// <param name="appCode">应用编码</param>
    /// <param name="messageType">消息类型</param>
    /// <returns>删除结果</returns>
    Task<bool> RemoveDefaultProviderAsync(string appCode, string messageType);
}
