namespace XJ.Framework.Messaging.Application.Contract.Interfaces;

/// <summary>
/// 消息Provider工厂接口
/// </summary>
public interface IMessageProviderFactory
{
    /// <summary>
    /// 根据消息类型获取Provider
    /// </summary>
    /// <param name="messageType">消息类型（sms、email等）</param>
    /// <returns>Provider实例</returns>
    IMessageProvider? GetProvider(string messageType);

    /// <summary>
    /// 获取所有支持的消息类型
    /// </summary>
    /// <returns>消息类型列表</returns>
    IEnumerable<string> GetSupportedMessageTypes();
}
