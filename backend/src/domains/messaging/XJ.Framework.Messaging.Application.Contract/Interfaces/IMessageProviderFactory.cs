using XJ.Framework.Messaging.Domain.Shared.Enums;

namespace XJ.Framework.Messaging.Application.Contract.Interfaces;

/// <summary>
/// 消息Provider工厂接口
/// </summary>
public interface IMessageProviderFactory
{
    /// <summary>
    /// 根据消息类型获取Provider
    /// </summary>
    /// <param name="messageType">消息类型</param>
    /// <param name="providerName">服务商实例名称，如CommonEmail、AliyunSms</param>
    /// <returns>Provider实例</returns>
    IMessageProvider? GetProvider(MessageType messageType, string providerName);

    /// <summary>
    /// 根据消息类型字符串获取Provider
    /// </summary>
    /// <param name="messageTypeString">消息类型字符串（sms、email等）</param>
    /// <param name="providerName">服务商实例名称，如CommonEmail、AliyunSms</param>
    /// <returns>Provider实例</returns>
    IMessageProvider? GetProvider(string messageTypeString, string providerName);

    Dictionary<string, List<string>> GetProviderNames();

    /// <summary>
    /// 获取所有支持的消息类型
    /// </summary>
    /// <returns>消息类型列表</returns>
    IEnumerable<MessageType> GetSupportedMessageTypes();
}
