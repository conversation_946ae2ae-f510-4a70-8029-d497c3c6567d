
using XJ.Framework.Messaging.Domain.Shared.Dtos;

namespace XJ.Framework.Messaging.Application.Contract.Interfaces;

/// <summary>
/// MessageAccount 服务接口
/// </summary>
public interface IMessageAccountService :
    IAppService<long, MessageAccountDto, MessageAccountQueryCriteria>,
    IEditableAppService<long, MessageAccountOperationDto>
{
    /// <summary>
    /// 根据账户编码获取账户
    /// </summary>
    /// <param name="accountCode">账户编码</param>
    /// <returns>账户信息</returns>
    Task<MessageAccountDto?> GetByCodeAsync(string accountCode);

    /// <summary>
    /// 根据应用编码获取账户列表
    /// </summary>
    /// <param name="appCode">应用编码</param>
    /// <returns>账户列表</returns>
    Task<List<MessageAccountDto>> GetByAppCodeAsync(string appCode);

    /// <summary>
    /// 启用/禁用账户
    /// </summary>
    /// <param name="id">账户ID</param>
    /// <param name="isEnabled">是否启用</param>
    /// <returns>操作结果</returns>
    Task<bool> SetEnabledAsync(long id, bool isEnabled);

    /// <summary>
    /// 账户充值
    /// </summary>
    /// <param name="accountCode">账户编码</param>
    /// <param name="amount">充值金额</param>
    /// <param name="operatorId">操作人ID</param>
    /// <param name="remark">备注</param>
    /// <returns>充值结果</returns>
    Task<bool> RechargeAsync(string accountCode, int amount, long operatorId, string? remark = null);

    /// <summary>
    /// 获取账户余额（按服务商分组）
    /// </summary>
    /// <param name="accountCode">账户编码</param>
    /// <returns>余额信息列表</returns>
    Task<List<AccountProviderBalanceDto>> GetBalanceByProviderAsync(string accountCode);

    /// <summary>
    /// 获取账户在特定服务商的余额
    /// </summary>
    /// <param name="accountCode">账户编码</param>
    /// <param name="providerCode">服务商编码</param>
    /// <returns>余额信息</returns>
    Task<AccountProviderBalanceDto?> GetBalanceAsync(string accountCode, string providerCode);

    /// <summary>
    /// 获取账户总体余额汇总
    /// </summary>
    /// <param name="accountCode">账户编码</param>
    /// <returns>汇总余额信息</returns>
    Task<AccountBalanceSummaryDto?> GetBalanceSummaryAsync(string accountCode);
}

/// <summary>
/// 账户-服务商余额DTO
/// </summary>
public class AccountProviderBalanceDto
{
    /// <summary>
    /// 账户编码
    /// </summary>
    public string AccountCode { get; set; } = null!;

    /// <summary>
    /// 服务商编码
    /// </summary>
    public string ProviderCode { get; set; } = null!;

    /// <summary>
    /// 消息类型
    /// </summary>
    public string MessageType { get; set; } = null!;

    /// <summary>
    /// 总额度
    /// </summary>
    public int TotalQuota { get; set; }

    /// <summary>
    /// 已用额度
    /// </summary>
    public int UsedQuota { get; set; }

    /// <summary>
    /// 剩余额度
    /// </summary>
    public int RemainingQuota => TotalQuota - UsedQuota;

    /// <summary>
    /// 优先级
    /// </summary>
    public int Priority { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; set; }
}

/// <summary>
/// 账户余额汇总DTO
/// </summary>
public class AccountBalanceSummaryDto
{
    /// <summary>
    /// 账户编码
    /// </summary>
    public string AccountCode { get; set; } = null!;

    /// <summary>
    /// 总额度
    /// </summary>
    public int TotalQuota { get; set; }

    /// <summary>
    /// 已用额度
    /// </summary>
    public int UsedQuota { get; set; }

    /// <summary>
    /// 剩余额度
    /// </summary>
    public int RemainingQuota => TotalQuota - UsedQuota;

    /// <summary>
    /// 服务商数量
    /// </summary>
    public int ProviderCount { get; set; }

    /// <summary>
    /// 启用的服务商数量
    /// </summary>
    public int EnabledProviderCount { get; set; }
}
