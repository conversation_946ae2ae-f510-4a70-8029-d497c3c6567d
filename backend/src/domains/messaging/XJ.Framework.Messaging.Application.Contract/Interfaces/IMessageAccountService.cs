
using XJ.Framework.Messaging.Domain.Shared.Dtos;

namespace XJ.Framework.Messaging.Application.Contract.Interfaces;

/// <summary>
/// MessageAccount 服务接口
/// </summary>
public interface IMessageAccountService :
    IAppService<long, MessageAccountDto, MessageAccountQueryCriteria>,
    IEditableAppService<long, MessageAccountOperationDto>
{
    /// <summary>
    /// 根据账户编码获取账户
    /// </summary>
    /// <param name="accountCode">账户编码</param>
    /// <returns>账户信息</returns>
    Task<MessageAccountDto?> GetByCodeAsync(string accountCode);

    /// <summary>
    /// 根据应用编码获取账户列表
    /// </summary>
    /// <param name="appCode">应用编码</param>
    /// <returns>账户列表</returns>
    Task<List<MessageAccountDto>> GetByAppCodeAsync(string appCode);

    /// <summary>
    /// 启用/禁用账户
    /// </summary>
    /// <param name="id">账户ID</param>
    /// <param name="isEnabled">是否启用</param>
    /// <returns>操作结果</returns>
    Task<bool> SetEnabledAsync(long id, bool isEnabled);

    /// <summary>
    /// 账户充值
    /// </summary>
    /// <param name="accountCode">账户编码</param>
    /// <param name="amount">充值金额</param>
    /// <param name="operatorId">操作人ID</param>
    /// <param name="remark">备注</param>
    /// <returns>充值结果</returns>
    Task<bool> RechargeAsync(string accountCode, int amount, long operatorId, string? remark = null);

    /// <summary>
    /// 获取账户余额
    /// </summary>
    /// <param name="accountCode">账户编码</param>
    /// <returns>余额信息</returns>
    Task<AccountBalanceDto?> GetBalanceAsync(string accountCode);
}

/// <summary>
/// 账户余额DTO
/// </summary>
public class AccountBalanceDto
{
    /// <summary>
    /// 账户编码
    /// </summary>
    public string AccountCode { get; set; } = null!;

    /// <summary>
    /// 总额度
    /// </summary>
    public int TotalQuota { get; set; }

    /// <summary>
    /// 已用额度
    /// </summary>
    public int UsedQuota { get; set; }

    /// <summary>
    /// 剩余额度
    /// </summary>
    public int RemainingQuota => TotalQuota - UsedQuota;
}
