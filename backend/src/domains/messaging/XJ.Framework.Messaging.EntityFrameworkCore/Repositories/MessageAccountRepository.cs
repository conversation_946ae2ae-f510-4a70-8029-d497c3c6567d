
using Microsoft.EntityFrameworkCore;

namespace XJ.Framework.Messaging.EntityFrameworkCore.Repositories;


/// <summary>
/// MessageAccount 仓储实现
/// </summary>
public class MessageAccountRepository : BaseSoftDeleteRepository<MessagingDbContext, long, MessageAccountEntity>, IMessageAccountRepository
{
    public MessageAccountRepository(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }

    /// <summary>
    /// 根据账户编码获取账户
    /// </summary>
    /// <param name="accountCode">账户编码</param>
    /// <returns>账户实体</returns>
    public async Task<MessageAccountEntity?> GetByCodeAsync(string accountCode)
    {
        return await DbSet
            .FirstOrDefaultAsync(x => x.AccountCode == accountCode);
    }

    /// <summary>
    /// 根据应用编码获取账户列表
    /// </summary>
    /// <param name="appCode">应用编码</param>
    /// <returns>账户列表</returns>
    public async Task<List<MessageAccountEntity>> GetByAppCodeAsync(string appCode)
    {
        return await DbSet
            .Where(x => x.AppCode == appCode)
            .OrderBy(x => x.AccountCode)
            .ToListAsync();
    }
}
