using Microsoft.EntityFrameworkCore;

namespace XJ.Framework.Messaging.EntityFrameworkCore.Repositories;

/// <summary>
/// MessageProviderDefault 仓储实现
/// </summary>
public class MessageProviderDefaultRepository :
    BaseSoftDeleteRepository<MessagingDbContext, long, MessageProviderDefaultEntity>, IMessageProviderDefaultRepository
{
    public MessageProviderDefaultRepository(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }

    /// <summary>
    /// 根据应用编码和消息类型获取默认服务商
    /// </summary>
    /// <param name="appCode">应用编码</param>
    /// <param name="messageType">消息类型</param>
    /// <returns>默认服务商配置</returns>
    public async Task<MessageProviderDefaultEntity?> GetDefaultProviderAsync(string appCode, string messageType)
    {
        return await DbSet
            .Where(x => x.AppCode == appCode && x.MessageType == messageType && x.IsEnabled)
            .OrderByDescending(x => x.Priority) // 按优先级降序排序
            .ThenBy(x => x.CreateTime) // 然后按创建时间升序排序
            .FirstOrDefaultAsync();
    }

    /// <summary>
    /// 根据应用编码获取所有默认服务商配置
    /// </summary>
    /// <param name="appCode">应用编码</param>
    /// <returns>默认服务商配置列表</returns>
    public async Task<List<MessageProviderDefaultEntity>> GetByAppCodeAsync(string appCode)
    {
        return await DbSet
            .Where(x => x.AppCode == appCode)
            .OrderBy(x => x.ProviderType)
            .ToListAsync();
    }

    /// <summary>
    /// 根据应用编码和消息类型获取所有默认服务商（按优先级排序）
    /// </summary>
    /// <param name="appCode">应用编码</param>
    /// <param name="messageType">消息类型</param>
    /// <returns>默认服务商列表</returns>
    public async Task<List<MessageProviderDefaultEntity>> GetDefaultProvidersByTypeAsync(string appCode,
        string messageType)
    {
        return await DbSet
            .Where(x => x.AppCode == appCode && x.ProviderType == messageType)
            .ThenBy(x => x.CreateTime)
            .ToListAsync();
    }

    /// <summary>
    /// 检查默认服务商配置是否存在
    /// </summary>
    /// <param name="appCode">应用编码</param>
    /// <param name="messageType">消息类型</param>
    /// <param name="providerCode">服务商编码</param>
    /// <returns>是否存在</returns>
    public async Task<bool> ExistsAsync(string appCode, string messageType, string providerCode)
    {
        return await DbSet
            .AnyAsync(x => x.AppCode == appCode && x.ProviderType == messageType && x.ProviderCode == providerCode);
    }
}
