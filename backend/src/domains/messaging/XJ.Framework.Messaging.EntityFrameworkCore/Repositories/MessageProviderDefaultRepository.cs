
namespace XJ.Framework.Messaging.EntityFrameworkCore.Repositories;


/// <summary>
/// MessageProviderDefault 仓储实现
/// </summary>
public class MessageProviderDefaultRepository : BaseSoftDeleteRepository<MessagingDbContext, long, MessageProviderDefaultEntity>, IMessageProviderDefaultRepository
{
    public MessageProviderDefaultRepository(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }
}
