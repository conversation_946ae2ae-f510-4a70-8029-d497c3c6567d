
using Microsoft.EntityFrameworkCore;

namespace XJ.Framework.Messaging.EntityFrameworkCore.Repositories;


/// <summary>
/// MessageSend 仓储实现
/// </summary>
public class MessageSendRepository : BaseSoftDeleteRepository<MessagingDbContext, long, MessageSendEntity>, IMessageSendRepository
{
    public MessageSendRepository(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }

    /// <summary>
    /// 获取待发送的消息
    /// </summary>
    /// <param name="maxCount">最大数量</param>
    /// <param name="maxRetryTimes">最大重试次数</param>
    /// <returns>待发送消息列表</returns>
    public async Task<List<MessageSendEntity>> GetPendingMessagesAsync(int maxCount = 50, int maxRetryTimes = 3)
    {
        return await DbSet
            .Where(x => x.Status == 0 && x.TryTime < maxRetryTimes && !x.Deleted) // 状态为待发送且未超过最大重试次数
            .OrderBy(x => x.Priority) // 按优先级排序（数值越小优先级越高）
            .ThenBy(x => x.CreateTime) // 然后按创建时间排序
            .Take(maxCount)
            .ToListAsync();
    }

    /// <summary>
    /// 根据状态获取消息数量
    /// </summary>
    /// <param name="status">状态</param>
    /// <returns>消息数量</returns>
    public async Task<int> GetCountByStatusAsync(int status)
    {
        return await DbSet.CountAsync(x => x.Status == status && !x.Deleted);
    }
}
