using Microsoft.EntityFrameworkCore;

namespace XJ.Framework.Messaging.EntityFrameworkCore.Repositories;

/// <summary>
/// MessageAccountProvider 仓储实现
/// </summary>
public class MessageAccountProviderRepository :
    BaseSoftDeleteRepository<MessagingDbContext, long, MessageAccountProviderEntity>, IMessageAccountProviderRepository
{
    public MessageAccountProviderRepository(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }

    /// <summary>
    /// 根据账户编码获取账户-服务商配置
    /// </summary>
    /// <param name="accountCode">账户编码</param>
    /// <returns>账户-服务商配置列表</returns>
    public async Task<List<MessageAccountProviderEntity>> GetByAccountCodeAsync(string accountCode)
    {
        return await DbSet
            .Where(x => x.AccountCode == accountCode && !x.Deleted)
            .OrderBy(x => x.ProviderCode)
            .ToListAsync();
    }

    /// <summary>
    /// 根据服务商编码获取账户-服务商配置
    /// </summary>
    /// <param name="providerCode">服务商编码</param>
    /// <returns>账户-服务商配置列表</returns>
    public async Task<List<MessageAccountProviderEntity>> GetByProviderCodeAsync(string providerCode)
    {
        return await DbSet
            .Where(x => x.ProviderCode == providerCode && !x.Deleted)
            .OrderBy(x => x.AccountCode)
            .ToListAsync();
    }

    /// <summary>
    /// 根据账户编码和服务商编码获取配置
    /// </summary>
    /// <param name="accountCode">账户编码</param>
    /// <param name="providerCode">服务商编码</param>
    /// <returns>账户-服务商配置</returns>
    public async Task<MessageAccountProviderEntity?> GetByAccountAndProviderAsync(string accountCode,
        string providerCode)
    {
        return await DbSet
            .FirstOrDefaultAsync(x => x.AccountCode == accountCode && x.ProviderCode == providerCode && !x.Deleted);
    }

    /// <summary>
    /// 根据账户编码和服务商编码获取配置（在事务中获取最新数据）
    /// </summary>
    /// <param name="accountCode">账户编码</param>
    /// <param name="providerCode">服务商编码</param>
    /// <returns>账户-服务商配置</returns>
    public async Task<MessageAccountProviderEntity?> GetByAccountAndProviderWithLockAsync(string accountCode,
        string providerCode)
    {
        // 在事务中重新查询最新数据，确保获取到最新的余额信息
        // 由于应用层已经有账户级信号量控制，这里主要是获取最新数据
        return await DbSet
            .Where(x => x.AccountCode == accountCode && x.ProviderCode == providerCode && !x.Deleted)
            .FirstOrDefaultAsync();
    }
}
