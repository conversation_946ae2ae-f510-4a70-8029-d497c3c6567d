
namespace XJ.Framework.Messaging.EntityFrameworkCore.Repositories;


/// <summary>
/// MessageAccountProvider 仓储实现
/// </summary>
public class MessageAccountProviderRepository : BaseSoftDeleteRepository<MessagingDbContext, long, MessageAccountProviderEntity>, IMessageAccountProviderRepository
{
    public MessageAccountProviderRepository(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }
}
