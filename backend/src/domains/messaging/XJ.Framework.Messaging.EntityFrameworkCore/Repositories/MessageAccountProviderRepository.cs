using Microsoft.EntityFrameworkCore;

namespace XJ.Framework.Messaging.EntityFrameworkCore.Repositories;

/// <summary>
/// MessageAccountProvider 仓储实现
/// </summary>
public class MessageAccountProviderRepository :
    BaseSoftDeleteRepository<MessagingDbContext, long, MessageAccountProviderEntity>, IMessageAccountProviderRepository
{
    public MessageAccountProviderRepository(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }

    /// <summary>
    /// 根据账户编码获取账户-服务商配置
    /// </summary>
    /// <param name="accountCode">账户编码</param>
    /// <returns>账户-服务商配置列表</returns>
    public async Task<List<MessageAccountProviderEntity>> GetByAccountCodeAsync(string accountCode)
    {
        return await DbSet
            .Where(x => x.AccountCode == accountCode && !x.Deleted)
            .OrderBy(x => x.ProviderCode)
            .ToListAsync();
    }

    /// <summary>
    /// 根据服务商编码获取账户-服务商配置
    /// </summary>
    /// <param name="providerCode">服务商编码</param>
    /// <returns>账户-服务商配置列表</returns>
    public async Task<List<MessageAccountProviderEntity>> GetByProviderCodeAsync(string providerCode)
    {
        return await DbSet
            .Where(x => x.ProviderCode == providerCode && !x.Deleted)
            .OrderBy(x => x.AccountCode)
            .ToListAsync();
    }

    /// <summary>
    /// 根据账户编码和服务商编码获取配置
    /// </summary>
    /// <param name="accountCode">账户编码</param>
    /// <param name="providerCode">服务商编码</param>
    /// <returns>账户-服务商配置</returns>
    public async Task<MessageAccountProviderEntity?> GetByAccountAndProviderAsync(string accountCode,
        string providerCode)
    {
        return await DbSet
            .FirstOrDefaultAsync(x => x.AccountCode == accountCode && x.ProviderCode == providerCode && !x.Deleted);
    }

    /// <summary>
    /// 根据账户编码和服务商编码获取配置（使用行级锁）
    /// </summary>
    /// <param name="accountCode">账户编码</param>
    /// <param name="providerCode">服务商编码</param>
    /// <returns>账户-服务商配置</returns>
    public async Task<MessageAccountProviderEntity?> GetByAccountAndProviderWithLockAsync(string accountCode,
        string providerCode)
    {
        // 使用 FromSqlRaw 执行带 FOR UPDATE 的 SQL 查询，实现行级锁
        var sql = @"
            SELECT * FROM m.message_account_provider
            WHERE account_code = {0} AND provider_code = {1} AND is_deleted = 0
            FOR UPDATE";

        var result = await DbSet
            .FromSqlRaw(sql, accountCode, providerCode)
            .FirstOrDefaultAsync();

        return result;
    }
}
