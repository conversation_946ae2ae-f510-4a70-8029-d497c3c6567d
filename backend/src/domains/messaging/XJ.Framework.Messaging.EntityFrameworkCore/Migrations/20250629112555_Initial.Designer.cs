// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using XJ.Framework.Messaging.EntityFrameworkCore;

#nullable disable

namespace XJ.Framework.Messaging.EntityFrameworkCore.Migrations
{
    [DbContext(typeof(MessagingDbContext))]
    [Migration("20250629112555_Initial")]
    partial class Initial
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.11")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("XJ.Framework.Messaging.Domain.Entities.MessageAccountEntity", b =>
                {
                    b.Property<long>("Key")
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    b.Property<string>("AccountCode")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("account_code")
                        .HasComment("账户编码");

                    b.Property<string>("AccountName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("account_name")
                        .HasComment("账户名称");

                    b.Property<string>("AppCode")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("app_code")
                        .HasComment("应用编码");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_time");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit")
                        .HasColumnName("is_deleted");

                    b.Property<bool>("IsEnabled")
                        .HasColumnType("bit")
                        .HasColumnName("is_enabled")
                        .HasComment("是否启用");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("last_modified_by");

                    b.Property<DateTimeOffset?>("LastModifiedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("last_modified_time");

                    b.HasKey("Key");

                    b.HasIndex("AccountCode", "AppCode")
                        .IsUnique()
                        .HasDatabaseName("IX_MessageAccount_AccountCode_AppCode")
                        .HasFilter("[is_deleted] = 0");

                    b.ToTable("message_account", "m", t =>
                        {
                            t.HasComment("MessageAccount 实体");
                        });
                });

            modelBuilder.Entity("XJ.Framework.Messaging.Domain.Entities.MessageAccountProviderEntity", b =>
                {
                    b.Property<long>("Key")
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    b.Property<string>("AccountCode")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("account_code")
                        .HasComment("账户编码");

                    b.Property<string>("ConfigJson")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("config_json")
                        .HasComment("账户专属配置");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_time");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit")
                        .HasColumnName("is_deleted");

                    b.Property<bool>("IsEnabled")
                        .HasColumnType("bit")
                        .HasColumnName("is_enabled")
                        .HasComment("是否启用");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("last_modified_by");

                    b.Property<DateTimeOffset?>("LastModifiedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("last_modified_time");

                    b.Property<int>("Priority")
                        .HasColumnType("int")
                        .HasColumnName("priority")
                        .HasComment("优先级");

                    b.Property<string>("ProviderCode")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("provider_code")
                        .HasComment("服务商编码");

                    b.Property<string>("ProviderType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("provider_type")
                        .HasComment("服务商类型");

                    b.Property<int?>("QuotaTotal")
                        .HasColumnType("int")
                        .HasColumnName("quota_total")
                        .HasComment("总额度");

                    b.Property<int?>("QuotaUsed")
                        .HasColumnType("int")
                        .HasColumnName("quota_used")
                        .HasComment("已用额度");

                    b.HasKey("Key");

                    b.HasIndex("AccountCode", "Priority")
                        .HasDatabaseName("IX_account_provider_priority")
                        .HasFilter("[is_deleted] = 0");

                    b.HasIndex("AccountCode", "ProviderCode")
                        .IsUnique()
                        .HasDatabaseName("UQ_account_provider")
                        .HasFilter("[is_deleted] = 0");

                    b.ToTable("message_account_provider", "m", t =>
                        {
                            t.HasComment("MessageAccountProvider 实体");
                        });
                });

            modelBuilder.Entity("XJ.Framework.Messaging.Domain.Entities.MessageAccountRechargeEntity", b =>
                {
                    b.Property<long>("Key")
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    b.Property<long>("AccountProviderId")
                        .HasColumnType("bigint")
                        .HasColumnName("account_provider_id")
                        .HasComment("账户-服务商路由ID");

                    b.Property<int>("Amount")
                        .HasColumnType("int")
                        .HasColumnName("amount")
                        .HasComment("充值数量");

                    b.Property<int?>("BalanceAfter")
                        .HasColumnType("int")
                        .HasColumnName("balance_after")
                        .HasComment("充值后余额");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_time");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit")
                        .HasColumnName("is_deleted");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("last_modified_by");

                    b.Property<DateTimeOffset?>("LastModifiedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("last_modified_time");

                    b.Property<string>("Operator")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("operator")
                        .HasComment("操作人");

                    b.Property<DateTimeOffset>("RechargeTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("recharge_time")
                        .HasComment("充值时间");

                    b.Property<string>("Remark")
                        .HasMaxLength(400)
                        .HasColumnType("nvarchar(400)")
                        .HasColumnName("remark")
                        .HasComment("备注");

                    b.HasKey("Key");

                    b.HasIndex("AccountProviderId")
                        .HasDatabaseName("IX_MessageAccountRecharge_AccountProviderId")
                        .HasFilter("[is_deleted] = 0");

                    b.ToTable("message_account_recharge", "m", t =>
                        {
                            t.HasComment("MessageAccountRecharge 实体");
                        });
                });

            modelBuilder.Entity("XJ.Framework.Messaging.Domain.Entities.MessageAccountUsageEntity", b =>
                {
                    b.Property<long>("Key")
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    b.Property<string>("AccountCode")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("account_code")
                        .HasComment("账户编码");

                    b.Property<long>("AccountProviderId")
                        .HasColumnType("bigint")
                        .HasColumnName("account_provider_id")
                        .HasComment("账户-服务商路由ID");

                    b.Property<int>("Amount")
                        .HasColumnType("int")
                        .HasColumnName("amount")
                        .HasComment("用量");

                    b.Property<string>("AppCode")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("app_code")
                        .HasComment("应用编码");

                    b.Property<int?>("BalanceAfter")
                        .HasColumnType("int")
                        .HasColumnName("balance_after")
                        .HasComment("用量后余额");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_time");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit")
                        .HasColumnName("is_deleted");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("last_modified_by");

                    b.Property<DateTimeOffset?>("LastModifiedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("last_modified_time");

                    b.Property<long?>("MessageSendId")
                        .HasColumnType("bigint")
                        .HasColumnName("message_send_id")
                        .HasComment("关联消息发送ID");

                    b.Property<string>("MessageType")
                        .IsRequired()
                        .HasMaxLength(40)
                        .HasColumnType("nvarchar(40)")
                        .HasColumnName("message_type")
                        .HasComment("消息类型");

                    b.Property<string>("ProviderCode")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("provider_code")
                        .HasComment("服务商编码");

                    b.Property<string>("Remark")
                        .HasMaxLength(400)
                        .HasColumnType("nvarchar(400)")
                        .HasColumnName("remark")
                        .HasComment("备注");

                    b.Property<string>("Target")
                        .HasMaxLength(400)
                        .HasColumnType("nvarchar(400)")
                        .HasColumnName("target")
                        .HasComment("发送目标");

                    b.Property<string>("TemplateCode")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("template_code")
                        .HasComment("模板编码");

                    b.Property<DateTimeOffset>("UsageTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("usage_time")
                        .HasComment("用量时间");

                    b.Property<string>("UsageType")
                        .IsRequired()
                        .HasMaxLength(40)
                        .HasColumnType("nvarchar(40)")
                        .HasColumnName("usage_type")
                        .HasComment("使用类型");

                    b.HasKey("Key");

                    b.HasIndex("AccountProviderId")
                        .HasDatabaseName("IX_account_usage_account_provider")
                        .HasFilter("[is_deleted] = 0");

                    b.HasIndex("AppCode")
                        .HasDatabaseName("IX_account_usage_appcode")
                        .HasFilter("[is_deleted] = 0");

                    b.HasIndex("ProviderCode")
                        .HasDatabaseName("IX_account_usage_provider")
                        .HasFilter("[is_deleted] = 0");

                    b.HasIndex("Target")
                        .HasDatabaseName("IX_account_usage_target")
                        .HasFilter("[is_deleted] = 0");

                    b.HasIndex("TemplateCode")
                        .HasDatabaseName("IX_account_usage_template")
                        .HasFilter("[is_deleted] = 0");

                    b.HasIndex("MessageType", "TemplateCode")
                        .HasDatabaseName("IX_account_usage_type_template")
                        .HasFilter("[is_deleted] = 0");

                    b.ToTable("message_account_usage", "m", t =>
                        {
                            t.HasComment("MessageAccountUsage 实体");
                        });
                });

            modelBuilder.Entity("XJ.Framework.Messaging.Domain.Entities.MessageProviderDefaultEntity", b =>
                {
                    b.Property<long>("Key")
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    b.Property<string>("AppCode")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("app_code")
                        .HasComment("应用编码");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_time");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit")
                        .HasColumnName("is_deleted");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("last_modified_by");

                    b.Property<DateTimeOffset?>("LastModifiedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("last_modified_time");

                    b.Property<string>("ProviderCode")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("provider_code")
                        .HasComment("服务商编码");

                    b.Property<string>("ProviderType")
                        .IsRequired()
                        .HasMaxLength(40)
                        .HasColumnType("nvarchar(40)")
                        .HasColumnName("provider_type")
                        .HasComment("服务商类型");

                    b.HasKey("Key");

                    b.HasIndex("AppCode", "ProviderType")
                        .IsUnique()
                        .HasDatabaseName("IX_provider_default")
                        .HasFilter("[is_deleted] = 0");

                    b.ToTable("message_provider_default", "m", t =>
                        {
                            t.HasComment("MessageProviderDefault 实体");
                        });
                });

            modelBuilder.Entity("XJ.Framework.Messaging.Domain.Entities.MessageProviderEntity", b =>
                {
                    b.Property<long>("Key")
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_time");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit")
                        .HasColumnName("is_deleted");

                    b.Property<bool>("IsEnabled")
                        .HasColumnType("bit")
                        .HasColumnName("is_enabled")
                        .HasComment("是否启用");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("last_modified_by");

                    b.Property<DateTimeOffset?>("LastModifiedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("last_modified_time");

                    b.Property<string>("ProviderCode")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("provider_code")
                        .HasComment("服务商编码");

                    b.Property<string>("ProviderName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("provider_name")
                        .HasComment("服务商名称");

                    b.Property<string>("ProviderType")
                        .IsRequired()
                        .HasMaxLength(40)
                        .HasColumnType("nvarchar(40)")
                        .HasColumnName("provider_type")
                        .HasComment("服务商类型");

                    b.HasKey("Key");

                    b.HasIndex("ProviderType", "ProviderName")
                        .IsUnique()
                        .HasDatabaseName("UQ_message_provider_type_name")
                        .HasFilter("[is_deleted] = 0");

                    b.ToTable("message_provider", "m", t =>
                        {
                            t.HasComment("MessageProvider 实体");
                        });
                });

            modelBuilder.Entity("XJ.Framework.Messaging.Domain.Entities.MessageSendArchiveEntity", b =>
                {
                    b.Property<long>("Key")
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    b.Property<string>("AppCode")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("app_code")
                        .HasComment("应用编码");

                    b.Property<DateTimeOffset>("ArchiveTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("archive_time")
                        .HasComment("归档时间");

                    b.Property<string>("CorrelationId")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("correlation_id")
                        .HasComment("关联ID");

                    b.Property<string>("CorrelationType")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("correlation_type")
                        .HasComment("关联类型");

                    b.Property<DateTimeOffset>("CreateTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("create_time")
                        .HasComment("创建时间");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_time");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit")
                        .HasColumnName("is_deleted");

                    b.Property<string>("ErrorInfo")
                        .HasMaxLength(2000)
                        .HasColumnType("nvarchar(2000)")
                        .HasColumnName("error_info")
                        .HasComment("错误信息");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("last_modified_by");

                    b.Property<DateTimeOffset?>("LastModifiedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("last_modified_time");

                    b.Property<string>("MessageType")
                        .IsRequired()
                        .HasMaxLength(40)
                        .HasColumnType("nvarchar(40)")
                        .HasColumnName("message_type")
                        .HasComment("消息类型");

                    b.Property<int>("Priority")
                        .HasColumnType("int")
                        .HasColumnName("priority")
                        .HasComment("优先级");

                    b.Property<string>("ProviderCode")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("provider_code")
                        .HasComment("服务商编码");

                    b.Property<DateTimeOffset?>("SendTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("send_time")
                        .HasComment("发送时间");

                    b.Property<int>("Status")
                        .HasColumnType("int")
                        .HasColumnName("status")
                        .HasComment("发送状态");

                    b.Property<string>("Target")
                        .IsRequired()
                        .HasMaxLength(400)
                        .HasColumnType("nvarchar(400)")
                        .HasColumnName("target")
                        .HasComment("发送目标");

                    b.Property<string>("TemplateCode")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("template_code")
                        .HasComment("模板编码");

                    b.Property<int>("TryTime")
                        .HasColumnType("int")
                        .HasColumnName("try_times")
                        .HasComment("重试次数");

                    b.Property<string>("VariablesJson")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("variables_json")
                        .HasComment("变量内容");

                    b.HasKey("Key");

                    b.HasIndex("AppCode")
                        .HasDatabaseName("IX_message_send_archive_appcode")
                        .HasFilter("[is_deleted] = 0");

                    b.HasIndex("TemplateCode")
                        .HasDatabaseName("IX_message_send_archive_template")
                        .HasFilter("[is_deleted] = 0");

                    b.ToTable("message_send_archive", "m", t =>
                        {
                            t.HasComment("MessageSendArchive 实体");
                        });
                });

            modelBuilder.Entity("XJ.Framework.Messaging.Domain.Entities.MessageSendEntity", b =>
                {
                    b.Property<long>("Key")
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    b.Property<string>("AppCode")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("app_code")
                        .HasComment("应用编码");

                    b.Property<string>("CorrelationId")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("correlation_id")
                        .HasComment("关联ID");

                    b.Property<string>("CorrelationType")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("correlation_type")
                        .HasComment("关联类型");

                    b.Property<DateTimeOffset>("CreateTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("create_time")
                        .HasComment("创建时间");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_time");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit")
                        .HasColumnName("is_deleted");

                    b.Property<string>("ErrorInfo")
                        .HasMaxLength(2000)
                        .HasColumnType("nvarchar(2000)")
                        .HasColumnName("error_info")
                        .HasComment("错误信息");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("last_modified_by");

                    b.Property<DateTimeOffset?>("LastModifiedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("last_modified_time");

                    b.Property<string>("MessageType")
                        .IsRequired()
                        .HasMaxLength(40)
                        .HasColumnType("nvarchar(40)")
                        .HasColumnName("message_type")
                        .HasComment("消息类型");

                    b.Property<int>("Priority")
                        .HasColumnType("int")
                        .HasColumnName("priority")
                        .HasComment("优先级");

                    b.Property<string>("ProviderCode")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("provider_code")
                        .HasComment("服务商编码");

                    b.Property<DateTimeOffset?>("SendTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("send_time")
                        .HasComment("发送时间");

                    b.Property<int>("Status")
                        .HasColumnType("int")
                        .HasColumnName("status")
                        .HasComment("发送状态");

                    b.Property<string>("Target")
                        .IsRequired()
                        .HasMaxLength(400)
                        .HasColumnType("nvarchar(400)")
                        .HasColumnName("target")
                        .HasComment("发送目标");

                    b.Property<string>("TemplateCode")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("template_code")
                        .HasComment("模板编码");

                    b.Property<int>("TryTime")
                        .HasColumnType("int")
                        .HasColumnName("try_times")
                        .HasComment("重试次数");

                    b.Property<string>("VariablesJson")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("variables_json")
                        .HasComment("变量内容");

                    b.HasKey("Key");

                    b.HasIndex("AppCode")
                        .HasDatabaseName("IX_message_send_appcode")
                        .HasFilter("[is_deleted] = 0");

                    b.HasIndex("TemplateCode")
                        .HasDatabaseName("IX_message_send_template")
                        .HasFilter("[is_deleted] = 0");

                    b.HasIndex("Status", "TryTime")
                        .HasDatabaseName("IX_message_send_status_trytimes")
                        .HasFilter("[is_deleted] = 0");

                    b.ToTable("message_send", "m", t =>
                        {
                            t.HasComment("MessageSend 实体");
                        });
                });

            modelBuilder.Entity("XJ.Framework.Messaging.Domain.Entities.MessageTemplateEntity", b =>
                {
                    b.Property<long>("Key")
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    b.Property<string>("AppCode")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("app_code")
                        .HasComment("应用编码");

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("content")
                        .HasComment("模板内容");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_time");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit")
                        .HasColumnName("is_deleted");

                    b.Property<bool>("IsEnabled")
                        .HasColumnType("bit")
                        .HasColumnName("is_enabled")
                        .HasComment("是否启用");

                    b.Property<string>("Lang")
                        .HasMaxLength(40)
                        .HasColumnType("nvarchar(40)")
                        .HasColumnName("lang")
                        .HasComment("语言");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("last_modified_by");

                    b.Property<DateTimeOffset?>("LastModifiedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("last_modified_time");

                    b.Property<string>("ProviderCode")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("provider_code")
                        .HasComment("服务商编码");

                    b.Property<string>("TemplateCode")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("template_code")
                        .HasComment("模板编码");

                    b.Property<string>("TemplateName")
                        .IsRequired()
                        .HasMaxLength(400)
                        .HasColumnType("nvarchar(400)")
                        .HasColumnName("template_name")
                        .HasComment("模板名称");

                    b.Property<string>("TemplateType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("template_type")
                        .HasComment("模板类型");

                    b.Property<string>("Variable")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("variables")
                        .HasComment("变量定义");

                    b.HasKey("Key");

                    b.HasIndex("AppCode", "TemplateCode")
                        .IsUnique()
                        .HasDatabaseName("UQ_message_template_appcode_code")
                        .HasFilter("[is_deleted] = 0");

                    b.ToTable("message_template", "m", t =>
                        {
                            t.HasComment("MessageTemplate 实体");
                        });
                });
#pragma warning restore 612, 618
        }
    }
}
