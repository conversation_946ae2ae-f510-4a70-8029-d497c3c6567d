using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace XJ.Framework.Messaging.EntityFrameworkCore.Migrations
{
    /// <inheritdoc />
    public partial class Initial : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.EnsureSchema(
                name: "m");

            migrationBuilder.CreateTable(
                name: "message_account",
                schema: "m",
                columns: table => new
                {
                    id = table.Column<long>(type: "bigint", nullable: false),
                    account_code = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false, comment: "账户编码"),
                    app_code = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false, comment: "应用编码"),
                    account_name = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false, comment: "账户名称"),
                    is_enabled = table.Column<bool>(type: "bit", nullable: false, comment: "是否启用"),
                    created_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    last_modified_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    last_modified_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_message_account", x => x.id);
                },
                comment: "MessageAccount 实体");

            migrationBuilder.CreateTable(
                name: "message_account_provider",
                schema: "m",
                columns: table => new
                {
                    id = table.Column<long>(type: "bigint", nullable: false),
                    account_code = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false, comment: "账户编码"),
                    provider_code = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false, comment: "服务商编码"),
                    provider_type = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false, comment: "服务商类型"),
                    priority = table.Column<int>(type: "int", nullable: false, comment: "优先级"),
                    config_json = table.Column<string>(type: "nvarchar(max)", nullable: true, comment: "账户专属配置"),
                    quota_total = table.Column<int>(type: "int", nullable: true, comment: "总额度"),
                    quota_used = table.Column<int>(type: "int", nullable: true, comment: "已用额度"),
                    is_enabled = table.Column<bool>(type: "bit", nullable: false, comment: "是否启用"),
                    created_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    last_modified_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    last_modified_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_message_account_provider", x => x.id);
                },
                comment: "MessageAccountProvider 实体");

            migrationBuilder.CreateTable(
                name: "message_account_recharge",
                schema: "m",
                columns: table => new
                {
                    id = table.Column<long>(type: "bigint", nullable: false),
                    account_provider_id = table.Column<long>(type: "bigint", nullable: false, comment: "账户-服务商路由ID"),
                    amount = table.Column<int>(type: "int", nullable: false, comment: "充值数量"),
                    balance_after = table.Column<int>(type: "int", nullable: true, comment: "充值后余额"),
                    recharge_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false, comment: "充值时间"),
                    @operator = table.Column<string>(name: "operator", type: "nvarchar(100)", maxLength: 100, nullable: true, comment: "操作人"),
                    remark = table.Column<string>(type: "nvarchar(400)", maxLength: 400, nullable: true, comment: "备注"),
                    created_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    last_modified_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    last_modified_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_message_account_recharge", x => x.id);
                },
                comment: "MessageAccountRecharge 实体");

            migrationBuilder.CreateTable(
                name: "message_account_usage",
                schema: "m",
                columns: table => new
                {
                    id = table.Column<long>(type: "bigint", nullable: false),
                    account_provider_id = table.Column<long>(type: "bigint", nullable: false, comment: "账户-服务商路由ID"),
                    account_code = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false, comment: "账户编码"),
                    app_code = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false, comment: "应用编码"),
                    provider_code = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false, comment: "服务商编码"),
                    usage_type = table.Column<string>(type: "nvarchar(40)", maxLength: 40, nullable: false, comment: "使用类型"),
                    message_type = table.Column<string>(type: "nvarchar(40)", maxLength: 40, nullable: false, comment: "消息类型"),
                    template_code = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true, comment: "模板编码"),
                    target = table.Column<string>(type: "nvarchar(400)", maxLength: 400, nullable: true, comment: "发送目标"),
                    amount = table.Column<int>(type: "int", nullable: false, comment: "用量"),
                    balance_after = table.Column<int>(type: "int", nullable: true, comment: "用量后余额"),
                    usage_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false, comment: "用量时间"),
                    message_send_id = table.Column<long>(type: "bigint", nullable: true, comment: "关联消息发送ID"),
                    remark = table.Column<string>(type: "nvarchar(400)", maxLength: 400, nullable: true, comment: "备注"),
                    created_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    last_modified_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    last_modified_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_message_account_usage", x => x.id);
                },
                comment: "MessageAccountUsage 实体");

            migrationBuilder.CreateTable(
                name: "message_provider",
                schema: "m",
                columns: table => new
                {
                    id = table.Column<long>(type: "bigint", nullable: false),
                    provider_code = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false, comment: "服务商编码"),
                    provider_name = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false, comment: "服务商名称"),
                    provider_type = table.Column<string>(type: "nvarchar(40)", maxLength: 40, nullable: false, comment: "服务商类型"),
                    config = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: false, comment: "服务商配置"),
                    is_enabled = table.Column<bool>(type: "bit", nullable: false, comment: "是否启用"),
                    created_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    last_modified_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    last_modified_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_message_provider", x => x.id);
                },
                comment: "MessageProvider 实体");

            migrationBuilder.CreateTable(
                name: "message_send",
                schema: "m",
                columns: table => new
                {
                    id = table.Column<long>(type: "bigint", nullable: false),
                    app_code = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false, comment: "应用编码"),
                    target = table.Column<string>(type: "nvarchar(400)", maxLength: 400, nullable: false, comment: "发送目标"),
                    template_code = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false, comment: "模板编码"),
                    variables_json = table.Column<string>(type: "nvarchar(max)", nullable: false, comment: "变量内容"),
                    priority = table.Column<int>(type: "int", nullable: false, comment: "优先级"),
                    message_type = table.Column<string>(type: "nvarchar(40)", maxLength: 40, nullable: false, comment: "消息类型"),
                    provider_code = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true, comment: "服务商编码"),
                    create_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false, comment: "创建时间"),
                    send_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true, comment: "发送时间"),
                    status = table.Column<int>(type: "int", nullable: false, comment: "发送状态"),
                    try_times = table.Column<int>(type: "int", nullable: false, comment: "重试次数"),
                    error_info = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: true, comment: "错误信息"),
                    correlation_id = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true, comment: "关联ID"),
                    correlation_type = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true, comment: "关联类型"),
                    created_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    last_modified_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    last_modified_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_message_send", x => x.id);
                },
                comment: "MessageSend 实体");

            migrationBuilder.CreateTable(
                name: "message_send_archive",
                schema: "m",
                columns: table => new
                {
                    id = table.Column<long>(type: "bigint", nullable: false),
                    app_code = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false, comment: "应用编码"),
                    target = table.Column<string>(type: "nvarchar(400)", maxLength: 400, nullable: false, comment: "发送目标"),
                    template_code = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false, comment: "模板编码"),
                    variables_json = table.Column<string>(type: "nvarchar(max)", nullable: false, comment: "变量内容"),
                    priority = table.Column<int>(type: "int", nullable: false, comment: "优先级"),
                    message_type = table.Column<string>(type: "nvarchar(40)", maxLength: 40, nullable: false, comment: "消息类型"),
                    provider_code = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true, comment: "服务商编码"),
                    create_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false, comment: "创建时间"),
                    send_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true, comment: "发送时间"),
                    status = table.Column<int>(type: "int", nullable: false, comment: "发送状态"),
                    try_times = table.Column<int>(type: "int", nullable: false, comment: "重试次数"),
                    error_info = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: true, comment: "错误信息"),
                    correlation_id = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true, comment: "关联ID"),
                    correlation_type = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true, comment: "关联类型"),
                    archive_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false, comment: "归档时间"),
                    created_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    last_modified_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    last_modified_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_message_send_archive", x => x.id);
                },
                comment: "MessageSendArchive 实体");

            migrationBuilder.CreateTable(
                name: "message_template",
                schema: "m",
                columns: table => new
                {
                    id = table.Column<long>(type: "bigint", nullable: false),
                    template_code = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false, comment: "模板编码"),
                    app_code = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false, comment: "应用编码"),
                    template_name = table.Column<string>(type: "nvarchar(400)", maxLength: 400, nullable: false, comment: "模板名称"),
                    template_type = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false, comment: "模板类型"),
                    title = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "模板消息标题"),
                    content = table.Column<string>(type: "nvarchar(max)", nullable: false, comment: "模板内容"),
                    variables = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true, comment: "变量定义"),
                    provider_code = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true, comment: "服务商编码"),
                    is_enabled = table.Column<bool>(type: "bit", nullable: false, comment: "是否启用"),
                    created_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    last_modified_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    last_modified_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_message_template", x => x.id);
                },
                comment: "MessageTemplate 实体");

            migrationBuilder.CreateIndex(
                name: "UQ_MessageAccount_AccountCode_AppCode",
                schema: "m",
                table: "message_account",
                columns: new[] { "account_code", "app_code" },
                unique: true,
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_account_provider_priority",
                schema: "m",
                table: "message_account_provider",
                columns: new[] { "account_code", "priority" },
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "UQ_account_provider",
                schema: "m",
                table: "message_account_provider",
                columns: new[] { "account_code", "provider_code" },
                unique: true,
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_MessageAccountRecharge_AccountProviderId",
                schema: "m",
                table: "message_account_recharge",
                column: "account_provider_id",
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_account_usage_account_provider",
                schema: "m",
                table: "message_account_usage",
                column: "account_provider_id",
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_account_usage_appcode",
                schema: "m",
                table: "message_account_usage",
                column: "app_code",
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_account_usage_provider",
                schema: "m",
                table: "message_account_usage",
                column: "provider_code",
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_account_usage_target",
                schema: "m",
                table: "message_account_usage",
                column: "target",
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_account_usage_template",
                schema: "m",
                table: "message_account_usage",
                column: "template_code",
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_account_usage_type_template",
                schema: "m",
                table: "message_account_usage",
                columns: new[] { "message_type", "template_code" },
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "UQ_message_provider_type_name",
                schema: "m",
                table: "message_provider",
                columns: new[] { "provider_type", "provider_name" },
                unique: true,
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_message_send_appcode",
                schema: "m",
                table: "message_send",
                column: "app_code",
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_message_send_status_trytimes",
                schema: "m",
                table: "message_send",
                columns: new[] { "status", "try_times" },
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_message_send_template",
                schema: "m",
                table: "message_send",
                column: "template_code",
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_message_send_archive_appcode",
                schema: "m",
                table: "message_send_archive",
                column: "app_code",
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_message_send_archive_template",
                schema: "m",
                table: "message_send_archive",
                column: "template_code",
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "UQ_message_template_appcode_code",
                schema: "m",
                table: "message_template",
                columns: new[] { "app_code", "template_code" },
                unique: true,
                filter: "[is_deleted] = 0");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "message_account",
                schema: "m");

            migrationBuilder.DropTable(
                name: "message_account_provider",
                schema: "m");

            migrationBuilder.DropTable(
                name: "message_account_recharge",
                schema: "m");

            migrationBuilder.DropTable(
                name: "message_account_usage",
                schema: "m");

            migrationBuilder.DropTable(
                name: "message_provider",
                schema: "m");

            migrationBuilder.DropTable(
                name: "message_send",
                schema: "m");

            migrationBuilder.DropTable(
                name: "message_send_archive",
                schema: "m");

            migrationBuilder.DropTable(
                name: "message_template",
                schema: "m");
        }
    }
}
