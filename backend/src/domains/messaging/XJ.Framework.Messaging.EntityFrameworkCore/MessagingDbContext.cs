namespace XJ.Framework.Messaging.EntityFrameworkCore;

public class MessagingDbContext : BaseDbContext
{
    public MessagingDbContext(DbContextOptions options, IConfiguration configuration,
        IOptions<DatabaseOption> databaseOptions) : base(options, databaseOptions)
    {
    }

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    {
        
                optionsBuilder
                    .UseSqlServer(DatabaseOptions.Value["Messaging"]!.ConnectionString);
                
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        modelBuilder.Initialize();

        modelBuilder.AddPropertyComments();

        modelBuilder.ApplyIndexAttributes();

        modelBuilder.Seed();

        // 配置枚举转换
        modelBuilder.ConfigureEnumToIntConversions();
}

} 