using XJ.Framework.Messaging.Application.Contract.Interfaces;
using XJ.Framework.Messaging.Application.Providers;
using XJ.Framework.Messaging.Application.Services;

namespace XJ.Framework.Messaging.Application;

public class MessagingApplicationWrapper : ApplicationWrapper
{
    public override void Init(IServiceCollection services, IConfigurationRoot configuration)
    {
        // 注册消息Provider
        services.AddScoped<SmsProvider>();
        services.AddScoped<EmailProvider>();

        // 注册Provider工厂
        services.AddScoped<IMessageProviderFactory, MessageProviderFactory>();
    }
}
