<Project Sdk="Microsoft.NET.Sdk">

    <Import Project="..\..\..\..\Common.Secrets.props"/>
    <Import Project="..\..\..\..\Common.props"/>

    <ItemGroup>
        <ProjectReference Include="..\..\..\shared\XJ.Framework.Library.Application\XJ.Framework.Library.Application.csproj"/>
        <ProjectReference Include="..\XJ.Framework.Messaging.Application.Contract\XJ.Framework.Messaging.Application.Contract.csproj"/>
        <ProjectReference Include="..\XJ.Framework.Messaging.EntityFrameworkCore\XJ.Framework.Messaging.EntityFrameworkCore.csproj"/>
    </ItemGroup>

    <ItemGroup>
      <Folder Include="Mappers\" />
    </ItemGroup>

    <ItemGroup>
      <PackageReference Include="AlibabaCloud.OpenApiClient" />
      <PackageReference Include="AlibabaCloud.SDK.Dysmsapi20170525" />
      <PackageReference Include="Aliyun.Credentials" />
    </ItemGroup>

</Project> 