using Microsoft.Extensions.Logging;
using System.Net;
using System.Net.Mail;
using System.Text.Json;
using System.Text.RegularExpressions;
using XJ.Framework.Messaging.Application.Contract.Interfaces;
using XJ.Framework.Messaging.Domain.Shared.Enums;

namespace XJ.Framework.Messaging.Application.Providers;

/// <summary>
/// SMTP邮件配置
/// </summary>
public class SmtpConfig
{
    /// <summary>
    /// SMTP服务器地址
    /// </summary>
    public string SMTP_HOST { get; set; } = string.Empty;

    /// <summary>
    /// SMTP服务器端口
    /// </summary>
    public string SMTP_PORT { get; set; } = "587";

    /// <summary>
    /// 发件人邮箱地址
    /// </summary>
    public string FROM_ADDRESS { get; set; } = string.Empty;

    /// <summary>
    /// 发件人显示名称
    /// </summary>
    public string FROM_DISPLAY_NAME { get; set; } = string.Empty;

    /// <summary>
    /// 发件人邮箱用户名
    /// </summary>
    public string FROM_USER { get; set; } = string.Empty;


    /// <summary>
    /// 发件人邮箱密码或授权码
    /// </summary>
    public string FROM_PASSWORD { get; set; } = string.Empty;

    /// <summary>
    /// 超时时间（毫秒）
    /// </summary>
    public int TIME_OUT { get; set; } = 30000;

    /// <summary>
    /// 是否启用SSL
    /// </summary>
    public bool ENABLE_SSL { get; set; } = true;

    /// <summary>
    /// 验证配置是否完整
    /// </summary>
    public bool IsValid()
    {
        return !string.IsNullOrWhiteSpace(SMTP_HOST) &&
               !string.IsNullOrWhiteSpace(FROM_ADDRESS) &&
               !string.IsNullOrWhiteSpace(FROM_PASSWORD) &&
               int.TryParse(SMTP_PORT, out var port) && port > 0 && port <= 65535;
    }
}

/// <summary>
/// Email消息发送Provider
/// </summary>
public class EmailProvider : IMessageProvider
{
    private readonly ILogger<EmailProvider> _logger;

    public EmailProvider(ILogger<EmailProvider> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// Provider支持的消息类型
    /// </summary>
    public MessageType SupportedMessageType => MessageType.Email;

    public string ProviderInstanceName => "CommonEmail";

    /// <summary>
    /// 发送邮件
    /// </summary>
    /// <param name="target">邮箱地址</param>
    /// <param name="title">邮件标题</param>
    /// <param name="content">邮件内容</param>
    /// <param name="providerConfig">服务商配置</param>
    /// <returns>发送结果</returns>
    public async Task<MessageProviderResult> SendAsync(string target, string title, string content,
        string? providerConfig = null)
    {
        try
        {
            _logger.LogInformation("开始发送邮件，目标：{Target}，内容长度：{ContentLength}", target, content?.Length ?? 0);


            // 验证邮件内容
            if (string.IsNullOrWhiteSpace(content))
            {
                return MessageProviderResult.CreateFailure("邮件内容不能为空");
            }

            if (string.IsNullOrWhiteSpace(title))
            {
                return MessageProviderResult.CreateFailure("邮件标题不能为空");
            }

            // 解析和验证配置
            var smtpConfig = ParseConfig(providerConfig);
            if (smtpConfig == null || !smtpConfig.IsValid())
            {
                return MessageProviderResult.CreateFailure("无效的SMTP配置");
            }

            // 验证邮箱格式
            if (!ValidateTarget(target))
            {
                return MessageProviderResult.CreateFailure("无效的邮箱地址格式");
            }

            // 发送邮件
            var providerMessageId = await SendEmailAsync(target, title, content, smtpConfig);

            _logger.LogInformation("邮件发送成功，目标：{Target}，服务商消息ID：{ProviderMessageId}", target, providerMessageId);

            return MessageProviderResult.CreateSuccess(providerMessageId, "邮件发送成功");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "邮件发送失败，目标：{Target}", target);
            return MessageProviderResult.CreateFailure($"邮件发送异常：{ex.Message}");
        }
    }

    /// <summary>
    /// 使用SMTP发送邮件
    /// </summary>
    /// <param name="target">收件人邮箱</param>
    /// <param name="title">邮件标题</param>
    /// <param name="content">邮件内容</param>
    /// <param name="smtpConfig">SMTP配置</param>
    /// <returns>服务商消息ID</returns>
    private async Task<string> SendEmailAsync(string target, string title, string content, SmtpConfig smtpConfig)
    {
        using var smtpClient = new SmtpClient(smtpConfig.SMTP_HOST, int.Parse(smtpConfig.SMTP_PORT));

        // 配置SMTP客户端
        smtpClient.EnableSsl = smtpConfig.ENABLE_SSL;
        smtpClient.UseDefaultCredentials = false;
        smtpClient.Credentials = new NetworkCredential(smtpConfig.FROM_USER, smtpConfig.FROM_PASSWORD);
        smtpClient.Timeout = smtpConfig.TIME_OUT;

        // 创建邮件消息
        using var mailMessage = new MailMessage();

        // 设置发件人
        mailMessage.From = new MailAddress(smtpConfig.FROM_ADDRESS, smtpConfig.FROM_DISPLAY_NAME);

        // 设置收件人
        mailMessage.To.Add(new MailAddress(target));

        // 设置邮件内容
        mailMessage.Subject = title;
        mailMessage.Body = content;
        mailMessage.IsBodyHtml = IsHtmlContent(content);
        mailMessage.BodyEncoding = System.Text.Encoding.UTF8;
        mailMessage.SubjectEncoding = System.Text.Encoding.UTF8;

        // 生成消息ID（用于追踪）
        var messageId = Guid.NewGuid().ToString("N");
        mailMessage.Headers.Add("X-Message-ID", messageId);

        _logger.LogDebug("开始发送SMTP邮件，服务器：{SmtpHost}:{SmtpPort}，发件人：{FromAddress}，收件人：{ToAddress}",
            smtpConfig.SMTP_HOST, smtpConfig.SMTP_PORT, smtpConfig.FROM_ADDRESS, target);

        try
        {
            // 发送邮件
            await smtpClient.SendMailAsync(mailMessage);

            _logger.LogDebug("SMTP邮件发送成功，消息ID：{MessageId}", messageId);
            return messageId;
        }
        catch (SmtpException ex)
        {
            _logger.LogError(ex, "SMTP邮件发送失败，错误代码：{StatusCode}，错误信息：{Message}",
                ex.StatusCode, ex.Message);
            throw new InvalidOperationException($"SMTP发送失败：{ex.Message}", ex);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "邮件发送过程中发生未知异常");
            throw;
        }
    }

    /// <summary>
    /// 解析SMTP配置
    /// </summary>
    /// <param name="providerConfig">配置JSON字符串</param>
    /// <returns>SMTP配置对象</returns>
    private SmtpConfig? ParseConfig(string? providerConfig)
    {
        if (string.IsNullOrWhiteSpace(providerConfig))
        {
            _logger.LogWarning("SMTP配置为空");
            return null;
        }

        try
        {
            var config = JsonSerializer.Deserialize<SmtpConfig>(providerConfig, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            if (config == null)
            {
                _logger.LogWarning("SMTP配置解析结果为null");
                return null;
            }

            _logger.LogDebug("SMTP配置解析成功，服务器：{SmtpHost}:{SmtpPort}，发件人：{FromAddress}",
                config.SMTP_HOST, config.SMTP_PORT, config.FROM_ADDRESS);

            return config;
        }
        catch (JsonException ex)
        {
            _logger.LogError(ex, "SMTP配置JSON解析失败：{Config}", providerConfig);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "解析SMTP配置时发生异常");
            return null;
        }
    }

    /// <summary>
    /// 判断内容是否为HTML格式
    /// </summary>
    /// <param name="content">邮件内容</param>
    /// <returns>是否为HTML</returns>
    private static bool IsHtmlContent(string content)
    {
        if (string.IsNullOrWhiteSpace(content))
            return false;

        // 简单检测是否包含HTML标签
        var htmlTagRegex = new Regex(@"<[^>]+>", RegexOptions.IgnoreCase);
        return htmlTagRegex.IsMatch(content);
    }

    /// <summary>
    /// 验证配置是否有效
    /// </summary>
    /// <param name="providerConfig">服务商配置</param>
    /// <returns>是否有效</returns>
    public bool ValidateConfig(string? providerConfig)
    {
        var config = ParseConfig(providerConfig);
        return config?.IsValid() == true;
    }

    /// <summary>
    /// 验证邮箱格式
    /// </summary>
    /// <param name="email">邮箱地址</param>
    /// <returns>是否有效</returns>
    public bool ValidateTarget(string email)
    {
        if (string.IsNullOrWhiteSpace(email))
            return false;

        try
        {
            // 使用正则表达式验证邮箱格式
            var emailRegex = new Regex(@"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$", RegexOptions.IgnoreCase);
            return emailRegex.IsMatch(email);
        }
        catch
        {
            return false;
        }
    }
}
