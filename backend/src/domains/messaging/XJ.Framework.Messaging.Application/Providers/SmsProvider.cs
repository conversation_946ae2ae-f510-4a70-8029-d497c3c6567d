using Microsoft.Extensions.Logging;
using XJ.Framework.Messaging.Application.Contract.Interfaces;
using XJ.Framework.Messaging.Domain.Shared.Enums;

namespace XJ.Framework.Messaging.Application.Providers;

/// <summary>
/// SMS消息发送Provider
/// </summary>
public class SmsProvider : IMessageProvider
{
    private readonly ILogger<SmsProvider> _logger;

    public SmsProvider(ILogger<SmsProvider> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// Provider支持的消息类型
    /// </summary>
    public MessageType SupportedMessageType => MessageType.Sms;

    /// <summary>
    /// 发送短信
    /// </summary>
    /// <param name="target">手机号</param>
    /// <param name="content">短信内容</param>
    /// <param name="providerConfig">服务商配置</param>
    /// <returns>发送结果</returns>
    public async Task<MessageProviderResult> SendAsync(string target, string content, string? providerConfig = null)
    {
        try
        {
            _logger.LogInformation("开始发送短信，目标：{Target}，内容：{Content}", target, content);


            // 验证短信内容
            if (string.IsNullOrWhiteSpace(content))
            {
                return MessageProviderResult.CreateFailure("短信内容不能为空");
            }

            // 验证配置
            if (!ValidateConfig(providerConfig))
            {
                return MessageProviderResult.CreateFailure("无效的服务商配置");
            }

            // TODO: 这里实现具体的短信发送逻辑
            // 例如调用阿里云短信、腾讯云短信等第三方服务

            // 模拟发送过程
            await Task.Delay(100); // 模拟网络延迟

            // 模拟成功发送
            var providerMessageId = Guid.NewGuid().ToString("N");

            _logger.LogInformation("短信发送成功，目标：{Target}，服务商消息ID：{ProviderMessageId}", target, providerMessageId);

            return MessageProviderResult.CreateSuccess(providerMessageId, "短信发送成功");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "短信发送失败，目标：{Target}", target);
            return MessageProviderResult.CreateFailure($"短信发送异常：{ex.Message}");
        }
    }

    /// <summary>
    /// 验证配置是否有效
    /// </summary>
    /// <param name="providerConfig">服务商配置</param>
    /// <returns>是否有效</returns>
    public bool ValidateConfig(string? providerConfig)
    {
        // TODO: 根据具体的短信服务商验证配置
        // 例如验证AccessKey、SecretKey、签名等

        // 暂时返回true，表示配置有效
        return true;
    }

    /// <summary>
    /// 验证手机号格式
    /// </summary>
    /// <param name="phoneNumber">手机号</param>
    /// <returns>是否有效</returns>
    public bool ValidateTarget(string phoneNumber)
    {
        if (string.IsNullOrWhiteSpace(phoneNumber))
            return false;

        // 简单的手机号验证（支持中国大陆手机号）
        return phoneNumber.Length == 11 && phoneNumber.StartsWith("1") && phoneNumber.All(char.IsDigit);
    }
}
