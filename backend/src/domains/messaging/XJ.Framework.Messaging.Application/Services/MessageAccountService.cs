using Microsoft.Extensions.Logging;
using XJ.Framework.Messaging.Domain.Shared.Dtos;

namespace XJ.Framework.Messaging.Application.Services;

/// <summary>
/// MessageAccount 服务实现
/// </summary>
public sealed class MessageAccountService :
    BaseEditableAppService<long, MessageAccountEntity, MessageAccountDto, MessageAccountOperationDto,
        IMessageAccountRepository, MessageAccountQueryCriteria>,
    IMessageAccountService
{
    private readonly ILogger<MessageAccountService> _logger;
    private readonly IMessageAccountProviderRepository _accountProviderRepository;
    private readonly IMessageAccountRechargeRepository _rechargeRepository;
    private readonly IMessageAccountUsageRepository _usageRepository;

    public MessageAccountService(
        IMessageAccountRepository repository,
        IMapper mapper,
        IUnitOfWork unitOfWork,
        IKeyGenerator<long> keyGenerator,
        ICurrentUserContext currentUserContext,
        ILogger<MessageAccountService> logger,
        IMessageAccountProviderRepository accountProviderRepository,
        IMessageAccountRechargeRepository rechargeRepository,
        IMessageAccountUsageRepository usageRepository)
        : base(repository, mapper, unitOfWork, keyGenerator, currentUserContext)
    {
        _logger = logger;
        _accountProviderRepository = accountProviderRepository;
        _rechargeRepository = rechargeRepository;
        _usageRepository = usageRepository;
    }

    /// <summary>
    /// 根据账户编码获取账户
    /// </summary>
    /// <param name="accountCode">账户编码</param>
    /// <returns>账户信息</returns>
    public async Task<MessageAccountDto?> GetByCodeAsync(string accountCode)
    {
        if (string.IsNullOrWhiteSpace(accountCode))
        {
            _logger.LogWarning("账户编码不能为空");
            return null;
        }

        var entity = await Repository.GetByCodeAsync(accountCode);
        return entity == null ? null : Mapper.Map<MessageAccountDto>(entity);
    }

    /// <summary>
    /// 根据应用编码获取账户列表
    /// </summary>
    /// <param name="appCode">应用编码</param>
    /// <returns>账户列表</returns>
    public async Task<List<MessageAccountDto>> GetByAppCodeAsync(string appCode)
    {
        if (string.IsNullOrWhiteSpace(appCode))
        {
            _logger.LogWarning("应用编码不能为空");
            return new List<MessageAccountDto>();
        }

        var entities = await Repository.GetByAppCodeAsync(appCode);
        return Mapper.Map<List<MessageAccountDto>>(entities);
    }

    /// <summary>
    /// 启用/禁用账户
    /// </summary>
    /// <param name="id">账户ID</param>
    /// <param name="isEnabled">是否启用</param>
    /// <returns>操作结果</returns>
    public async Task<bool> SetEnabledAsync(long id, bool isEnabled)
    {
        try
        {
            var entity = await Repository.GetAsync(x => x.Key.Equals(id));
            if (entity == null)
            {
                _logger.LogWarning("账户不存在，ID：{AccountId}", id);
                return false;
            }

            entity.IsEnabled = isEnabled;
            var result = await Repository.UpdateAsync(entity);

            _logger.LogInformation("账户状态更新{Status}，ID：{AccountId}，编码：{AccountCode}",
                isEnabled ? "启用" : "禁用", id, entity.AccountCode);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新账户状态失败，ID：{AccountId}", id);
            return false;
        }
    }

    /// <summary>
    /// 账户-服务商充值
    /// </summary>
    /// <param name="accountCode">账户编码</param>
    /// <param name="providerCode">服务商编码</param>
    /// <param name="amount">充值金额</param>
    /// <param name="operatorId">操作人ID</param>
    /// <param name="remark">备注</param>
    /// <returns>充值结果</returns>
    public async Task<bool> RechargeAsync(string accountCode, string providerCode, int amount, long operatorId, string? remark = null)
    {
        if (string.IsNullOrWhiteSpace(accountCode) || string.IsNullOrWhiteSpace(providerCode))
        {
            _logger.LogWarning("账户编码和服务商编码不能为空");
            return false;
        }

        if (amount <= 0)
        {
            _logger.LogWarning("充值金额必须大于0，当前金额：{Amount}", amount);
            return false;
        }

        try
        {
            await UnitOfWork.BeginTransactionAsync();

            // 获取账户信息
            var account = await Repository.GetByCodeAsync(accountCode);
            if (account == null)
            {
                _logger.LogWarning("账户不存在，账户编码：{AccountCode}", accountCode);
                return false;
            }

            if (!account.IsEnabled)
            {
                _logger.LogWarning("账户已禁用，无法充值，账户编码：{AccountCode}", accountCode);
                return false;
            }

            // 获取指定的账户-服务商配置
            var accountProvider = await _accountProviderRepository.GetByAccountAndProviderAsync(accountCode, providerCode);
            if (accountProvider == null)
            {
                _logger.LogWarning("账户-服务商配置不存在，账户：{AccountCode}，服务商：{ProviderCode}",
                    accountCode, providerCode);
                return false;
            }

            if (!accountProvider.IsEnabled)
            {
                _logger.LogWarning("账户-服务商配置已禁用，无法充值，账户：{AccountCode}，服务商：{ProviderCode}",
                    accountCode, providerCode);
                return false;
            }

            // 更新额度
            var oldQuota = accountProvider.QuotaTotal ?? 0;
            accountProvider.QuotaTotal = oldQuota + amount;
            await _accountProviderRepository.UpdateAsync(accountProvider);

            // 记录充值记录
            var rechargeRecord = new MessageAccountRechargeEntity
            {
                Key = KeyGenerator.GenerateKey(),
                AccountProviderId = accountProvider.Key,
                Amount = amount,
                BalanceAfter = accountProvider.QuotaTotal,
                RechargeTime = DateTimeOffset.Now,
                Operator = operatorId.ToString(),
                Remark = remark
            };

            await _rechargeRepository.InsertAsync(rechargeRecord);

            _logger.LogInformation("账户充值成功，账户：{AccountCode}，服务商：{ProviderCode}，充值金额：{Amount}，充值前余额：{OldBalance}，充值后余额：{NewBalance}",
                accountCode, providerCode, amount, oldQuota, accountProvider.QuotaTotal);

            await UnitOfWork.CommitAsync();
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "账户充值失败，账户：{AccountCode}，服务商：{ProviderCode}，充值金额：{Amount}",
                accountCode, providerCode, amount);
            return false;
        }
    }

    /// <summary>
    /// 获取账户余额（按服务商分组）
    /// </summary>
    /// <param name="accountCode">账户编码</param>
    /// <returns>余额信息列表</returns>
    public async Task<List<AccountProviderBalanceDto>> GetBalanceByProviderAsync(string accountCode)
    {
        if (string.IsNullOrWhiteSpace(accountCode))
        {
            _logger.LogWarning("账户编码不能为空");
            return new List<AccountProviderBalanceDto>();
        }

        try
        {
            // 获取账户的服务商配置
            var accountProviders = await _accountProviderRepository.GetByAccountCodeAsync(accountCode);
            if (!accountProviders.Any())
            {
                _logger.LogWarning("账户未配置服务商，账户编码：{AccountCode}", accountCode);
                return new List<AccountProviderBalanceDto>();
            }

            // 转换为余额DTO
            var balances = accountProviders.Select(x => new AccountProviderBalanceDto
            {
                AccountCode = accountCode,
                ProviderCode = x.ProviderCode,
                MessageType = x.ProviderType,
                TotalQuota = x.QuotaTotal ?? 0,
                UsedQuota = x.QuotaUsed ?? 0,
                Priority = x.Priority,
                IsEnabled = x.IsEnabled
            }).OrderByDescending(x => x.Priority)
              .ThenBy(x => x.ProviderCode)
              .ToList();

            return balances;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取账户余额失败，账户编码：{AccountCode}", accountCode);
            return new List<AccountProviderBalanceDto>();
        }
    }

    /// <summary>
    /// 获取账户在特定服务商的余额
    /// </summary>
    /// <param name="accountCode">账户编码</param>
    /// <param name="providerCode">服务商编码</param>
    /// <returns>余额信息</returns>
    public async Task<AccountProviderBalanceDto?> GetBalanceAsync(string accountCode, string providerCode)
    {
        if (string.IsNullOrWhiteSpace(accountCode) || string.IsNullOrWhiteSpace(providerCode))
        {
            _logger.LogWarning("账户编码和服务商编码不能为空");
            return null;
        }

        try
        {
            var accountProvider = await _accountProviderRepository.GetByAccountAndProviderAsync(accountCode, providerCode);
            if (accountProvider == null)
            {
                _logger.LogWarning("未找到账户-服务商配置，账户：{AccountCode}，服务商：{ProviderCode}",
                    accountCode, providerCode);
                return null;
            }

            return new AccountProviderBalanceDto
            {
                AccountCode = accountCode,
                ProviderCode = providerCode,
                MessageType = accountProvider.MessageType,
                TotalQuota = accountProvider.QuotaTotal ?? 0,
                UsedQuota = accountProvider.QuotaUsed ?? 0,
                Priority = accountProvider.Priority,
                IsEnabled = accountProvider.IsEnabled
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取账户余额失败，账户：{AccountCode}，服务商：{ProviderCode}",
                accountCode, providerCode);
            return null;
        }
    }

    /// <summary>
    /// 获取账户总体余额汇总
    /// </summary>
    /// <param name="accountCode">账户编码</param>
    /// <returns>汇总余额信息</returns>
    public async Task<AccountBalanceSummaryDto?> GetBalanceSummaryAsync(string accountCode)
    {
        if (string.IsNullOrWhiteSpace(accountCode))
        {
            _logger.LogWarning("账户编码不能为空");
            return null;
        }

        try
        {
            // 获取账户的服务商配置
            var accountProviders = await _accountProviderRepository.GetByAccountCodeAsync(accountCode);
            if (!accountProviders.Any())
            {
                _logger.LogWarning("账户未配置服务商，账户编码：{AccountCode}", accountCode);
                return null;
            }

            // 计算汇总信息
            var totalQuota = accountProviders.Sum(x => x.QuotaTotal ?? 0);
            var usedQuota = accountProviders.Sum(x => x.QuotaUsed ?? 0);
            var providerCount = accountProviders.Count;
            var enabledProviderCount = accountProviders.Count(x => x.IsEnabled);

            return new AccountBalanceSummaryDto
            {
                AccountCode = accountCode,
                TotalQuota = totalQuota,
                UsedQuota = usedQuota,
                ProviderCount = providerCount,
                EnabledProviderCount = enabledProviderCount
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取账户余额汇总失败，账户编码：{AccountCode}", accountCode);
            return null;
        }
    }
}
