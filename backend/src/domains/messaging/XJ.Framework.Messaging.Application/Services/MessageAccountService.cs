using Microsoft.Extensions.Logging;
using XJ.Framework.Messaging.Domain.Shared.Dtos;

namespace XJ.Framework.Messaging.Application.Services;

/// <summary>
/// MessageAccount 服务实现
/// </summary>
public sealed class MessageAccountService :
    BaseEditableAppService<long, MessageAccountEntity, MessageAccountDto, MessageAccountOperationDto,
        IMessageAccountRepository, MessageAccountQueryCriteria>,
    IMessageAccountService
{
    private readonly ILogger<MessageAccountService> _logger;
    private readonly IMessageAccountProviderRepository _accountProviderRepository;
    private readonly IMessageAccountRechargeRepository _rechargeRepository;
    private readonly IMessageAccountUsageRepository _usageRepository;

    public MessageAccountService(
        IMessageAccountRepository repository,
        IMapper mapper,
        IUnitOfWork unitOfWork,
        IKeyGenerator<long> keyGenerator,
        ICurrentUserContext currentUserContext,
        ILogger<MessageAccountService> logger,
        IMessageAccountProviderRepository accountProviderRepository,
        IMessageAccountRechargeRepository rechargeRepository,
        IMessageAccountUsageRepository usageRepository)
        : base(repository, mapper, unitOfWork, keyGenerator, currentUserContext)
    {
        _logger = logger;
        _accountProviderRepository = accountProviderRepository;
        _rechargeRepository = rechargeRepository;
        _usageRepository = usageRepository;
    }

    /// <summary>
    /// 根据账户编码获取账户
    /// </summary>
    /// <param name="accountCode">账户编码</param>
    /// <returns>账户信息</returns>
    public async Task<MessageAccountDto?> GetByCodeAsync(string accountCode)
    {
        if (string.IsNullOrWhiteSpace(accountCode))
        {
            _logger.LogWarning("账户编码不能为空");
            return null;
        }

        var entity = await Repository.GetByCodeAsync(accountCode);
        return entity == null ? null : Mapper.Map<MessageAccountDto>(entity);
    }

    /// <summary>
    /// 根据应用编码获取账户列表
    /// </summary>
    /// <param name="appCode">应用编码</param>
    /// <returns>账户列表</returns>
    public async Task<List<MessageAccountDto>> GetByAppCodeAsync(string appCode)
    {
        if (string.IsNullOrWhiteSpace(appCode))
        {
            _logger.LogWarning("应用编码不能为空");
            return new List<MessageAccountDto>();
        }

        var entities = await Repository.GetByAppCodeAsync(appCode);
        return Mapper.Map<List<MessageAccountDto>>(entities);
    }

    /// <summary>
    /// 启用/禁用账户
    /// </summary>
    /// <param name="id">账户ID</param>
    /// <param name="isEnabled">是否启用</param>
    /// <returns>操作结果</returns>
    public async Task<bool> SetEnabledAsync(long id, bool isEnabled)
    {
        try
        {
            var entity = await Repository.GetAsync(x => x.Key.Equals(id));
            if (entity == null)
            {
                _logger.LogWarning("账户不存在，ID：{AccountId}", id);
                return false;
            }

            entity.IsEnabled = isEnabled;
            var result = await Repository.UpdateAsync(entity);

            _logger.LogInformation("账户状态更新{Status}，ID：{AccountId}，编码：{AccountCode}",
                isEnabled ? "启用" : "禁用", id, entity.AccountCode);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新账户状态失败，ID：{AccountId}", id);
            return false;
        }
    }

    /// <summary>
    /// 账户充值
    /// </summary>
    /// <param name="accountCode">账户编码</param>
    /// <param name="amount">充值金额</param>
    /// <param name="operatorId">操作人ID</param>
    /// <param name="remark">备注</param>
    /// <returns>充值结果</returns>
    public async Task<bool> RechargeAsync(string accountCode, int amount, long operatorId, string? remark = null)
    {
        if (string.IsNullOrWhiteSpace(accountCode))
        {
            _logger.LogWarning("账户编码不能为空");
            return false;
        }

        if (amount <= 0)
        {
            _logger.LogWarning("充值金额必须大于0，当前金额：{Amount}", amount);
            return false;
        }

        try
        {
            await UnitOfWork.BeginTransactionAsync();

            // 获取账户信息
            var account = await Repository.GetByCodeAsync(accountCode);
            if (account == null)
            {
                _logger.LogWarning("账户不存在，账户编码：{AccountCode}", accountCode);
                return false;
            }

            if (!account.IsEnabled)
            {
                _logger.LogWarning("账户已禁用，无法充值，账户编码：{AccountCode}", accountCode);
                return false;
            }

            // 获取账户的服务商配置
            var accountProviders = await _accountProviderRepository.GetByAccountCodeAsync(accountCode);
            if (!accountProviders.Any())
            {
                _logger.LogWarning("账户未配置服务商，无法充值，账户编码：{AccountCode}", accountCode);
                return false;
            }

            // 为每个账户-服务商配置充值
            foreach (var accountProvider in accountProviders.Where(x => x.IsEnabled))
            {
                // 更新额度
                accountProvider.QuotaTotal = (accountProvider.QuotaTotal ?? 0) + amount;
                await _accountProviderRepository.UpdateAsync(accountProvider);

                // 记录充值记录
                var rechargeRecord = new MessageAccountRechargeEntity
                {
                    Key = KeyGenerator.GenerateKey(),
                    AccountProviderId = accountProvider.Key,
                    Amount = amount,
                    BalanceAfter = accountProvider.QuotaTotal,
                    RechargeTime = DateTimeOffset.Now,
                    Operator = operatorId.ToString(),
                    Remark = remark
                };

                await _rechargeRepository.InsertAsync(rechargeRecord);

                _logger.LogInformation("账户充值成功，账户编码：{AccountCode}，服务商：{ProviderCode}，充值金额：{Amount}，充值后余额：{Balance}",
                    accountCode, accountProvider.ProviderCode, amount, accountProvider.QuotaTotal);
            }

            await UnitOfWork.CommitAsync();
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "账户充值失败，账户编码：{AccountCode}，充值金额：{Amount}", accountCode, amount);
            return false;
        }
    }

    /// <summary>
    /// 获取账户余额
    /// </summary>
    /// <param name="accountCode">账户编码</param>
    /// <returns>余额信息</returns>
    public async Task<AccountBalanceDto?> GetBalanceAsync(string accountCode)
    {
        if (string.IsNullOrWhiteSpace(accountCode))
        {
            _logger.LogWarning("账户编码不能为空");
            return null;
        }

        try
        {
            // 获取账户的服务商配置
            var accountProviders = await _accountProviderRepository.GetByAccountCodeAsync(accountCode);
            if (!accountProviders.Any())
            {
                _logger.LogWarning("账户未配置服务商，账户编码：{AccountCode}", accountCode);
                return null;
            }

            // 计算总额度和已用额度
            var totalQuota = accountProviders.Where(x => x.IsEnabled).Sum(x => x.QuotaTotal ?? 0);
            var usedQuota = accountProviders.Where(x => x.IsEnabled).Sum(x => x.QuotaUsed ?? 0);

            return new AccountBalanceDto
            {
                AccountCode = accountCode,
                TotalQuota = totalQuota,
                UsedQuota = usedQuota
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取账户余额失败，账户编码：{AccountCode}", accountCode);
            return null;
        }
    }
}
