using Microsoft.Extensions.Logging;
using XJ.Framework.Messaging.Domain.Shared.Dtos;

namespace XJ.Framework.Messaging.Application.Services;

/// <summary>
/// MessageAccountProvider 服务实现
/// </summary>
public sealed class MessageAccountProviderService :
    BaseEditableAppService<long, MessageAccountProviderEntity, MessageAccountProviderDto,
        MessageAccountProviderOperationDto, IMessageAccountProviderRepository, MessageAccountProviderQueryCriteria>,
    IMessageAccountProviderService
{
    private readonly ILogger<MessageAccountProviderService> _logger;
    private readonly IMessageAccountRepository _accountRepository;
    private readonly IMessageProviderRepository _providerRepository;

    public MessageAccountProviderService(
        IMessageAccountProviderRepository repository,
        IMapper mapper,
        IUnitOfWork unitOfWork,
        IKeyGenerator<long> keyGenerator,
        ICurrentUserContext currentUserContext,
        ILogger<MessageAccountProviderService> logger,
        IMessageAccountRepository accountRepository,
        IMessageProviderRepository providerRepository)
        : base(repository, mapper, unitOfWork, keyGenerator, currentUserContext)
    {
        _logger = logger;
        _accountRepository = accountRepository;
        _providerRepository = providerRepository;
    }

    /// <summary>
    /// 根据账户编码获取账户-服务商配置
    /// </summary>
    /// <param name="accountCode">账户编码</param>
    /// <returns>账户-服务商配置列表</returns>
    public async Task<List<MessageAccountProviderDto>> GetByAccountCodeAsync(string accountCode)
    {
        if (string.IsNullOrWhiteSpace(accountCode))
        {
            _logger.LogWarning("账户编码不能为空");
            return new List<MessageAccountProviderDto>();
        }

        var entities = await Repository.GetByAccountCodeAsync(accountCode);
        return Mapper.Map<List<MessageAccountProviderDto>>(entities);
    }

    /// <summary>
    /// 根据服务商编码获取账户-服务商配置
    /// </summary>
    /// <param name="providerCode">服务商编码</param>
    /// <returns>账户-服务商配置列表</returns>
    public async Task<List<MessageAccountProviderDto>> GetByProviderCodeAsync(string providerCode)
    {
        if (string.IsNullOrWhiteSpace(providerCode))
        {
            _logger.LogWarning("服务商编码不能为空");
            return new List<MessageAccountProviderDto>();
        }

        var entities = await Repository.GetByProviderCodeAsync(providerCode);
        return Mapper.Map<List<MessageAccountProviderDto>>(entities);
    }

    /// <summary>
    /// 根据账户编码和服务商编码获取配置
    /// </summary>
    /// <param name="accountCode">账户编码</param>
    /// <param name="providerCode">服务商编码</param>
    /// <returns>账户-服务商配置</returns>
    public async Task<MessageAccountProviderDto?> GetByAccountAndProviderAsync(string accountCode, string providerCode)
    {
        if (string.IsNullOrWhiteSpace(accountCode) || string.IsNullOrWhiteSpace(providerCode))
        {
            _logger.LogWarning("账户编码和服务商编码不能为空");
            return null;
        }

        var entity = await Repository.GetByAccountAndProviderAsync(accountCode, providerCode);
        return entity == null ? null : Mapper.Map<MessageAccountProviderDto>(entity);
    }

    /// <summary>
    /// 创建账户-服务商关联
    /// </summary>
    /// <param name="request">关联请求</param>
    /// <returns>创建结果</returns>
    public async Task<bool> CreateAccountProviderAsync(CreateAccountProviderRequestDto request)
    {
        if (request == null)
        {
            _logger.LogWarning("创建请求不能为空");
            return false;
        }

        try
        {
            // 验证账户是否存在
            var account = await _accountRepository.GetByCodeAsync(request.AccountCode);
            if (account == null)
            {
                _logger.LogWarning("账户不存在，账户编码：{AccountCode}", request.AccountCode);
                return false;
            }

            if (!account.IsEnabled)
            {
                _logger.LogWarning("账户已禁用，账户编码：{AccountCode}", request.AccountCode);
                return false;
            }

            // 验证服务商是否存在
            var provider = await _providerRepository.GetByCodeAsync(request.ProviderCode);
            if (provider == null)
            {
                _logger.LogWarning("服务商不存在，服务商编码：{ProviderCode}", request.ProviderCode);
                return false;
            }

            if (!provider.IsEnabled)
            {
                _logger.LogWarning("服务商已禁用，服务商编码：{ProviderCode}", request.ProviderCode);
                return false;
            }

            // 验证服务商类型是否匹配
            if (!string.Equals(provider.ProviderType, request.MessageType, StringComparison.OrdinalIgnoreCase))
            {
                _logger.LogWarning("服务商类型不匹配，服务商类型：{ProviderType}，消息类型：{MessageType}",
                    provider.ProviderType, request.MessageType);
                return false;
            }

            // 检查是否已存在相同配置
            var existing = await Repository.GetByAccountAndProviderAsync(request.AccountCode, request.ProviderCode);
            if (existing != null)
            {
                _logger.LogWarning("账户-服务商配置已存在，账户：{AccountCode}，服务商：{ProviderCode}",
                    request.AccountCode, request.ProviderCode);
                return false;
            }

            // 创建新的配置
            var entity = new MessageAccountProviderEntity
            {
                Key = KeyGenerator.GenerateKey(),
                AccountCode = request.AccountCode,
                ProviderCode = request.ProviderCode,
                ProviderType = request.MessageType,
                Priority = request.Priority,
                QuotaTotal = request.QuotaTotal,
                QuotaUsed = 0,
                ConfigJson = request.ProviderConfig,
                IsEnabled = request.IsEnabled
            };

            var result = await Repository.InsertAsync(entity);

            if (result)
            {
                _logger.LogInformation("创建账户-服务商关联成功，账户：{AccountCode}，服务商：{ProviderCode}，类型：{MessageType}",
                    request.AccountCode, request.ProviderCode, request.MessageType);
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建账户-服务商关联失败，账户：{AccountCode}，服务商：{ProviderCode}",
                request.AccountCode, request.ProviderCode);
            return false;
        }
    }

    /// <summary>
    /// 更新账户-服务商配置
    /// </summary>
    /// <param name="id">配置ID</param>
    /// <param name="request">更新请求</param>
    /// <returns>更新结果</returns>
    public async Task<bool> UpdateAccountProviderAsync(long id, UpdateAccountProviderRequestDto request)
    {
        if (request == null)
        {
            _logger.LogWarning("更新请求不能为空");
            return false;
        }

        try
        {
            var entity = await Repository.GetAsync(x => x.Key.Equals(id));
            if (entity == null)
            {
                _logger.LogWarning("账户-服务商配置不存在，ID：{ConfigId}", id);
                return false;
            }

            // 更新配置
            entity.Priority = request.Priority;
            entity.QuotaTotal = request.QuotaTotal;
            entity.ConfigJson = request.ProviderConfig;
            entity.IsEnabled = request.IsEnabled;

            var result = await Repository.UpdateAsync(entity);

            if (result)
            {
                _logger.LogInformation("更新账户-服务商配置成功，ID：{ConfigId}，账户：{AccountCode}，服务商：{ProviderCode}",
                    id, entity.AccountCode, entity.ProviderCode);
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新账户-服务商配置失败，ID：{ConfigId}", id);
            return false;
        }
    }

    /// <summary>
    /// 启用/禁用账户-服务商配置
    /// </summary>
    /// <param name="id">配置ID</param>
    /// <param name="isEnabled">是否启用</param>
    /// <returns>操作结果</returns>
    public async Task<bool> SetEnabledAsync(long id, bool isEnabled)
    {
        try
        {
            var entity = await Repository.GetAsync(x => x.Key.Equals(id));
            if (entity == null)
            {
                _logger.LogWarning("账户-服务商配置不存在，ID：{ConfigId}", id);
                return false;
            }

            entity.IsEnabled = isEnabled;
            var result = await Repository.UpdateAsync(entity);

            _logger.LogInformation("账户-服务商配置状态更新{Status}，ID：{ConfigId}，账户：{AccountCode}，服务商：{ProviderCode}",
                isEnabled ? "启用" : "禁用", id, entity.AccountCode, entity.ProviderCode);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新账户-服务商配置状态失败，ID：{ConfigId}", id);
            return false;
        }
    }

    /// <summary>
    /// 删除账户-服务商关联
    /// </summary>
    /// <param name="accountCode">账户编码</param>
    /// <param name="providerCode">服务商编码</param>
    /// <returns>删除结果</returns>
    public async Task<bool> RemoveAccountProviderAsync(string accountCode, string providerCode)
    {
        if (string.IsNullOrWhiteSpace(accountCode) || string.IsNullOrWhiteSpace(providerCode))
        {
            _logger.LogWarning("账户编码和服务商编码不能为空");
            return false;
        }

        try
        {
            var entity = await Repository.GetByAccountAndProviderAsync(accountCode, providerCode);
            if (entity == null)
            {
                _logger.LogWarning("账户-服务商配置不存在，账户：{AccountCode}，服务商：{ProviderCode}",
                    accountCode, providerCode);
                return false;
            }

            var result = await Repository.DeleteAsync(entity);

            if (result)
            {
                _logger.LogInformation("删除账户-服务商关联成功，账户：{AccountCode}，服务商：{ProviderCode}",
                    accountCode, providerCode);
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除账户-服务商关联失败，账户：{AccountCode}，服务商：{ProviderCode}",
                accountCode, providerCode);
            return false;
        }
    }

    /// <summary>
    /// 获取账户的可用服务商列表（有剩余额度的）
    /// </summary>
    /// <param name="accountCode">账户编码</param>
    /// <param name="messageType">消息类型</param>
    /// <returns>可用服务商列表</returns>
    public async Task<List<MessageAccountProviderDto>> GetAvailableProvidersAsync(string accountCode,
        string messageType)
    {
        if (string.IsNullOrWhiteSpace(accountCode) || string.IsNullOrWhiteSpace(messageType))
        {
            _logger.LogWarning("账户编码和消息类型不能为空");
            return new List<MessageAccountProviderDto>();
        }

        try
        {
            var entities = await Repository.GetByAccountCodeAsync(accountCode);

            // 筛选启用的、类型匹配的、有剩余额度的配置
            var availableEntities = entities
                .Where(x => x.IsEnabled &&
                            string.Equals(x.MessageType, messageType, StringComparison.OrdinalIgnoreCase) &&
                            (x.QuotaTotal ?? 0) > (x.QuotaUsed ?? 0))
                .OrderByDescending(x => x.Priority)
                .ThenBy(x => x.QuotaUsed)
                .ToList();

            return Mapper.Map<List<MessageAccountProviderDto>>(availableEntities);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取可用服务商列表失败，账户：{AccountCode}，类型：{MessageType}",
                accountCode, messageType);
            return new List<MessageAccountProviderDto>();
        }
    }
}
