
namespace XJ.Framework.Messaging.Application.Services;

/// <summary>
/// MessageAccountProvider 服务实现
/// </summary>
public sealed class MessageAccountProviderService :
    BaseEditableAppService<long, MessageAccountProviderEntity, MessageAccountProviderDto, MessageAccountProviderOperationDto, IMessageAccountProviderRepository, MessageAccountProviderQueryCriteria>,
    IMessageAccountProviderService
{
    public MessageAccountProviderService(IMessageAccountProviderRepository repository, IMapper mapper, IUnitOfWork unitOfWork,IKeyGenerator<long> keyGenerator, ICurrentUserContext currentUserContext) : base(repository, mapper, unitOfWork, keyGenerator, currentUserContext)
    {
    }
} 