
namespace XJ.Framework.Messaging.Application.Services;

/// <summary>
/// MessageProvider 服务实现
/// </summary>
public sealed class MessageProviderService :
    BaseEditableAppService<long, MessageProviderEntity, MessageProviderDto, MessageProviderOperationDto, IMessageProviderRepository, MessageProviderQueryCriteria>,
    IMessageProviderService
{
    public MessageProviderService(IMessageProviderRepository repository, IMapper mapper, IUnitOfWork unitOfWork,IKeyGenerator<long> keyGenerator, ICurrentUserContext currentUserContext) : base(repository, mapper, unitOfWork, keyGenerator, currentUserContext)
    {
    }
} 