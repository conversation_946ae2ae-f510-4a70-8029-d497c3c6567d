
namespace XJ.Framework.Messaging.Application.Services;

/// <summary>
/// MessageSend 服务实现
/// </summary>
public sealed class MessageSendService :
    BaseEditableAppService<long, MessageSendEntity, MessageSendDto, MessageSendOperationDto, IMessageSendRepository, MessageSendQueryCriteria>,
    IMessageSendService
{
    public MessageSendService(IMessageSendRepository repository, IMapper mapper, IUnitOfWork unitOfWork,IKeyGenerator<long> keyGenerator, ICurrentUserContext currentUserContext) : base(repository, mapper, unitOfWork, keyGenerator, currentUserContext)
    {
    }
} 