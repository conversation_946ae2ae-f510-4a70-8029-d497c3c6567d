
using XJ.Framework.Messaging.Domain.Shared.Dtos;

namespace XJ.Framework.Messaging.Application.Services;

/// <summary>
/// MessageSend 服务实现
/// </summary>
public sealed class MessageSendService :
    BaseEditableAppService<long, MessageSendEntity, MessageSendDto, MessageSendOperationDto, IMessageSendRepository, MessageSendQueryCriteria>,
    IMessageSendService
{
    public MessageSendService(IMessageSendRepository repository, IMapper mapper, IUnitOfWork unitOfWork,IKeyGenerator<long> keyGenerator, ICurrentUserContext currentUserContext) : base(repository, mapper, unitOfWork, keyGenerator, currentUserContext)
    {
    }

    /// <summary>
    /// 发送消息
    /// </summary>
    /// <param name="request">消息发送请求</param>
    /// <returns>发送结果</returns>
    public async Task<MessageSendResultDto> SendMessageAsync(MessageSendRequestDto request)
    {
        try
        {
            // 创建消息发送实体
            var sendEntity = new MessageSendEntity
            {
                Key = KeyGenerator.GenerateKey(),
                AppCode = request.AppCode,
                Target = request.Target,
                TemplateCode = request.TemplateCode,
                VariablesJson = request.VariablesJson,
                Priority = request.Priority,
                MessageType = request.MessageType,
                ProviderCode = request.ProviderCode,
                CreateTime = DateTimeOffset.Now,
                Status = 0, // 待发送
                TryTime = 0,
                CorrelationId = request.CorrelationId,
                CorrelationType = request.CorrelationType
            };

            // 插入数据库
            var success = await Repository.InsertAsync(sendEntity);

            if (success)
            {
                return new MessageSendResultDto
                {
                    SendId = sendEntity.Key,
                    Success = true,
                    Message = "消息发送任务已成功提交",
                    Status = 0,
                    CreateTime = sendEntity.CreateTime
                };
            }
            else
            {
                return new MessageSendResultDto
                {
                    SendId = 0,
                    Success = false,
                    ErrorMessage = "消息发送任务提交失败",
                    Status = -1,
                    CreateTime = DateTimeOffset.Now
                };
            }
        }
        catch (Exception ex)
        {
            return new MessageSendResultDto
            {
                SendId = 0,
                Success = false,
                ErrorMessage = $"消息发送任务提交异常: {ex.Message}",
                Status = -1,
                CreateTime = DateTimeOffset.Now
            };
        }
    }

    /// <summary>
    /// 获取发送状态
    /// </summary>
    /// <param name="sendId">发送记录ID</param>
    /// <returns>发送状态信息</returns>
    public async Task<MessageSendResultDto?> GetSendStatusAsync(long sendId)
    {
        var entity = await Repository.GetAsync(q => q.Key.Equals(sendId));

        if (entity == null)
        {
            return null;
        }

        return new MessageSendResultDto
        {
            SendId = entity.Key,
            Success = entity.Status == 2, // 2表示发送成功
            Message = entity.Status == 2 ? "发送成功" : GetStatusMessage(entity.Status),
            ErrorMessage = entity.ErrorInfo,
            Status = entity.Status,
            CreateTime = entity.CreateTime
        };
    }

    /// <summary>
    /// 获取状态描述
    /// </summary>
    /// <param name="status">状态码</param>
    /// <returns>状态描述</returns>
    private static string GetStatusMessage(int status)
    {
        return status switch
        {
            0 => "待发送",
            1 => "发送中",
            2 => "发送成功",
            3 => "发送失败",
            _ => "未知状态"
        };
    }
}
