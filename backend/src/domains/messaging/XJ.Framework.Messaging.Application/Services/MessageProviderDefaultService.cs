
using Microsoft.Extensions.Logging;
using XJ.Framework.Messaging.Domain.Shared.Dtos;

namespace XJ.Framework.Messaging.Application.Services;

/// <summary>
/// MessageProviderDefault 服务实现
/// </summary>
public sealed class MessageProviderDefaultService :
    BaseEditableAppService<long, MessageProviderDefaultEntity, MessageProviderDefaultDto, MessageProviderDefaultOperationDto, IMessageProviderDefaultRepository, MessageProviderDefaultQueryCriteria>,
    IMessageProviderDefaultService
{
    private readonly ILogger<MessageProviderDefaultService> _logger;
    private readonly IMessageProviderRepository _providerRepository;

    public MessageProviderDefaultService(
        IMessageProviderDefaultRepository repository,
        IMapper mapper,
        IUnitOfWork unitOfWork,
        IKeyGenerator<long> keyGenerator,
        ICurrentUserContext currentUserContext,
        ILogger<MessageProviderDefaultService> logger,
        IMessageProviderRepository providerRepository)
        : base(repository, mapper, unitOfWork, keyGenerator, currentUserContext)
    {
        _logger = logger;
        _providerRepository = providerRepository;
    }

    /// <summary>
    /// 根据应用编码和消息类型获取默认服务商
    /// </summary>
    /// <param name="appCode">应用编码</param>
    /// <param name="messageType">消息类型</param>
    /// <returns>默认服务商配置</returns>
    public async Task<MessageProviderDefaultDto?> GetDefaultProviderAsync(string appCode, string messageType)
    {
        if (string.IsNullOrWhiteSpace(appCode) || string.IsNullOrWhiteSpace(messageType))
        {
            _logger.LogWarning("应用编码和消息类型不能为空");
            return null;
        }

        var entity = await Repository.GetDefaultProviderAsync(appCode, messageType);
        return entity == null ? null : Mapper.Map<MessageProviderDefaultDto>(entity);
    }

    /// <summary>
    /// 根据应用编码获取所有默认服务商配置
    /// </summary>
    /// <param name="appCode">应用编码</param>
    /// <returns>默认服务商配置列表</returns>
    public async Task<List<MessageProviderDefaultDto>> GetByAppCodeAsync(string appCode)
    {
        if (string.IsNullOrWhiteSpace(appCode))
        {
            _logger.LogWarning("应用编码不能为空");
            return new List<MessageProviderDefaultDto>();
        }

        var entities = await Repository.GetByAppCodeAsync(appCode);
        return Mapper.Map<List<MessageProviderDefaultDto>>(entities);
    }
    /// <summary>
    /// 设置应用的默认服务商
    /// </summary>
    /// <param name="appCode">应用编码</param>
    /// <param name="messageType">消息类型</param>
    /// <param name="providerCode">服务商编码</param>
    /// <param name="priority">优先级</param>
    /// <returns>设置结果</returns>
    public async Task<bool> SetDefaultProviderAsync(string appCode, string messageType, string providerCode, int priority = 0)
    {
        if (string.IsNullOrWhiteSpace(appCode) || string.IsNullOrWhiteSpace(messageType) || string.IsNullOrWhiteSpace(providerCode))
        {
            _logger.LogWarning("应用编码、消息类型和服务商编码不能为空");
            return false;
        }

        try
        {
            // 验证服务商是否存在且启用
            var provider = await _providerRepository.GetByCodeAsync(providerCode);
            if (provider == null)
            {
                _logger.LogWarning("服务商不存在，服务商编码：{ProviderCode}", providerCode);
                return false;
            }

            if (!provider.IsEnabled)
            {
                _logger.LogWarning("服务商已禁用，服务商编码：{ProviderCode}", providerCode);
                return false;
            }

            // 验证服务商类型是否匹配
            if (!string.Equals(provider.ProviderType, messageType, StringComparison.OrdinalIgnoreCase))
            {
                _logger.LogWarning("服务商类型不匹配，服务商类型：{ProviderType}，消息类型：{MessageType}",
                    provider.ProviderType, messageType);
                return false;
            }

            // 检查是否已存在相同配置
            var exists = await Repository.ExistsAsync(appCode, messageType, providerCode);
            if (exists)
            {
                _logger.LogWarning("默认服务商配置已存在，应用：{AppCode}，类型：{MessageType}，服务商：{ProviderCode}",
                    appCode, messageType, providerCode);
                return false;
            }

            // 创建新的默认服务商配置
            var entity = new MessageProviderDefaultEntity
            {
                Key = KeyGenerator.GenerateKey(),
                AppCode = appCode,
                MessageType = messageType,
                ProviderCode = providerCode,
                Priority = priority,
                IsEnabled = true
            };

            var result = await Repository.InsertAsync(entity);

            if (result)
            {
                _logger.LogInformation("设置默认服务商成功，应用：{AppCode}，类型：{MessageType}，服务商：{ProviderCode}，优先级：{Priority}",
                    appCode, messageType, providerCode, priority);
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "设置默认服务商失败，应用：{AppCode}，类型：{MessageType}，服务商：{ProviderCode}",
                appCode, messageType, providerCode);
            return false;
        }
    }

    /// <summary>
    /// 删除应用的默认服务商配置
    /// </summary>
    /// <param name="appCode">应用编码</param>
    /// <param name="messageType">消息类型</param>
    /// <returns>删除结果</returns>
    public async Task<bool> RemoveDefaultProviderAsync(string appCode, string messageType)
    {
        if (string.IsNullOrWhiteSpace(appCode) || string.IsNullOrWhiteSpace(messageType))
        {
            _logger.LogWarning("应用编码和消息类型不能为空");
            return false;
        }

        try
        {
            var entities = await Repository.GetDefaultProvidersByTypeAsync(appCode, messageType);
            if (!entities.Any())
            {
                _logger.LogWarning("未找到默认服务商配置，应用：{AppCode}，类型：{MessageType}", appCode, messageType);
                return false;
            }

            var deleteCount = 0;
            foreach (var entity in entities)
            {
                if (await Repository.DeleteAsync(entity))
                {
                    deleteCount++;
                }
            }

            _logger.LogInformation("删除默认服务商配置成功，应用：{AppCode}，类型：{MessageType}，删除数量：{Count}",
                appCode, messageType, deleteCount);

            return deleteCount > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除默认服务商配置失败，应用：{AppCode}，类型：{MessageType}", appCode, messageType);
            return false;
        }
    }
    /// <summary>
    /// 启用/禁用默认服务商配置
    /// </summary>
    /// <param name="id">配置ID</param>
    /// <param name="isEnabled">是否启用</param>
    /// <returns>操作结果</returns>
    public async Task<bool> SetEnabledAsync(long id, bool isEnabled)
    {
        try
        {
            var entity = await Repository.GetAsync(x => x.Key.Equals(id));
            if (entity == null)
            {
                _logger.LogWarning("默认服务商配置不存在，ID：{ConfigId}", id);
                return false;
            }

            entity.IsEnabled = isEnabled;
            var result = await Repository.UpdateAsync(entity);

            _logger.LogInformation("默认服务商配置状态更新{Status}，ID：{ConfigId}，应用：{AppCode}，类型：{MessageType}，服务商：{ProviderCode}",
                isEnabled ? "启用" : "禁用", id, entity.AppCode, entity.MessageType, entity.ProviderCode);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新默认服务商配置状态失败，ID：{ConfigId}", id);
            return false;
        }
    }

    /// <summary>
    /// 获取消息类型的所有默认服务商（按优先级排序）
    /// </summary>
    /// <param name="appCode">应用编码</param>
    /// <param name="messageType">消息类型</param>
    /// <returns>默认服务商列表</returns>
    public async Task<List<MessageProviderDefaultDto>> GetDefaultProvidersByTypeAsync(string appCode, string messageType)
    {
        if (string.IsNullOrWhiteSpace(appCode) || string.IsNullOrWhiteSpace(messageType))
        {
            _logger.LogWarning("应用编码和消息类型不能为空");
            return new List<MessageProviderDefaultDto>();
        }

        try
        {
            var entities = await Repository.GetDefaultProvidersByTypeAsync(appCode, messageType);
            return Mapper.Map<List<MessageProviderDefaultDto>>(entities);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取默认服务商列表失败，应用：{AppCode}，类型：{MessageType}", appCode, messageType);
            return new List<MessageProviderDefaultDto>();
        }
    }
