
namespace XJ.Framework.Messaging.Application.Services;

/// <summary>
/// MessageProviderDefault 服务实现
/// </summary>
public sealed class MessageProviderDefaultService :
    BaseEditableAppService<long, MessageProviderDefaultEntity, MessageProviderDefaultDto, MessageProviderDefaultOperationDto, IMessageProviderDefaultRepository, MessageProviderDefaultQueryCriteria>,
    IMessageProviderDefaultService
{
    public MessageProviderDefaultService(IMessageProviderDefaultRepository repository, IMapper mapper, IUnitOfWork unitOfWork,IKeyGenerator<long> keyGenerator, ICurrentUserContext currentUserContext) : base(repository, mapper, unitOfWork, keyGenerator, currentUserContext)
    {
    }
} 