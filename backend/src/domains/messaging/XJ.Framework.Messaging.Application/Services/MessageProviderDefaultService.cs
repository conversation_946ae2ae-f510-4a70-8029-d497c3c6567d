using Microsoft.Extensions.Logging;
using XJ.Framework.Messaging.Domain.Shared.Dtos;
using XJ.Framework.Messaging.Domain.Shared.Enums;

namespace XJ.Framework.Messaging.Application.Services;

/// <summary>
/// MessageProviderDefault 服务实现
/// </summary>
public sealed class MessageProviderDefaultService :
    BaseEditableAppService<long, MessageProviderDefaultEntity, MessageProviderDefaultDto,
        MessageProviderDefaultOperationDto, IMessageProviderDefaultRepository, MessageProviderDefaultQueryCriteria>,
    IMessageProviderDefaultService
{
    private readonly ILogger<MessageProviderDefaultService> _logger;
    private readonly IMessageProviderRepository _providerRepository;

    public MessageProviderDefaultService(
        IMessageProviderDefaultRepository repository,
        IMapper mapper,
        IUnitOfWork unitOfWork,
        IKeyGenerator<long> keyGenerator,
        ICurrentUserContext currentUserContext,
        ILogger<MessageProviderDefaultService> logger,
        IMessageProviderRepository providerRepository)
        : base(repository, mapper, unitOfWork, keyGenerator, currentUserContext)
    {
        _logger = logger;
        _providerRepository = providerRepository;
    }

    /// <summary>
    /// 根据应用编码和消息类型获取默认服务商
    /// </summary>
    /// <param name="appCode">应用编码</param>
    /// <param name="messageType">消息类型</param>
    /// <returns>默认服务商配置</returns>
    public async Task<MessageProviderDefaultDto?> GetDefaultProviderAsync(string appCode, string messageType)
    {
        if (string.IsNullOrWhiteSpace(appCode) || string.IsNullOrWhiteSpace(messageType))
        {
            _logger.LogWarning("应用编码和消息类型不能为空");
            return null;
        }

        var entity = await Repository.GetDefaultProviderAsync(appCode, messageType);
        return entity == null ? null : Mapper.Map<MessageProviderDefaultDto>(entity);
    }

    /// <summary>
    /// 根据应用编码获取所有默认服务商配置
    /// </summary>
    /// <param name="appCode">应用编码</param>
    /// <returns>默认服务商配置列表</returns>
    public async Task<List<MessageProviderDefaultDto>> GetByAppCodeAsync(string appCode)
    {
        if (string.IsNullOrWhiteSpace(appCode))
        {
            _logger.LogWarning("应用编码不能为空");
            return new List<MessageProviderDefaultDto>();
        }

        var entities = await Repository.GetByAppCodeAsync(appCode);
        return Mapper.Map<List<MessageProviderDefaultDto>>(entities);
    }

    /// <summary>
    /// 设置应用的默认服务商（同一个appCode+messageType只能有一个默认）
    /// </summary>
    /// <param name="appCode">应用编码</param>
    /// <param name="messageType">消息类型</param>
    /// <param name="providerCode">服务商编码</param>
    /// <returns>设置结果</returns>
    public async Task<bool> SetDefaultProviderAsync(string appCode, string messageType, string providerCode)
    {
        if (string.IsNullOrWhiteSpace(appCode) || string.IsNullOrWhiteSpace(messageType) ||
            string.IsNullOrWhiteSpace(providerCode))
        {
            _logger.LogWarning("应用编码、消息类型和服务商编码不能为空");
            return false;
        }

        try
        {
            // 验证服务商是否存在且启用
            var provider = await _providerRepository.GetByCodeAsync(providerCode);
            if (provider == null)
            {
                _logger.LogWarning("服务商不存在，服务商编码：{ProviderCode}", providerCode);
                return false;
            }

            if (!provider.IsEnabled)
            {
                _logger.LogWarning("服务商已禁用，服务商编码：{ProviderCode}", providerCode);
                return false;
            }

            // 验证服务商类型是否匹配
            if (!MessageTypeExtensions.TryParseFromString(messageType, out var parsedMessageType) ||
                !string.Equals(provider.ProviderType, parsedMessageType.ToStringValue(), StringComparison.OrdinalIgnoreCase))
            {
                _logger.LogWarning("服务商类型不匹配，服务商类型：{ProviderType}，消息类型：{MessageType}",
                    provider.ProviderType, messageType);
                return false;
            }

            // 检查是否已存在该应用和消息类型的默认配置
            var existingDefault = await Repository.GetDefaultProviderAsync(appCode, messageType);

            if (existingDefault != null)
            {
                // 如果已存在且是相同的服务商，直接返回成功
                if (existingDefault.ProviderCode == providerCode)
                {
                    _logger.LogInformation("默认服务商配置已存在且相同，应用：{AppCode}，类型：{MessageType}，服务商：{ProviderCode}",
                        appCode, messageType, providerCode);
                    return true;
                }

                // 如果存在不同的默认服务商，先删除旧的
                await Repository.DeleteAsync(existingDefault);
                _logger.LogInformation("删除旧的默认服务商配置，应用：{AppCode}，类型：{MessageType}，旧服务商：{OldProviderCode}",
                    appCode, messageType, existingDefault.ProviderCode);
            }

            // 创建新的默认服务商配置
            var entity = new MessageProviderDefaultEntity
            {
                Key = KeyGenerator.GenerateKey(),
                AppCode = appCode,
                ProviderType = messageType,
                ProviderCode = providerCode
            };

            var result = await Repository.InsertAsync(entity);

            if (result)
            {
                _logger.LogInformation("设置默认服务商成功，应用：{AppCode}，类型：{MessageType}，服务商：{ProviderCode}",
                    appCode, messageType, providerCode);
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "设置默认服务商失败，应用：{AppCode}，类型：{MessageType}，服务商：{ProviderCode}",
                appCode, messageType, providerCode);
            return false;
        }
    }

    /// <summary>
    /// 删除应用的默认服务商配置
    /// </summary>
    /// <param name="appCode">应用编码</param>
    /// <param name="messageType">消息类型</param>
    /// <returns>删除结果</returns>
    public async Task<bool> RemoveDefaultProviderAsync(string appCode, string messageType)
    {
        if (string.IsNullOrWhiteSpace(appCode) || string.IsNullOrWhiteSpace(messageType))
        {
            _logger.LogWarning("应用编码和消息类型不能为空");
            return false;
        }

        try
        {
            var entity = await Repository.GetDefaultProviderAsync(appCode, messageType);
            if (entity == null)
            {
                _logger.LogWarning("未找到默认服务商配置，应用：{AppCode}，类型：{MessageType}", appCode, messageType);
                return false;
            }

            var result = await Repository.DeleteAsync(entity);

            if (result)
            {
                _logger.LogInformation("删除默认服务商配置成功，应用：{AppCode}，类型：{MessageType}，服务商：{ProviderCode}",
                    appCode, messageType, entity.ProviderCode);
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除默认服务商配置失败，应用：{AppCode}，类型：{MessageType}", appCode, messageType);
            return false;
        }
    }
}
