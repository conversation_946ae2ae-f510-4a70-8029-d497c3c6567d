
namespace XJ.Framework.Messaging.Application.Services;

/// <summary>
/// MessageAccountUsage 服务实现
/// </summary>
public sealed class MessageAccountUsageService :
    BaseEditableAppService<long, MessageAccountUsageEntity, MessageAccountUsageDto, MessageAccountUsageOperationDto, IMessageAccountUsageRepository, MessageAccountUsageQueryCriteria>,
    IMessageAccountUsageService
{
    public MessageAccountUsageService(IMessageAccountUsageRepository repository, IMapper mapper, IUnitOfWork unitOfWork,IKeyGenerator<long> keyGenerator, ICurrentUserContext currentUserContext) : base(repository, mapper, unitOfWork, keyGenerator, currentUserContext)
    {
    }
} 