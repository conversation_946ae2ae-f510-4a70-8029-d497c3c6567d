
namespace XJ.Framework.Messaging.Application.Services;

/// <summary>
/// MessageTemplate 服务实现
/// </summary>
public sealed class MessageTemplateService :
    BaseEditableAppService<long, MessageTemplateEntity, MessageTemplateDto, MessageTemplateOperationDto, IMessageTemplateRepository, MessageTemplateQueryCriteria>,
    IMessageTemplateService
{
    public MessageTemplateService(IMessageTemplateRepository repository, IMapper mapper, IUnitOfWork unitOfWork,IKeyGenerator<long> keyGenerator, ICurrentUserContext currentUserContext) : base(repository, mapper, unitOfWork, keyGenerator, currentUserContext)
    {
    }
} 