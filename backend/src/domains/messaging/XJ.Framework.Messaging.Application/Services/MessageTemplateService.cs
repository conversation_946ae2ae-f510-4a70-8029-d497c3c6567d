
using XJ.Framework.Messaging.Domain.Shared.Dtos;

namespace XJ.Framework.Messaging.Application.Services;

/// <summary>
/// MessageTemplate 服务实现
/// </summary>
public sealed class MessageTemplateService :
    BaseEditableAppService<long, MessageTemplateEntity, MessageTemplateDto, MessageTemplateOperationDto, IMessageTemplateRepository, MessageTemplateQueryCriteria>,
    IMessageTemplateService
{
    public MessageTemplateService(IMessageTemplateRepository repository, IMapper mapper, IUnitOfWork unitOfWork,IKeyGenerator<long> keyGenerator, ICurrentUserContext currentUserContext) : base(repository, mapper, unitOfWork, keyGenerator, currentUserContext)
    {
    }

    /// <summary>
    /// 根据模板编码和应用编码获取模板
    /// </summary>
    /// <param name="templateCode">模板编码</param>
    /// <param name="appCode">应用编码</param>
    /// <returns>模板信息</returns>
    public async Task<MessageTemplateDto?> GetByCodeAsync(string templateCode, string appCode)
    {
        var entity = await Repository.GetByCodeAsync(templateCode, appCode);
        return entity == null ? null : Mapper.Map<MessageTemplateDto>(entity);
    }

    /// <summary>
    /// 根据应用编码获取模板列表
    /// </summary>
    /// <param name="appCode">应用编码</param>
    /// <returns>模板列表</returns>
    public async Task<List<MessageTemplateDto>> GetByAppCodeAsync(string appCode)
    {
        var entities = await Repository.GetByAppCodeAsync(appCode);
        return Mapper.Map<List<MessageTemplateDto>>(entities);
    }

    /// <summary>
    /// 启用/禁用模板
    /// </summary>
    /// <param name="id">模板ID</param>
    /// <param name="isEnabled">是否启用</param>
    /// <returns>操作结果</returns>
    public async Task<bool> SetEnabledAsync(long id, bool isEnabled)
    {
        var entity = await Repository.GetAsync(x => x.Key.Equals(id));
        if (entity == null)
            return false;

        entity.IsEnabled = isEnabled;
        return await Repository.UpdateAsync(entity);
    }
}
