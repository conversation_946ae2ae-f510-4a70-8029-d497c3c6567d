using Microsoft.Extensions.DependencyInjection;
using XJ.Framework.Messaging.Application.Contract.Interfaces;

namespace XJ.Framework.Messaging.Application.Services;

/// <summary>
/// 消息Provider工厂实现
/// </summary>
public class MessageProviderFactory : IMessageProviderFactory
{
    private readonly IServiceProvider _serviceProvider;
    private readonly Dictionary<string, Type> _providerTypes;

    public MessageProviderFactory(IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
        _providerTypes = new Dictionary<string, Type>(StringComparer.OrdinalIgnoreCase);

        // 注册Provider类型
        RegisterProviders();
    }

    /// <summary>
    /// 根据消息类型获取Provider
    /// </summary>
    /// <param name="messageType">消息类型</param>
    /// <returns>Provider实例</returns>
    public IMessageProvider? GetProvider(string messageType)
    {
        if (string.IsNullOrWhiteSpace(messageType))
            return null;

        if (!_providerTypes.TryGetValue(messageType, out var providerType))
            return null;

        return _serviceProvider.GetService(providerType) as IMessageProvider;
    }

    /// <summary>
    /// 获取所有支持的消息类型
    /// </summary>
    /// <returns>消息类型列表</returns>
    public IEnumerable<string> GetSupportedMessageTypes()
    {
        return _providerTypes.Keys;
    }

    /// <summary>
    /// 注册Provider类型
    /// </summary>
    private void RegisterProviders()
    {
        // 通过反射自动发现所有IMessageProvider实现
        var providerTypes = AppDomain.CurrentDomain.GetAssemblies()
            .SelectMany(assembly => assembly.GetTypes())
            .Where(type => typeof(IMessageProvider).IsAssignableFrom(type) &&
                           !type.IsInterface &&
                           !type.IsAbstract)
            .ToList();

        foreach (var providerType in providerTypes)
        {
            try
            {
                // 创建临时实例获取ProviderType
                if (Activator.CreateInstance(providerType,
                        _serviceProvider.GetService<Microsoft.Extensions.Logging.ILoggerFactory>()
                            ?.CreateLogger(providerType.ToString())) is IMessageProvider tempInstance)
                {
                    _providerTypes[tempInstance.ProviderType] = providerType;
                }
            }
            catch
            {
                // 忽略创建失败的Provider
            }
        }
    }
}
