
namespace XJ.Framework.Messaging.Domain.Shared.Dtos;

/// <summary>
/// MessageAccountProvider DTO
/// </summary>
public class MessageAccountProviderDto : BaseDto<long>
{
    /// <summary>
    /// 账户编码
    /// </summary>
    public string AccountCode { get; set; } = null!;

    /// <summary>
    /// 服务商编码
    /// </summary>
    public string ProviderCode { get; set; } = null!;

    /// <summary>
    /// 优先级
    /// </summary>
    public int Priority { get; set; }

    /// <summary>
    /// 账户专属配置
    /// </summary>
    public string ConfigJson { get; set; } = null!;

    /// <summary>
    /// 总额度
    /// </summary>
    public int? QuotaTotal { get; set; }

    /// <summary>
    /// 已用额度
    /// </summary>
    public int? QuotaUsed { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; set; }

} 