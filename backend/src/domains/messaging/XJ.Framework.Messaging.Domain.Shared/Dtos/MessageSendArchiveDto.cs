
namespace XJ.Framework.Messaging.Domain.Shared.Dtos;

/// <summary>
/// MessageSendArchive DTO
/// </summary>
public class MessageSendArchiveDto : BaseDto<long>
{
    /// <summary>
    /// 应用编码
    /// </summary>
    public string AppCode { get; set; } = null!;

    /// <summary>
    /// 发送目标
    /// </summary>
    public string Target { get; set; } = null!;

    /// <summary>
    /// 模板编码
    /// </summary>
    public string TemplateCode { get; set; } = null!;

    /// <summary>
    /// 变量内容
    /// </summary>
    public string VariablesJson { get; set; } = null!;

    /// <summary>
    /// 优先级
    /// </summary>
    public int Priority { get; set; }

    /// <summary>
    /// 消息类型
    /// </summary>
    public string MessageType { get; set; } = null!;

    /// <summary>
    /// 服务商编码
    /// </summary>
    public string? ProviderCode { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTimeOffset CreateTime { get; set; }

    /// <summary>
    /// 发送时间
    /// </summary>
    public DateTimeOffset? SendTime { get; set; }

    /// <summary>
    /// 发送状态
    /// </summary>
    public int Status { get; set; }

    /// <summary>
    /// 重试次数
    /// </summary>
    public int TryTime { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorInfo { get; set; }

    /// <summary>
    /// 关联ID
    /// </summary>
    public string? CorrelationId { get; set; }

    /// <summary>
    /// 关联类型
    /// </summary>
    public string? CorrelationType { get; set; }

    /// <summary>
    /// 归档时间
    /// </summary>
    public DateTimeOffset ArchiveTime { get; set; }
    
} 
