using System.ComponentModel.DataAnnotations;

namespace XJ.Framework.Messaging.Domain.Shared.Dtos;

/// <summary>
/// 消息发送请求 DTO
/// </summary>
public class MessageSendRequestDto
{
    /// <summary>
    /// 应用编码
    /// </summary>
    [Required(ErrorMessage = "应用编码不能为空")]
    [StringLength(100, ErrorMessage = "应用编码长度不能超过100个字符")]
    public string AppCode { get; set; } = null!;

    /// <summary>
    /// 发送目标（手机号、邮箱等）
    /// </summary>
    [Required(ErrorMessage = "发送目标不能为空")]
    [StringLength(400, ErrorMessage = "发送目标长度不能超过400个字符")]
    public string Target { get; set; } = null!;

    /// <summary>
    /// 模板编码
    /// </summary>
    [Required(ErrorMessage = "模板编码不能为空")]
    [StringLength(200, ErrorMessage = "模板编码长度不能超过200个字符")]
    public string TemplateCode { get; set; } = null!;

    /// <summary>
    /// 变量内容（JSON格式）
    /// </summary>
    [Required(ErrorMessage = "变量内容不能为空")]
    public string VariablesJson { get; set; } = null!;

    /// <summary>
    /// 优先级（数值越大优先级越高，默认为0）
    /// </summary>
    public int Priority { get; set; } = 0;

    /// <summary>
    /// 消息类型（sms、email、push等）
    /// </summary>
    [Required(ErrorMessage = "消息类型不能为空")]
    [StringLength(40, ErrorMessage = "消息类型长度不能超过40个字符")]
    public string MessageType { get; set; } = null!;

    /// <summary>
    /// 指定服务商编码（可选，不指定则自动选择）
    /// </summary>
    [StringLength(100, ErrorMessage = "服务商编码长度不能超过100个字符")]
    public string? ProviderCode { get; set; }

    /// <summary>
    /// 关联ID（用于业务关联，可选）
    /// </summary>
    [StringLength(200, ErrorMessage = "关联ID长度不能超过200个字符")]
    public string? CorrelationId { get; set; }

    /// <summary>
    /// 关联类型（用于业务关联，可选）
    /// </summary>
    [StringLength(100, ErrorMessage = "关联类型长度不能超过100个字符")]
    public string? CorrelationType { get; set; }
}
