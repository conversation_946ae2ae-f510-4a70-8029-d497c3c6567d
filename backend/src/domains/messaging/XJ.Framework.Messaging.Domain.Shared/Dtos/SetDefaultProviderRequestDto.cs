using System.ComponentModel.DataAnnotations;

namespace XJ.Framework.Messaging.Domain.Shared.Dtos;

/// <summary>
/// 设置默认服务商请求DTO
/// </summary>
public class SetDefaultProviderRequestDto
{
    /// <summary>
    /// 应用编码
    /// </summary>
    [Required(ErrorMessage = "应用编码不能为空")]
    [StringLength(100, ErrorMessage = "应用编码长度不能超过100个字符")]
    public string AppCode { get; set; } = null!;

    /// <summary>
    /// 消息类型
    /// </summary>
    [Required(ErrorMessage = "消息类型不能为空")]
    [StringLength(40, ErrorMessage = "消息类型长度不能超过40个字符")]
    public string MessageType { get; set; } = null!;

    /// <summary>
    /// 服务商编码
    /// </summary>
    [Required(ErrorMessage = "服务商编码不能为空")]
    [StringLength(100, ErrorMessage = "服务商编码长度不能超过100个字符")]
    public string ProviderCode { get; set; } = null!;


}
