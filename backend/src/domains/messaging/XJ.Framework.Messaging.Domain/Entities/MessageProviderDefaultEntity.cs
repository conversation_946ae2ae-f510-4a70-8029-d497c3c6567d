using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using XJ.Framework.Library.Domain.Attributes;

namespace XJ.Framework.Messaging.Domain.Entities;

/// <summary>
/// MessageProviderDefault 实体
/// </summary>
[Table("message_provider_default", Schema = "m")]
[SoftDeleteIndex("IX_provider_default", nameof(AppCode), nameof(ProviderType), IsUnique = true)]
public class MessageProviderDefaultEntity : BaseSoftDeleteEntity<long>
{
    /// <summary>
    /// 应用编码
    /// </summary>
    [Column("app_code")]
    [StringLength(100)]
    public required string AppCode { get; set; } = null!;

    /// <summary>
    /// 服务商类型
    /// </summary>
    [Column("provider_type")]
    [StringLength(40)]
    public required string ProviderType { get; set; } = null!;

    /// <summary>
    /// 服务商编码
    /// </summary>
    [Column("provider_code")]
    [StringLength(100)]
    public required string ProviderCode { get; set; } = null!;
}
