using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using XJ.Framework.Library.Domain.Attributes;

namespace XJ.Framework.Messaging.Domain.Entities;

/// <summary>
/// MessageProvider 实体
/// </summary>
[Table("message_provider", Schema = "m")]
[SoftDeleteIndex("UQ_message_provider_type_name", nameof(ProviderType), nameof(ProviderName), IsUnique = true)]
public class MessageProviderEntity : BaseSoftDeleteEntity<long>
{
    /// <summary>
    /// 服务商编码
    /// </summary>
    [Column("provider_code")]
    [StringLength(100)]
    public required string ProviderCode { get; set; } = null!;

    /// <summary>
    /// 服务商名称
    /// </summary>
    [Column("provider_name")]
    [StringLength(200)]
    public required string ProviderName { get; set; } = null!;

    /// <summary>
    /// 服务商类型
    /// </summary>
    [Column("provider_type")]
    [StringLength(40)]
    public required string ProviderType { get; set; } = null!;

    /// <summary>
    /// 服务商配置
    /// </summary>
    [Column("config")]
    [StringLength(2000)]
    public required string Config { get; set; } = null!;

    /// <summary>
    /// 是否启用
    /// </summary>
    [Column("is_enabled")]
    public required bool IsEnabled { get; set; }
}
