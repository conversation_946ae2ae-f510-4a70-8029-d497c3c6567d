using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using XJ.Framework.Library.Domain.Attributes;

namespace XJ.Framework.Messaging.Domain.Entities;

/// <summary>
/// MessageAccountProvider 实体
/// </summary>
[Table("message_account_provider", Schema = "m")]
[SoftDeleteIndex("UQ_account_provider", nameof(AccountCode),
    nameof(ProviderCode), IsUnique = true)]
[SoftDeleteIndex("IX_account_provider_priority", nameof(AccountCode), nameof(Priority))]
public class MessageAccountProviderEntity : BaseSoftDeleteEntity<long>
{
    /// <summary>
    /// 账户编码
    /// </summary>
    [Column("account_code")]
    [StringLength(100)]
    public required string AccountCode { get; set; } = null!;

    /// <summary>
    /// 服务商编码
    /// </summary>
    [Column("provider_code")]
    [StringLength(100)]
    public required string ProviderCode { get; set; } = null!;
    
    /// <summary>
    /// 服务商类型
    /// </summary>
    [Column("provider_type")]
    [StringLength(50)]
    public required string ProviderType { get; set; } = null!;

    /// <summary>
    /// 优先级
    /// </summary>
    [Column("priority")]
    public required int Priority { get; set; }

    /// <summary>
    /// 账户专属配置
    /// </summary>
    [Column("config_json")]
    [StringLength(-1)]
    public required string ConfigJson { get; set; } = null!;

    /// <summary>
    /// 总额度
    /// </summary>
    [Column("quota_total")]
    public int? QuotaTotal { get; set; }

    /// <summary>
    /// 已用额度
    /// </summary>
    [Column("quota_used")]
    public int? QuotaUsed { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    [Column("is_enabled")]
    public required bool IsEnabled { get; set; }
}
