using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using XJ.Framework.Library.Domain.Attributes;

namespace XJ.Framework.Messaging.Domain.Entities;

/// <summary>
/// MessageTemplate 实体
/// </summary>
[Table("message_template", Schema = "m")]
[SoftDeleteIndex("UQ_message_template_appcode_code", nameof(AppCode), nameof(TemplateCode), IsUnique = true)]
public class MessageTemplateEntity : BaseSoftDeleteEntity<long>
{
    /// <summary>
    /// 模板编码
    /// </summary>
    [Column("template_code")]
    [StringLength(200)]
    public required string TemplateCode { get; set; } = null!;

    /// <summary>
    /// 应用编码
    /// </summary>
    [Column("app_code")]
    [StringLength(100)]
    public required string AppCode { get; set; } = null!;

    /// <summary>
    /// 模板名称
    /// </summary>
    [Column("template_name")]
    [StringLength(400)]
    public required string TemplateName { get; set; } = null!;

    /// <summary>
    /// 模板类型
    /// </summary>
    [Column("template_type")]
    [StringLength(50)]
    public string TemplateType { get; set; } = null!;

    /// <summary>
    /// 模板内容
    /// </summary>
    [Column("content")]
    [StringLength(-1)]
    public required string Content { get; set; } = null!;

    /// <summary>
    /// 变量定义
    /// </summary>
    [Column("variables")]
    [StringLength(1000)]
    public string? Variable { get; set; }

    /// <summary>
    /// 语言
    /// </summary>
    [Column("lang")]
    [StringLength(40)]
    public string? Lang { get; set; }

    /// <summary>
    /// 服务商编码
    /// </summary>
    [Column("provider_code")]
    [StringLength(100)]
    public string? ProviderCode { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    [Column("is_enabled")]
    public required bool IsEnabled { get; set; }
}
