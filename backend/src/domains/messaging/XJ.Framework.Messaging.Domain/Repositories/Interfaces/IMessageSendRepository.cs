
namespace XJ.Framework.Messaging.Domain.Repositories.Interfaces;

/// <summary>
/// MessageSend 仓储接口
/// </summary>

public interface IMessageSendRepository : ISoftDeleteRepository<long, MessageSendEntity>
{
    /// <summary>
    /// 获取待发送的消息
    /// </summary>
    /// <param name="maxCount">最大数量</param>
    /// <param name="maxRetryTimes">最大重试次数</param>
    /// <returns>待发送消息列表</returns>
    Task<List<MessageSendEntity>> GetPendingMessagesAsync(int maxCount = 50, int maxRetryTimes = 3);

    /// <summary>
    /// 根据状态获取消息数量
    /// </summary>
    /// <param name="status">状态</param>
    /// <returns>消息数量</returns>
    Task<int> GetCountByStatusAsync(int status);
}
