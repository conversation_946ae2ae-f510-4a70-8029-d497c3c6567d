
namespace XJ.Framework.Messaging.Domain.Repositories.Interfaces;

/// <summary>
/// MessageProviderDefault 仓储接口
/// </summary>

public interface IMessageProviderDefaultRepository : IAuditRepository<long, MessageProviderDefaultEntity>
{
    /// <summary>
    /// 根据应用编码和消息类型获取默认服务商
    /// </summary>
    /// <param name="appCode">应用编码</param>
    /// <param name="messageType">消息类型</param>
    /// <returns>默认服务商配置</returns>
    Task<MessageProviderDefaultEntity?> GetDefaultProviderAsync(string appCode, string messageType);

    /// <summary>
    /// 根据应用编码获取所有默认服务商配置
    /// </summary>
    /// <param name="appCode">应用编码</param>
    /// <returns>默认服务商配置列表</returns>
    Task<List<MessageProviderDefaultEntity>> GetByAppCodeAsync(string appCode);

    /// <summary>
    /// 根据应用编码和消息类型获取所有默认服务商（按优先级排序）
    /// </summary>
    /// <param name="appCode">应用编码</param>
    /// <param name="messageType">消息类型</param>
    /// <returns>默认服务商列表</returns>
    Task<List<MessageProviderDefaultEntity>> GetDefaultProvidersByTypeAsync(string appCode, string messageType);

    /// <summary>
    /// 检查默认服务商配置是否存在
    /// </summary>
    /// <param name="appCode">应用编码</param>
    /// <param name="messageType">消息类型</param>
    /// <param name="providerCode">服务商编码</param>
    /// <returns>是否存在</returns>
    Task<bool> ExistsAsync(string appCode, string messageType, string providerCode);
}
