
namespace XJ.Framework.Messaging.Domain.Repositories.Interfaces;

/// <summary>
/// MessageAccountProvider 仓储接口
/// </summary>

public interface IMessageAccountProviderRepository : IAuditRepository<long, MessageAccountProviderEntity>
{
    /// <summary>
    /// 根据账户编码获取账户-服务商配置
    /// </summary>
    /// <param name="accountCode">账户编码</param>
    /// <returns>账户-服务商配置列表</returns>
    Task<List<MessageAccountProviderEntity>> GetByAccountCodeAsync(string accountCode);

    /// <summary>
    /// 根据服务商编码获取账户-服务商配置
    /// </summary>
    /// <param name="providerCode">服务商编码</param>
    /// <returns>账户-服务商配置列表</returns>
    Task<List<MessageAccountProviderEntity>> GetByProviderCodeAsync(string providerCode);

    /// <summary>
    /// 根据账户编码和服务商编码获取配置
    /// </summary>
    /// <param name="accountCode">账户编码</param>
    /// <param name="providerCode">服务商编码</param>
    /// <returns>账户-服务商配置</returns>
    Task<MessageAccountProviderEntity?> GetByAccountAndProviderAsync(string accountCode, string providerCode);

    /// <summary>
    /// 根据账户编码和服务商编码获取配置（使用行级锁）
    /// </summary>
    /// <param name="accountCode">账户编码</param>
    /// <param name="providerCode">服务商编码</param>
    /// <returns>账户-服务商配置</returns>
    Task<MessageAccountProviderEntity?> GetByAccountAndProviderWithLockAsync(string accountCode, string providerCode);
}
