using XJ.Framework.Itmctr.Domain.Entities;
using XJ.Framework.Itmctr.Domain.Repositories.Interfaces;

namespace XJ.Framework.Itmctr.EntityFrameworkCore.Repositories
{
    /// <summary>
    /// 测量指标 仓储实现
    /// </summary>
    public class ProjectMeasurementRepository : BaseAuditRepository<ItmctrDbContext, long, ProjectMeasurementEntity>,
       IProjectMeasurementRepository
    {
        public ProjectMeasurementRepository(IServiceProvider serviceProvider) : base(
            serviceProvider)
        {
        }
    }
}
