using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace XJ.Framework.Itmctr.EntityFrameworkCore.Migrations
{
    /// <inheritdoc />
    public partial class Initial : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.EnsureSchema(
                name: "i");

            migrationBuilder.CreateTable(
                name: "async_tasks",
                schema: "i",
                columns: table => new
                {
                    id = table.Column<long>(type: "bigint", nullable: false),
                    task_code = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false, comment: "任务编码"),
                    business_id = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false, comment: "业务id"),
                    task_data = table.Column<string>(type: "nvarchar(max)", nullable: false, comment: "任务数据"),
                    task_result = table.Column<string>(type: "nvarchar(max)", nullable: true, comment: "任务结果"),
                    task_status = table.Column<int>(type: "int", nullable: false, comment: "任务数据"),
                    apply_user_id = table.Column<long>(type: "bigint", nullable: false, comment: "发起人id"),
                    created_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    last_modified_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    last_modified_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_async_tasks", x => x.id);
                },
                comment: "异步任务");

            migrationBuilder.CreateTable(
                name: "project_attach",
                schema: "i",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    project_id = table.Column<long>(type: "bigint", nullable: false, comment: "项目ID"),
                    business_id = table.Column<string>(type: "nvarchar(450)", nullable: false, comment: "关联业务id"),
                    version = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false, comment: "版本"),
                    file_id = table.Column<Guid>(type: "uniqueidentifier", nullable: false, comment: "文件id"),
                    file_name = table.Column<string>(type: "nvarchar(510)", maxLength: 510, nullable: false, comment: "文件原始名称"),
                    file_size = table.Column<long>(type: "bigint", nullable: false, comment: "文件总大小（字节）"),
                    file_type_code = table.Column<string>(type: "nvarchar(450)", nullable: false, comment: "文件类型code"),
                    created_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    last_modified_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    last_modified_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_project_attach", x => x.id);
                },
                comment: "项目基本信息附件");

            migrationBuilder.CreateTable(
                name: "project_attach_history",
                schema: "i",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    project_id = table.Column<long>(type: "bigint", nullable: false, comment: "项目ID"),
                    business_id = table.Column<string>(type: "nvarchar(450)", nullable: false, comment: "关联业务id"),
                    version = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false, comment: "版本"),
                    file_id = table.Column<Guid>(type: "uniqueidentifier", nullable: false, comment: "文件id"),
                    file_name = table.Column<string>(type: "nvarchar(510)", maxLength: 510, nullable: false, comment: "文件原始名称"),
                    file_size = table.Column<long>(type: "bigint", nullable: false, comment: "文件总大小（字节）"),
                    file_type_code = table.Column<string>(type: "nvarchar(450)", nullable: false, comment: "文件类型code"),
                    created_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    last_modified_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    last_modified_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_project_attach_history", x => x.id);
                },
                comment: "项目基本信息附件历史记录");

            migrationBuilder.CreateTable(
                name: "project_humansample",
                schema: "i",
                columns: table => new
                {
                    id = table.Column<long>(type: "bigint", nullable: false),
                    project_id = table.Column<long>(type: "bigint", nullable: false, comment: "项目ID"),
                    business_id = table.Column<string>(type: "nvarchar(450)", nullable: false, comment: "关联业务id"),
                    version = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false, comment: "版本"),
                    sample_name_Zh = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "标本中文名（中文）"),
                    sample_name_En = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "标本中文名（英文）"),
                    tissue_Zh = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "组织（中文）"),
                    tissue_En = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "组织（英文）"),
                    fate_of_sample = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "人体标本去向"),
                    sample_note_Zh = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "说明（中文）"),
                    sample_note_En = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "说明（英文）"),
                    row_index = table.Column<int>(type: "int", nullable: true, comment: "排序"),
                    created_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    last_modified_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    last_modified_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_project_humansample", x => x.id);
                },
                comment: "采集人体标本历史记录");

            migrationBuilder.CreateTable(
                name: "project_humansample_history",
                schema: "i",
                columns: table => new
                {
                    id = table.Column<long>(type: "bigint", nullable: false),
                    project_id = table.Column<long>(type: "bigint", nullable: false, comment: "项目ID"),
                    business_id = table.Column<string>(type: "nvarchar(450)", nullable: false, comment: "关联业务id"),
                    version = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false, comment: "版本"),
                    sample_name_Zh = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "标本中文名（中文）"),
                    sample_name_En = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "标本中文名（英文）"),
                    tissue_Zh = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "组织（中文）"),
                    tissue_En = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "组织（英文）"),
                    fate_of_sample = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "人体标本去向"),
                    sample_note_Zh = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "说明（中文）"),
                    sample_note_En = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "说明（英文）"),
                    row_index = table.Column<int>(type: "int", nullable: true, comment: "排序"),
                    created_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    last_modified_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    last_modified_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_project_humansample_history", x => x.id);
                },
                comment: "采集人体标本历史记录");

            migrationBuilder.CreateTable(
                name: "project_intervention",
                schema: "i",
                columns: table => new
                {
                    id = table.Column<long>(type: "bigint", nullable: false),
                    project_id = table.Column<long>(type: "bigint", nullable: false, comment: "项目ID"),
                    business_id = table.Column<string>(type: "nvarchar(450)", nullable: false, comment: "关联业务id"),
                    version = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false, comment: "版本"),
                    intervention_group_Zh = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "组别（中文）"),
                    intervention_group_En = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "组别（英文）"),
                    intervention_sample_size = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "样本量"),
                    intervention_name_Zh = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "干预措施（中文）"),
                    intervention_name_En = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "干预措施（英文）"),
                    intervention_code = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "干预措施代码"),
                    row_index = table.Column<int>(type: "int", nullable: true, comment: "排序"),
                    created_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    last_modified_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    last_modified_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_project_intervention", x => x.id);
                },
                comment: "干预措施");

            migrationBuilder.CreateTable(
                name: "project_intervention_history",
                schema: "i",
                columns: table => new
                {
                    id = table.Column<long>(type: "bigint", nullable: false),
                    project_id = table.Column<long>(type: "bigint", nullable: false, comment: "项目ID"),
                    business_id = table.Column<string>(type: "nvarchar(450)", nullable: false, comment: "关联业务id"),
                    version = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false, comment: "版本"),
                    intervention_group_Zh = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "组别（中文）"),
                    intervention_group_En = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "组别（英文）"),
                    intervention_sample_size = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "样本量"),
                    intervention_name_Zh = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "干预措施（中文）"),
                    intervention_name_En = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "干预措施（英文）"),
                    intervention_code = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "干预措施代码"),
                    row_index = table.Column<int>(type: "int", nullable: true, comment: "排序"),
                    created_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    last_modified_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    last_modified_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_project_intervention_history", x => x.id);
                },
                comment: "干预措施历史记录");

            migrationBuilder.CreateTable(
                name: "project_measurement",
                schema: "i",
                columns: table => new
                {
                    id = table.Column<long>(type: "bigint", nullable: false),
                    project_id = table.Column<long>(type: "bigint", nullable: false, comment: "项目ID"),
                    business_id = table.Column<string>(type: "nvarchar(450)", nullable: false, comment: "关联业务id"),
                    version = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false, comment: "版本"),
                    outcome_name_Zh = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "指标中文名（中文）"),
                    outcome_name_En = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "指标中文名（英文）"),
                    outcome_type = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "指标类型"),
                    measure_time_point_Zh = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "测量时间点（中文）"),
                    measure_time_point_En = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "测量时间点（英文）"),
                    measure_method_Zh = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "测量方法（中文）"),
                    measure_method_En = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "测量方法（英文）"),
                    row_index = table.Column<int>(type: "int", nullable: true, comment: "排序"),
                    created_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    last_modified_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    last_modified_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_project_measurement", x => x.id);
                },
                comment: "测量指标");

            migrationBuilder.CreateTable(
                name: "project_measurement_history",
                schema: "i",
                columns: table => new
                {
                    id = table.Column<long>(type: "bigint", nullable: false),
                    project_id = table.Column<long>(type: "bigint", nullable: false, comment: "项目ID"),
                    business_id = table.Column<string>(type: "nvarchar(450)", nullable: false, comment: "关联业务id"),
                    version = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false, comment: "版本"),
                    outcome_name_Zh = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "指标中文名（中文）"),
                    outcome_name_En = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "指标中文名（英文）"),
                    outcome_type = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "指标类型"),
                    measure_time_point_Zh = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "测量时间点（中文）"),
                    measure_time_point_En = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "测量时间点（英文）"),
                    measure_method_Zh = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "测量方法（中文）"),
                    measure_method_En = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "测量方法（英文）"),
                    row_index = table.Column<int>(type: "int", nullable: true, comment: "排序"),
                    created_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    last_modified_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    last_modified_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_project_measurement_history", x => x.id);
                },
                comment: "测量指标历史记录");

            migrationBuilder.CreateTable(
                name: "project_research_site",
                schema: "i",
                columns: table => new
                {
                    id = table.Column<long>(type: "bigint", nullable: false),
                    project_id = table.Column<long>(type: "bigint", nullable: false, comment: "项目ID"),
                    business_id = table.Column<string>(type: "nvarchar(450)", nullable: false, comment: "关联业务id"),
                    version = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false, comment: "版本"),
                    site_country_Zh = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "国家（中文）"),
                    site_country_En = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "国家（英文）"),
                    site_province_Zh = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "省(直辖市)（中文）"),
                    site_province_En = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "省(直辖市)（英文）"),
                    site_city_Zh = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "市(区县)（中文）"),
                    site_city_En = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "市(区县)（英文）"),
                    site_institution_Zh = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "单位(医院)（中文）"),
                    site_institution_En = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "单位(医院)（英文）"),
                    site_level_Zh = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "单位级别（中文）"),
                    site_level_En = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "单位级别（英文）"),
                    row_index = table.Column<int>(type: "int", nullable: true, comment: "排序"),
                    created_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    last_modified_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    last_modified_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_project_research_site", x => x.id);
                },
                comment: "研究实施地点");

            migrationBuilder.CreateTable(
                name: "project_research_site_history",
                schema: "i",
                columns: table => new
                {
                    id = table.Column<long>(type: "bigint", nullable: false),
                    project_id = table.Column<long>(type: "bigint", nullable: false, comment: "项目ID"),
                    business_id = table.Column<string>(type: "nvarchar(450)", nullable: false, comment: "关联业务id"),
                    version = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false, comment: "版本"),
                    site_country_Zh = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "国家（中文）"),
                    site_country_En = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "国家（英文）"),
                    site_province_Zh = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "省(直辖市)（中文）"),
                    site_province_En = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "省(直辖市)（英文）"),
                    site_city_Zh = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "市(区县)（中文）"),
                    site_city_En = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "市(区县)（英文）"),
                    site_institution_Zh = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "单位(医院)（中文）"),
                    site_institution_En = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "单位(医院)（英文）"),
                    site_level_Zh = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "单位级别（中文）"),
                    site_level_En = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "单位级别（英文）"),
                    row_index = table.Column<int>(type: "int", nullable: true, comment: "排序"),
                    created_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    last_modified_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    last_modified_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_project_research_site_history", x => x.id);
                },
                comment: "研究实施地点历史记录");

            migrationBuilder.CreateTable(
                name: "project_sponsor",
                schema: "i",
                columns: table => new
                {
                    id = table.Column<long>(type: "bigint", nullable: false),
                    project_id = table.Column<long>(type: "bigint", nullable: false, comment: "项目ID"),
                    business_id = table.Column<string>(type: "nvarchar(450)", nullable: false, comment: "关联业务id"),
                    version = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false, comment: "版本"),
                    sponsor_country_Zh = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true, comment: "国家（中文）"),
                    sponsor_country_En = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true, comment: "国家（英文）"),
                    sponsor_province_Zh = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true, comment: "省(直辖市)（中文）"),
                    sponsor_province_En = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true, comment: "省(直辖市)（英文）"),
                    sponsor_city_Zh = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true, comment: "市(区县)（中文）"),
                    sponsor_city_En = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true, comment: "市(区县)（英文）"),
                    sponsor_institution_Zh = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true, comment: "单位（中文）"),
                    sponsor_institution_En = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true, comment: "单位（英文）"),
                    sponsor_address_Zh = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true, comment: "具体地址（中文）"),
                    sponsor_address_En = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true, comment: "具体地址（英文）"),
                    row_index = table.Column<int>(type: "int", nullable: true, comment: "排序"),
                    created_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    last_modified_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    last_modified_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_project_sponsor", x => x.id);
                },
                comment: "试验主办单位");

            migrationBuilder.CreateTable(
                name: "project_sponsor_history",
                schema: "i",
                columns: table => new
                {
                    id = table.Column<long>(type: "bigint", nullable: false),
                    project_id = table.Column<long>(type: "bigint", nullable: false, comment: "项目ID"),
                    business_id = table.Column<string>(type: "nvarchar(450)", nullable: false, comment: "关联业务id"),
                    version = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false, comment: "版本"),
                    sponsor_country_Zh = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true, comment: "国家（中文）"),
                    sponsor_country_En = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true, comment: "国家（英文）"),
                    sponsor_province_Zh = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true, comment: "省(直辖市)（中文）"),
                    sponsor_province_En = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true, comment: "省(直辖市)（英文）"),
                    sponsor_city_Zh = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true, comment: "市(区县)（中文）"),
                    sponsor_city_En = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true, comment: "市(区县)（英文）"),
                    sponsor_institution_Zh = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true, comment: "单位（中文）"),
                    sponsor_institution_En = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true, comment: "单位（英文）"),
                    sponsor_address_Zh = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true, comment: "具体地址（中文）"),
                    sponsor_address_En = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true, comment: "具体地址（英文）"),
                    row_index = table.Column<int>(type: "int", nullable: true, comment: "排序"),
                    created_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    last_modified_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    last_modified_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_project_sponsor_history", x => x.id);
                },
                comment: "试验主办单位历史记录");

            migrationBuilder.CreateTable(
                name: "projects",
                schema: "i",
                columns: table => new
                {
                    id = table.Column<long>(type: "bigint", nullable: false),
                    language = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false, comment: "语言"),
                    business_id = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false, comment: "关联业务id"),
                    version = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false, comment: "版本号"),
                    form_code = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false, comment: "表单code"),
                    registration_number = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "注册号"),
                    registration_status = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "注册号状态"),
                    publictitle_Zh = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "注册题目中文"),
                    publictitle_En = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "注册题目英文"),
                    english_acronym_Zh = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "注册题目简写中文"),
                    english_acronym_En = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "注册题目简写英文"),
                    scientific_title_Zh = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "研究课题的正式科学名称中文"),
                    scientific_title_En = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "研究课题的正式科学名称英文"),
                    scientific_title_acronym_Zh = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "研究课题的正式科学名称简写（中文）"),
                    scientific_title_acronym_En = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "研究课题的正式科学名称简写（英文）"),
                    study_subject_id = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "研究课题代号(代码)"),
                    partner_registry_number = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "在二级注册机构或其它机构的注册号"),
                    applicant_Zh = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "申请注册联系人（中文）"),
                    applicant_En = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "申请注册联系人（英文）"),
                    study_leader_Zh = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "研究负责人（中文）"),
                    study_leader_En = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "研究负责人（英文）"),
                    applicant_telephone = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "申请注册联系人电话"),
                    study_leader_telephone = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "研究负责人电话"),
                    applicant_fax = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "申请注册联系人传真"),
                    study_leader_fax = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "研究负责人传真"),
                    applicant_email = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "申请注册联系人电子邮件"),
                    study_leader_email = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "研究负责人电子邮件"),
                    applicant_website = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "申请单位网址(自愿提供)"),
                    study_leader_website = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "研究负责人网址(自愿提供)"),
                    applicant_address_Zh = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "申请注册联系人通讯地址（中文）"),
                    applicant_address_En = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "申请注册联系人通讯地址（英文）"),
                    study_leader_address_Zh = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "研究负责人通讯地址（中文）"),
                    study_leader_address_En = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "研究负责人通讯地址（英文）"),
                    applicant_postcode = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "申请注册联系人邮政编码"),
                    study_leader_postcode = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "研究负责人邮政编码"),
                    applicant_affiliation_Zh = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "申请人所在单位（中文）"),
                    applicant_affiliation_En = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "申请人所在单位（英文）"),
                    study_leader_affiliation_Zh = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "研究负责人所在单位（中文）"),
                    study_leader_affiliation_En = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "研究负责人所在单位（英文）"),
                    ethic_committee_approved = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "是否获伦理委员会批准"),
                    ethic_committee_approved_no = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "伦理委员会批件文号"),
                    ethic_committee_name_Zh = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "批准本研究的伦理委员会名称（中文）"),
                    ethic_committee_name_En = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "批准本研究的伦理委员会名称（英文）"),
                    ethic_committee_approved_date = table.Column<DateTime>(type: "datetime2", nullable: true, comment: "伦理委员会批准日期"),
                    ethic_committee_contact_Zh = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "伦理委员会联系人（中文）"),
                    ethic_committee_contact_En = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "伦理委员会联系人（英文）"),
                    ethic_committee_address_Zh = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "伦理委员会联系地址（中文）"),
                    ethic_committee_address_En = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "伦理委员会联系地址（英文）"),
                    ethic_committee_phone = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "伦理委员会联系人电话"),
                    ethic_committee_email = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "伦理委员会联系人邮箱"),
                    mpa_approved_no = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "国家药监局批准文号"),
                    mpa_approved_date = table.Column<DateTime>(type: "datetime2", nullable: true, comment: "国家药监局批准日期"),
                    primary_sponsor_Zh = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "研究实施负责（组长）单位（中文）"),
                    primary_sponsor_En = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "研究实施负责（组长）单位（英文）"),
                    primary_sponsor_address_Zh = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "研究实施负责（组长）单位地址（中文）"),
                    primary_sponsor_address_En = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "研究实施负责（组长）单位地址（英文）"),
                    funding_source_Zh = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "经费或物资来源（中文）"),
                    funding_source_En = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "经费或物资来源（英文）"),
                    target_disease_Zh = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "研究疾病（中文）"),
                    target_disease_En = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "研究疾病（英文）"),
                    target_disease_code = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "研究疾病代码"),
                    study_type = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "研究类型"),
                    study_design = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "研究设计"),
                    study_phase = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "研究所处阶段"),
                    study_objectives_Zh = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true, comment: "研究目的（中文）"),
                    study_objectives_En = table.Column<string>(type: "nvarchar(1500)", maxLength: 1500, nullable: true, comment: "研究目的（英文）"),
                    treatment_description_Zh = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true, comment: "药物成份或治疗方案详述（中文）"),
                    treatment_description_En = table.Column<string>(type: "nvarchar(1500)", maxLength: 1500, nullable: true, comment: "药物成份或治疗方案详述（英文）"),
                    inclusion_criteria_Zh = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true, comment: "纳入标准（中文）"),
                    inclusion_criteria_En = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: true, comment: "纳入标准（英文）"),
                    exclusion_criteria_Zh = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true, comment: "排除标准（中文）"),
                    exclusion_criteria_En = table.Column<string>(type: "nvarchar(1500)", maxLength: 1500, nullable: true, comment: "排除标准（英文）"),
                    study_time_start = table.Column<DateTime>(type: "datetime2", nullable: true, comment: "研究实施时间开始"),
                    study_time_end = table.Column<DateTime>(type: "datetime2", nullable: true, comment: "研究实施时间结束"),
                    recruiting_time_start = table.Column<DateTime>(type: "datetime2", nullable: true, comment: "征募观察对象时间开始"),
                    recruiting_time_end = table.Column<DateTime>(type: "datetime2", nullable: true, comment: "征募观察对象时间结束"),
                    gold_standard_Zh = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true, comment: "金标准或参考标准（中文）"),
                    gold_standard_En = table.Column<string>(type: "nvarchar(1500)", maxLength: 1500, nullable: true, comment: "金标准或参考标准（英文）"),
                    index_test_Zh = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "指标试验（中文）"),
                    index_test_En = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "指标试验（英文）"),
                    target_condition_Zh = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "目标人群（中文）"),
                    target_condition_En = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "目标人群（英文）"),
                    target_sample_size = table.Column<string>(type: "nvarchar(max)", nullable: true, comment: "目标人群例数"),
                    confounding_condition_Zh = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "容易混淆的疾病人群（中文）"),
                    confounding_condition_En = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "容易混淆的疾病人群（英文）"),
                    confounding_sample_size = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "容易混淆的疾病人群例数"),
                    intervention_total_sample_size = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "干预措施样本总量"),
                    recruiting_status = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "征募研究对象状况"),
                    age_range_min = table.Column<int>(type: "int", nullable: true, comment: "年龄范围开始"),
                    age_range_max = table.Column<int>(type: "int", nullable: true, comment: "年龄范围结束"),
                    gender = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "性别"),
                    randomization_procedure_Zh = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true, comment: "随机方法（请说明由何人用什么方法产生随机序列）（中文）"),
                    randomization_procedure_En = table.Column<string>(type: "nvarchar(1500)", maxLength: 1500, nullable: true, comment: "随机方法（请说明由何人用什么方法产生随机序列）（英文）"),
                    sign_informed_consent = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "研究对象是否签署知情同意书"),
                    follow_up_length = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "随访时间"),
                    follow_up_unit = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "随访时间单位"),
                    allocation_concealment_Zh = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true, comment: "隐蔽分组方法和过程（中文）"),
                    allocation_concealment_En = table.Column<string>(type: "nvarchar(1500)", maxLength: 1500, nullable: true, comment: "隐蔽分组方法和过程（英文）"),
                    blinding_Zh = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true, comment: "盲法（中文）"),
                    blinding_En = table.Column<string>(type: "nvarchar(1500)", maxLength: 1500, nullable: true, comment: "盲法（英文）"),
                    unblinding_rules_Zh = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true, comment: "揭盲或破盲原则和方法（中文）"),
                    unblinding_rules_En = table.Column<string>(type: "nvarchar(1500)", maxLength: 1500, nullable: true, comment: "揭盲或破盲原则和方法（英文）"),
                    statistical_methods_Zh = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "统计方法名称（中文）"),
                    statistical_methods_En = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "统计方法名称（英文）"),
                    calculated_results_Zh = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "试验完成后的统计结果（中文）"),
                    calculated_results_En = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "试验完成后的统计结果（英文）"),
                    calculated_results_public = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true, comment: "是否公开试验完成后的统计结果"),
                    utn = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "全球唯一识别码"),
                    ipd_sharing = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true, comment: "是否共享原始数据"),
                    ipd_sharing_way_Zh = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "共享原始数据的方式（中文）"),
                    ipd_sharing_way_En = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "共享原始数据的方式（英文）"),
                    data_collection_Zh = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "数据采集和管理（中文）"),
                    data_collection_En = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "数据采集和管理（英文）"),
                    safety_committee = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true, comment: "数据与安全监察委员会"),
                    publication_info_Zh = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "研究计划书或研究结果报告发表信息（中文）"),
                    publication_info_En = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "研究计划书或研究结果报告发表信息（英文）"),
                    send_number_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true, comment: "发号时间"),
                    first_submit_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true, comment: "首次提交时间"),
                    created_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    last_modified_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    last_modified_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_projects", x => x.id);
                },
                comment: "项目信息");

            migrationBuilder.CreateTable(
                name: "projects_history",
                schema: "i",
                columns: table => new
                {
                    id = table.Column<long>(type: "bigint", nullable: false),
                    language = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false, comment: "语言"),
                    business_id = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false, comment: "关联业务id"),
                    version = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false, comment: "版本号"),
                    form_code = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false, comment: "表单code"),
                    registration_number = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "注册号"),
                    registration_status = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "注册号状态"),
                    publictitle_Zh = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "注册题目中文"),
                    publictitle_En = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "注册题目英文"),
                    english_acronym_Zh = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "注册题目简写中文"),
                    english_acronym_En = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "注册题目简写英文"),
                    scientific_title_Zh = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "研究课题的正式科学名称中文"),
                    scientific_title_En = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "研究课题的正式科学名称英文"),
                    scientific_title_acronym_Zh = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "研究课题的正式科学名称简写（中文）"),
                    scientific_title_acronym_En = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "研究课题的正式科学名称简写（英文）"),
                    study_subject_id = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "研究课题代号(代码)"),
                    partner_registry_number = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "在二级注册机构或其它机构的注册号"),
                    applicant_Zh = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "申请注册联系人（中文）"),
                    applicant_En = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "申请注册联系人（英文）"),
                    study_leader_Zh = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "研究负责人（中文）"),
                    study_leader_En = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "研究负责人（英文）"),
                    applicant_telephone = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "申请注册联系人电话"),
                    study_leader_telephone = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "研究负责人电话"),
                    applicant_fax = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "申请注册联系人传真"),
                    study_leader_fax = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "研究负责人传真"),
                    applicant_email = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "申请注册联系人电子邮件"),
                    study_leader_email = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "研究负责人电子邮件"),
                    applicant_website = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "申请单位网址(自愿提供)"),
                    study_leader_website = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "研究负责人网址(自愿提供)"),
                    applicant_address_Zh = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "申请注册联系人通讯地址（中文）"),
                    applicant_address_En = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "申请注册联系人通讯地址（英文）"),
                    study_leader_address_Zh = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "研究负责人通讯地址（中文）"),
                    study_leader_address_En = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "研究负责人通讯地址（英文）"),
                    applicant_postcode = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "申请注册联系人邮政编码"),
                    study_leader_postcode = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "研究负责人邮政编码"),
                    applicant_affiliation_Zh = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "申请人所在单位（中文）"),
                    applicant_affiliation_En = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "申请人所在单位（英文）"),
                    study_leader_affiliation_Zh = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "研究负责人所在单位（中文）"),
                    study_leader_affiliation_En = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "研究负责人所在单位（英文）"),
                    ethic_committee_approved = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "是否获伦理委员会批准"),
                    ethic_committee_approved_no = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "伦理委员会批件文号"),
                    ethic_committee_name_Zh = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "批准本研究的伦理委员会名称（中文）"),
                    ethic_committee_name_En = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "批准本研究的伦理委员会名称（英文）"),
                    ethic_committee_approved_date = table.Column<DateTime>(type: "datetime2", nullable: true, comment: "伦理委员会批准日期"),
                    ethic_committee_contact_Zh = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "伦理委员会联系人（中文）"),
                    ethic_committee_contact_En = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "伦理委员会联系人（英文）"),
                    ethic_committee_address_Zh = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "伦理委员会联系地址（中文）"),
                    ethic_committee_address_En = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "伦理委员会联系地址（英文）"),
                    ethic_committee_phone = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "伦理委员会联系人电话"),
                    ethic_committee_email = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "伦理委员会联系人邮箱"),
                    mpa_approved_no = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "国家药监局批准文号"),
                    mpa_approved_date = table.Column<DateTime>(type: "datetime2", nullable: true, comment: "国家药监局批准日期"),
                    primary_sponsor_Zh = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "研究实施负责（组长）单位（中文）"),
                    primary_sponsor_En = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "研究实施负责（组长）单位（英文）"),
                    primary_sponsor_address_Zh = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "研究实施负责（组长）单位地址（中文）"),
                    primary_sponsor_address_En = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "研究实施负责（组长）单位地址（英文）"),
                    funding_source_Zh = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "经费或物资来源（中文）"),
                    funding_source_En = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "经费或物资来源（英文）"),
                    target_disease_Zh = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "研究疾病（中文）"),
                    target_disease_En = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "研究疾病（英文）"),
                    target_disease_code = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "研究疾病代码"),
                    study_type = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "研究类型"),
                    study_design = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "研究设计"),
                    study_phase = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "研究所处阶段"),
                    study_objectives_Zh = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true, comment: "研究目的（中文）"),
                    study_objectives_En = table.Column<string>(type: "nvarchar(1500)", maxLength: 1500, nullable: true, comment: "研究目的（英文）"),
                    treatment_description_Zh = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true, comment: "药物成份或治疗方案详述（中文）"),
                    treatment_description_En = table.Column<string>(type: "nvarchar(1500)", maxLength: 1500, nullable: true, comment: "药物成份或治疗方案详述（英文）"),
                    inclusion_criteria_Zh = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true, comment: "纳入标准（中文）"),
                    inclusion_criteria_En = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: true, comment: "纳入标准（英文）"),
                    exclusion_criteria_Zh = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true, comment: "排除标准（中文）"),
                    exclusion_criteria_En = table.Column<string>(type: "nvarchar(1500)", maxLength: 1500, nullable: true, comment: "排除标准（英文）"),
                    study_time_start = table.Column<DateTime>(type: "datetime2", nullable: true, comment: "研究实施时间开始"),
                    study_time_end = table.Column<DateTime>(type: "datetime2", nullable: true, comment: "研究实施时间结束"),
                    recruiting_time_start = table.Column<DateTime>(type: "datetime2", nullable: true, comment: "征募观察对象时间开始"),
                    recruiting_time_end = table.Column<DateTime>(type: "datetime2", nullable: true, comment: "征募观察对象时间结束"),
                    gold_standard_Zh = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true, comment: "金标准或参考标准（中文）"),
                    gold_standard_En = table.Column<string>(type: "nvarchar(1500)", maxLength: 1500, nullable: true, comment: "金标准或参考标准（英文）"),
                    index_test_Zh = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "指标试验（中文）"),
                    index_test_En = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "指标试验（英文）"),
                    target_condition_Zh = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "目标人群（中文）"),
                    target_condition_En = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "目标人群（英文）"),
                    target_sample_size = table.Column<string>(type: "nvarchar(max)", nullable: true, comment: "目标人群例数"),
                    confounding_condition_Zh = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "容易混淆的疾病人群（中文）"),
                    confounding_condition_En = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "容易混淆的疾病人群（英文）"),
                    confounding_sample_size = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "容易混淆的疾病人群例数"),
                    intervention_total_sample_size = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "干预措施样本总量"),
                    recruiting_status = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "征募研究对象状况"),
                    age_range_min = table.Column<int>(type: "int", nullable: true, comment: "年龄范围开始"),
                    age_range_max = table.Column<int>(type: "int", nullable: true, comment: "年龄范围结束"),
                    gender = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "性别"),
                    randomization_procedure_Zh = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true, comment: "随机方法（请说明由何人用什么方法产生随机序列）（中文）"),
                    randomization_procedure_En = table.Column<string>(type: "nvarchar(1500)", maxLength: 1500, nullable: true, comment: "随机方法（请说明由何人用什么方法产生随机序列）（英文）"),
                    sign_informed_consent = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "研究对象是否签署知情同意书"),
                    follow_up_length = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "随访时间"),
                    follow_up_unit = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "随访时间单位"),
                    allocation_concealment_Zh = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true, comment: "隐蔽分组方法和过程（中文）"),
                    allocation_concealment_En = table.Column<string>(type: "nvarchar(1500)", maxLength: 1500, nullable: true, comment: "隐蔽分组方法和过程（英文）"),
                    blinding_Zh = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true, comment: "盲法（中文）"),
                    blinding_En = table.Column<string>(type: "nvarchar(1500)", maxLength: 1500, nullable: true, comment: "盲法（英文）"),
                    unblinding_rules_Zh = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true, comment: "揭盲或破盲原则和方法（中文）"),
                    unblinding_rules_En = table.Column<string>(type: "nvarchar(1500)", maxLength: 1500, nullable: true, comment: "揭盲或破盲原则和方法（英文）"),
                    statistical_methods_Zh = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "统计方法名称（中文）"),
                    statistical_methods_En = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "统计方法名称（英文）"),
                    calculated_results_Zh = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "试验完成后的统计结果（中文）"),
                    calculated_results_En = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "试验完成后的统计结果（英文）"),
                    calculated_results_public = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true, comment: "是否公开试验完成后的统计结果"),
                    utn = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "全球唯一识别码"),
                    ipd_sharing = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true, comment: "是否共享原始数据"),
                    ipd_sharing_way_Zh = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "共享原始数据的方式（中文）"),
                    ipd_sharing_way_En = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "共享原始数据的方式（英文）"),
                    data_collection_Zh = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "数据采集和管理（中文）"),
                    data_collection_En = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "数据采集和管理（英文）"),
                    safety_committee = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true, comment: "数据与安全监察委员会"),
                    publication_info_Zh = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "研究计划书或研究结果报告发表信息（中文）"),
                    publication_info_En = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true, comment: "研究计划书或研究结果报告发表信息（英文）"),
                    send_number_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true, comment: "发号时间"),
                    first_submit_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true, comment: "首次提交时间"),
                    created_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    last_modified_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    last_modified_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_projects_history", x => x.id);
                },
                comment: "项目信息");

            migrationBuilder.CreateIndex(
                name: "IX_ProjectAttach_BusinessId",
                schema: "i",
                table: "project_attach",
                column: "business_id",
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_ProjectAttach_FileName",
                schema: "i",
                table: "project_attach",
                column: "file_name",
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_ProjectAttach_FileTypeCode",
                schema: "i",
                table: "project_attach",
                column: "file_type_code",
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_ProjectAttach_ProjectId",
                schema: "i",
                table: "project_attach",
                column: "project_id",
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_ProjectAttach_Version",
                schema: "i",
                table: "project_attach",
                column: "version",
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_ProjectAttachHistory_BusinessId",
                schema: "i",
                table: "project_attach_history",
                column: "business_id",
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_ProjectAttachHistory_FileName",
                schema: "i",
                table: "project_attach_history",
                column: "file_name",
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_ProjectAttachHistory_FileTypeCode",
                schema: "i",
                table: "project_attach_history",
                column: "file_type_code",
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_ProjectAttachHistory_ProjectId",
                schema: "i",
                table: "project_attach_history",
                column: "project_id",
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_ProjectAttachHistory_Version",
                schema: "i",
                table: "project_attach_history",
                column: "version",
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_ProjectHumanSample_BusinessId",
                schema: "i",
                table: "project_humansample",
                column: "business_id",
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_ProjectHumanSample_ProjectId",
                schema: "i",
                table: "project_humansample",
                column: "project_id",
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_ProjectHumanSample_RowIndex",
                schema: "i",
                table: "project_humansample",
                column: "row_index",
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_ProjectHumanSample_Version",
                schema: "i",
                table: "project_humansample",
                column: "version",
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_ProjectHumanSampleHistory_BusinessId",
                schema: "i",
                table: "project_humansample_history",
                column: "business_id",
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_ProjectHumanSampleHistory_ProjectId",
                schema: "i",
                table: "project_humansample_history",
                column: "project_id",
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_ProjectHumanSampleHistory_RowIndex",
                schema: "i",
                table: "project_humansample_history",
                column: "row_index",
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_ProjectHumanSampleHistory_Version",
                schema: "i",
                table: "project_humansample_history",
                column: "version",
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_ProjectIntervention_BusinessId",
                schema: "i",
                table: "project_intervention",
                column: "business_id",
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_ProjectIntervention_ProjectId",
                schema: "i",
                table: "project_intervention",
                column: "project_id",
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_ProjectIntervention_RowIndex",
                schema: "i",
                table: "project_intervention",
                column: "row_index",
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_ProjectIntervention_Version",
                schema: "i",
                table: "project_intervention",
                column: "version",
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_ProjectInterventionHistory_BusinessId",
                schema: "i",
                table: "project_intervention_history",
                column: "business_id",
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_ProjectInterventionHistory_ProjectId",
                schema: "i",
                table: "project_intervention_history",
                column: "project_id",
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_ProjectInterventionHistory_RowIndex",
                schema: "i",
                table: "project_intervention_history",
                column: "row_index",
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_ProjectInterventionHistory_Version",
                schema: "i",
                table: "project_intervention_history",
                column: "version",
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_ProjectMeasurement_BusinessId",
                schema: "i",
                table: "project_measurement",
                column: "business_id",
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_ProjectMeasurement_ProjectId",
                schema: "i",
                table: "project_measurement",
                column: "project_id",
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_ProjectMeasurement_RowIndex",
                schema: "i",
                table: "project_measurement",
                column: "row_index",
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_ProjectMeasurement_Version",
                schema: "i",
                table: "project_measurement",
                column: "version",
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_ProjectMeasurementHistory_BusinessId",
                schema: "i",
                table: "project_measurement_history",
                column: "business_id",
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_ProjectMeasurementHistory_ProjectId",
                schema: "i",
                table: "project_measurement_history",
                column: "project_id",
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_ProjectMeasurementHistory_RowIndex",
                schema: "i",
                table: "project_measurement_history",
                column: "row_index",
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_ProjectMeasurementHistory_Version",
                schema: "i",
                table: "project_measurement_history",
                column: "version",
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_ProjectResearchSite_BusinessId",
                schema: "i",
                table: "project_research_site",
                column: "business_id",
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_ProjectResearchSite_ProjectId",
                schema: "i",
                table: "project_research_site",
                column: "project_id",
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_ProjectResearchSite_RowIndex",
                schema: "i",
                table: "project_research_site",
                column: "row_index",
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_ProjectResearchSite_Version",
                schema: "i",
                table: "project_research_site",
                column: "version",
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_ProjectResearchSiteHistory_BusinessId",
                schema: "i",
                table: "project_research_site_history",
                column: "business_id",
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_ProjectResearchSiteHistory_ProjectId",
                schema: "i",
                table: "project_research_site_history",
                column: "project_id",
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_ProjectResearchSiteHistory_RowIndex",
                schema: "i",
                table: "project_research_site_history",
                column: "row_index",
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_ProjectResearchSiteHistory_Version",
                schema: "i",
                table: "project_research_site_history",
                column: "version",
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_ProjectSponsor_BusinessId",
                schema: "i",
                table: "project_sponsor",
                column: "business_id",
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_ProjectSponsor_ProjectId",
                schema: "i",
                table: "project_sponsor",
                column: "project_id",
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_ProjectSponsor_RowIndex",
                schema: "i",
                table: "project_sponsor",
                column: "row_index",
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_ProjectSponsor_Version",
                schema: "i",
                table: "project_sponsor",
                column: "version",
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_ProjectSponsorHistory_BusinessId",
                schema: "i",
                table: "project_sponsor_history",
                column: "business_id",
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_ProjectSponsorHistory_ProjectId",
                schema: "i",
                table: "project_sponsor_history",
                column: "project_id",
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_ProjectSponsorHistory_RowIndex",
                schema: "i",
                table: "project_sponsor_history",
                column: "row_index",
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_ProjectSponsorHistory_Version",
                schema: "i",
                table: "project_sponsor_history",
                column: "version",
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_Project_BusinessId",
                schema: "i",
                table: "projects",
                column: "business_id",
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_Project_FormCode",
                schema: "i",
                table: "projects",
                column: "form_code",
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_Project_Version",
                schema: "i",
                table: "projects",
                column: "version",
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_ProjectHistory_BusinessId",
                schema: "i",
                table: "projects_history",
                column: "business_id",
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_ProjectHistory_FormCode",
                schema: "i",
                table: "projects_history",
                column: "form_code",
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_ProjectHistory_Version",
                schema: "i",
                table: "projects_history",
                column: "version",
                filter: "[is_deleted] = 0");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "async_tasks",
                schema: "i");

            migrationBuilder.DropTable(
                name: "project_attach",
                schema: "i");

            migrationBuilder.DropTable(
                name: "project_attach_history",
                schema: "i");

            migrationBuilder.DropTable(
                name: "project_humansample",
                schema: "i");

            migrationBuilder.DropTable(
                name: "project_humansample_history",
                schema: "i");

            migrationBuilder.DropTable(
                name: "project_intervention",
                schema: "i");

            migrationBuilder.DropTable(
                name: "project_intervention_history",
                schema: "i");

            migrationBuilder.DropTable(
                name: "project_measurement",
                schema: "i");

            migrationBuilder.DropTable(
                name: "project_measurement_history",
                schema: "i");

            migrationBuilder.DropTable(
                name: "project_research_site",
                schema: "i");

            migrationBuilder.DropTable(
                name: "project_research_site_history",
                schema: "i");

            migrationBuilder.DropTable(
                name: "project_sponsor",
                schema: "i");

            migrationBuilder.DropTable(
                name: "project_sponsor_history",
                schema: "i");

            migrationBuilder.DropTable(
                name: "projects",
                schema: "i");

            migrationBuilder.DropTable(
                name: "projects_history",
                schema: "i");
        }
    }
}
