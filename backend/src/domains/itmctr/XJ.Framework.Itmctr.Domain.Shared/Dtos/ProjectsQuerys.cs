using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XJ.Framework.Itmctr.Domain.Shared.Dtos;

public class ProjectsQuerys
{
    public string Key { get; set; } = null!;

    /// <summary>
    /// Equal Empty NotEqual
    /// </summary>
    public ProjectsQueryOperator Operator { get; set; }

    public string? Value { get; set; }
}

public enum ProjectsQueryOperator
{
    Equal = 1,
    Empty = 2,
    NotEqual = 3,
    In = 4,
    NotEmpty = 5
}
