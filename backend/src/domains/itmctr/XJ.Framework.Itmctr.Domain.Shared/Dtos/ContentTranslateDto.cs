using System.Text.Json.Serialization;

namespace XJ.Framework.Itmctr.Domain.Shared.Dtos;

public class ContentTranslateResponseDto
{
    [JsonPropertyName("code")] public int Code { get; set; }
    [JsonPropertyName("msg")] public string Msg { get; set; } = null!;
    [JsonPropertyName("request_id")] public string RequestId { get; set; } = null!;
}

public class ContentTranslateDto : FormRecognitionResponse
{
    [JsonPropertyName("request_id")] public new string Id { get; set; } = null!;
    [JsonPropertyName("data")] public List<ParseItemDto> Data { get; set; } = new List<ParseItemDto>();
}
