using System.Linq.Expressions;
using XJ.Framework.Itmctr.Domain.Entities;
using XJ.Framework.Library.Domain.Common;

namespace XJ.Framework.Itmctr.Domain.Repositories.Interfaces;

/// <summary>
/// 用户仓储接口
/// </summary>
public interface IProjectHistoryRepository : IAuditRepository<long, ProjectHistoryEntity>
{
    Task<PageData<long, ProjectHistoryEntity>> GetProjectHistoryPageAsync(
      Expression<Func<ProjectHistoryEntity, bool>> whereLambda, int rowIndex,
      int pageSize,
      List<OrderbyDirection<ProjectHistoryEntity>> orderBy, bool isNoTracking = true);
}