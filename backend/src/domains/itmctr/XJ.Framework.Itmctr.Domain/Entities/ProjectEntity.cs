using System;
using XJ.Framework.Itmctr.Domain.Shared.Enums;
using XJ.Framework.Library.Domain.Attributes;

namespace XJ.Framework.Itmctr.Domain.Entities;

/// <summary>
/// 项目信息
/// </summary>
[Table("projects", Schema = "i")]
[SoftDeleteIndex("IX_Project_BusinessId", nameof(BusinessId))]
[SoftDeleteIndex("IX_Project_Version", nameof(Version))]
[SoftDeleteIndex("IX_Project_FormCode", nameof(FormCode))]
public class ProjectEntity : BaseSoftDeleteEntity<long>
{
    /// <summary>
    /// 语言
    /// </summary>
    [Column("language")]
    [StringLength(20)]
    public required string language { get; set; }

    /// <summary>
    /// 关联业务id
    /// </summary>
    [Column("business_id")]
    [StringLength(50)]
    public required string BusinessId { get; set; }

    /// <summary>
    /// 版本号
    /// </summary>
    [Column("version")]
    [StringLength(50)]
    public required string Version { get; set; }

    /// <summary>
    /// 表单code
    /// </summary>
    [Column("form_code")]
    [StringLength(50)]
    public required string FormCode { get; set; }

    /// <summary>
    /// 注册号
    /// </summary>
    [Column("registration_number")]
    [StringLength(500)]
    public string? registration_number { get; set; }

    /// <summary>
    /// 注册号状态
    /// </summary>
    [Column("registration_status")]
    [StringLength(500)]
    public string? registration_status { get; set; }

    /// <summary>
    /// 注册题目中文
    /// </summary>
    [Column("publictitle_Zh")]
    [StringLength(500)]
    public string? publictitle_zh { get; set; }

    /// <summary>
    /// 注册题目英文
    /// </summary>
    [Column("publictitle_En")]
    [StringLength(500)]
    public string? publictitle_en { get; set; }

    /// <summary>
    /// 注册题目简写中文
    /// </summary>
    [Column("english_acronym_Zh")]
    [StringLength(500)]
    public string? english_acronym_zh { get; set; }

    /// <summary>
    /// 注册题目简写英文
    /// </summary>
    [Column("english_acronym_En")]
    [StringLength(500)]
    public string? english_acronym_en { get; set; }

    /// <summary>
    /// 研究课题的正式科学名称中文
    /// </summary>
    [Column("scientific_title_Zh")]
    [StringLength(500)]
    public string? scientific_title_zh { get; set; }

    /// <summary>
    /// 研究课题的正式科学名称英文
    /// </summary>
    [Column("scientific_title_En")]
    [StringLength(500)]
    public string? scientific_title_en { get; set; }

    /// <summary>
    /// 研究课题的正式科学名称简写（中文）
    /// </summary>
    [Column("scientific_title_acronym_Zh")]
    [StringLength(500)]
    public string? scientific_title_acronym_zh { get; set; }

    /// <summary>
    /// 研究课题的正式科学名称简写（英文）
    /// </summary>
    [Column("scientific_title_acronym_En")]
    [StringLength(500)]
    public string? scientific_title_acronym_en { get; set; }

    /// <summary>
    /// 研究课题代号(代码)
    /// </summary>
    [Column("study_subject_id")]
    [StringLength(500)]
    public string? study_subject_id { get; set; }

    /// <summary>
    /// 在二级注册机构或其它机构的注册号
    /// </summary>
    [Column("partner_registry_number")]
    [StringLength(500)]
    public string? partner_registry_number { get; set; }

    /// <summary>
    /// 申请注册联系人（中文）
    /// </summary>
    [Column("applicant_Zh")]
    [StringLength(500)]
    public string? applicant_zh { get; set; }

    /// <summary>
    /// 申请注册联系人（英文）
    /// </summary>
    [Column("applicant_En")]
    [StringLength(500)]
    public string? applicant_en { get; set; }

    /// <summary>
    /// 研究负责人（中文）
    /// </summary>
    [Column("study_leader_Zh")]
    [StringLength(500)]
    public string? study_leader_zh { get; set; }

    /// <summary>
    /// 研究负责人（英文）
    /// </summary>
    [Column("study_leader_En")]
    [StringLength(500)]
    public string? study_leader_en { get; set; }

    /// <summary>
    /// 申请注册联系人电话
    /// </summary>
    [Column("applicant_telephone")]
    [StringLength(500)]
    public string? applicant_telephone { get; set; }

    /// <summary>
    /// 研究负责人电话
    /// </summary>
    [Column("study_leader_telephone")]
    [StringLength(500)]
    public string? study_leader_telephone { get; set; }

    /// <summary>
    /// 申请注册联系人传真
    /// </summary>
    [Column("applicant_fax")]
    [StringLength(500)]
    public string? applicant_fax { get; set; }

    /// <summary>
    /// 研究负责人传真
    /// </summary>
    [Column("study_leader_fax")]
    [StringLength(500)]
    public string? study_leader_fax { get; set; }

    /// <summary>
    /// 申请注册联系人电子邮件
    /// </summary>
    [Column("applicant_email")]
    [StringLength(500)]
    public string? applicant_email { get; set; }

    /// <summary>
    /// 研究负责人电子邮件
    /// </summary>
    [Column("study_leader_email")]
    [StringLength(500)]
    public string? study_leader_email { get; set; }

    /// <summary>
    /// 申请单位网址(自愿提供)
    /// </summary>
    [Column("applicant_website")]
    [StringLength(500)]
    public string? applicant_website { get; set; }

    /// <summary>
    /// 研究负责人网址(自愿提供)
    /// </summary>
    [Column("study_leader_website")]
    [StringLength(500)]
    public string? study_leader_website { get; set; }

    /// <summary>
    /// 申请注册联系人通讯地址（中文）
    /// </summary>
    [Column("applicant_address_Zh")]
    [StringLength(500)]
    public string? applicant_address_zh { get; set; }

    /// <summary>
    /// 申请注册联系人通讯地址（英文）
    /// </summary>
    [Column("applicant_address_En")]
    [StringLength(500)]
    public string? applicant_address_en { get; set; }

    /// <summary>
    /// 研究负责人通讯地址（中文）
    /// </summary>
    [Column("study_leader_address_Zh")]
    [StringLength(500)]
    public string? study_leader_address_zh { get; set; }

    /// <summary>
    /// 研究负责人通讯地址（英文）
    /// </summary>
    [Column("study_leader_address_En")]
    [StringLength(500)]
    public string? study_leader_address_en { get; set; }

    /// <summary>
    /// 申请注册联系人邮政编码
    /// </summary>
    [Column("applicant_postcode")]
    [StringLength(500)]
    public string? applicant_postcode { get; set; }

    /// <summary>
    /// 研究负责人邮政编码
    /// </summary>
    [Column("study_leader_postcode")]
    [StringLength(500)]
    public string? study_leader_postcode { get; set; }

    /// <summary>
    /// 申请人所在单位（中文）
    /// </summary>
    [Column("applicant_affiliation_Zh")]
    [StringLength(500)]
    public string? applicant_affiliation_zh { get; set; }

    /// <summary>
    /// 申请人所在单位（英文）
    /// </summary>
    [Column("applicant_affiliation_En")]
    [StringLength(500)]
    public string? applicant_affiliation_en { get; set; }

    /// <summary>
    /// 研究负责人所在单位（中文）
    /// </summary>
    [Column("study_leader_affiliation_Zh")]
    [StringLength(500)]
    public string? study_leader_affiliation_zh { get; set; }

    /// <summary>
    /// 研究负责人所在单位（英文）
    /// </summary>
    [Column("study_leader_affiliation_En")]
    [StringLength(500)]
    public string? study_leader_affiliation_en { get; set; }

    /// <summary>
    /// 是否获伦理委员会批准
    /// </summary>
    [Column("ethic_committee_approved")]
    [StringLength(500)]
    public string? ethic_committee_approved { get; set; }

    /// <summary>
    /// 伦理委员会批件文号
    /// </summary>
    [Column("ethic_committee_approved_no")]
    [StringLength(500)]
    public string? ethic_committee_approved_no { get; set; }

    /// <summary>
    /// 批准本研究的伦理委员会名称（中文）
    /// </summary>
    [Column("ethic_committee_name_Zh")]
    [StringLength(500)]
    public string? ethic_committee_name_zh { get; set; }

    /// <summary>
    /// 批准本研究的伦理委员会名称（英文）
    /// </summary>
    [Column("ethic_committee_name_En")]
    [StringLength(500)]
    public string? ethic_committee_name_en { get; set; }

    /// <summary>
    /// 伦理委员会批准日期
    /// </summary>
    [Column("ethic_committee_approved_date")]
    public DateTime? ethic_committee_approved_date { get; set; }

    /// <summary>
    /// 伦理委员会联系人（中文）
    /// </summary>
    [Column("ethic_committee_contact_Zh")]
    [StringLength(500)]
    public string? ethic_committee_contact_zh { get; set; }

    /// <summary>
    /// 伦理委员会联系人（英文）
    /// </summary>
    [Column("ethic_committee_contact_En")]
    [StringLength(500)]
    public string? ethic_committee_contact_en { get; set; }

    /// <summary>
    /// 伦理委员会联系地址（中文）
    /// </summary>
    [Column("ethic_committee_address_Zh")]
    [StringLength(500)]
    public string? ethic_committee_address_zh { get; set; }

    /// <summary>
    /// 伦理委员会联系地址（英文）
    /// </summary>
    [Column("ethic_committee_address_En")]
    [StringLength(500)]
    public string? ethic_committee_address_en { get; set; }

    /// <summary>
    /// 伦理委员会联系人电话
    /// </summary>
    [Column("ethic_committee_phone")]
    [StringLength(500)]
    public string? ethic_committee_phone { get; set; }

    /// <summary>
    /// 伦理委员会联系人邮箱
    /// </summary>
    [Column("ethic_committee_email")]
    [StringLength(500)]
    public string? ethic_committee_email { get; set; }

    /// <summary>
    /// 国家药监局批准文号
    /// </summary>
    [Column("mpa_approved_no")]
    [StringLength(500)]
    public string? mpa_approved_no { get; set; }

    /// <summary>
    /// 国家药监局批准日期
    /// </summary>
    [Column("mpa_approved_date")]
    public DateTime? mpa_approved_date { get; set; }

    /// <summary>
    /// 研究实施负责（组长）单位（中文）
    /// </summary>
    [Column("primary_sponsor_Zh")]
    [StringLength(500)]
    public string? primary_sponsor_zh { get; set; }

    /// <summary>
    /// 研究实施负责（组长）单位（英文）
    /// </summary>
    [Column("primary_sponsor_En")]
    [StringLength(500)]
    public string? primary_sponsor_en { get; set; }

    /// <summary>
    /// 研究实施负责（组长）单位地址（中文）
    /// </summary>
    [Column("primary_sponsor_address_Zh")]
    [StringLength(500)]
    public string? primary_sponsor_address_zh { get; set; }

    /// <summary>
    /// 研究实施负责（组长）单位地址（英文）
    /// </summary>
    [Column("primary_sponsor_address_En")]
    [StringLength(500)]
    public string? primary_sponsor_address_en { get; set; }

    /// <summary>
    /// 经费或物资来源（中文）
    /// </summary>
    [Column("funding_source_Zh")]
    [StringLength(500)]
    public string? funding_source_zh { get; set; }

    /// <summary>
    /// 经费或物资来源（英文）
    /// </summary>
    [Column("funding_source_En")]
    [StringLength(500)]
    public string? funding_source_en { get; set; }

    /// <summary>
    /// 研究疾病（中文）
    /// </summary>
    [Column("target_disease_Zh")]
    [StringLength(500)]
    public string? target_disease_zh { get; set; }

    /// <summary>
    /// 研究疾病（英文）
    /// </summary>
    [Column("target_disease_En")]
    [StringLength(500)]
    public string? target_disease_en { get; set; }

    /// <summary>
    /// 研究疾病代码
    /// </summary>
    [Column("target_disease_code")]
    [StringLength(500)]
    public string? target_disease_code { get; set; }

    /// <summary>
    /// 研究类型
    /// </summary>
    [Column("study_type")]
    [StringLength(500)]
    public string? study_type { get; set; }

    /// <summary>
    /// 研究设计
    /// </summary>
    [Column("study_design")]
    [StringLength(500)]
    public string? study_design { get; set; }

    /// <summary>
    /// 研究所处阶段
    /// </summary>
    [Column("study_phase")]
    [StringLength(500)]
    public string? study_phase { get; set; }

    /// <summary>
    /// 研究目的（中文）
    /// </summary>
    [Column("study_objectives_Zh")]
    [StringLength(1000)]
    public string? study_objectives_zh { get; set; }

    /// <summary>
    /// 研究目的（英文）
    /// </summary>
    [Column("study_objectives_En")]
    [StringLength(1500)]
    public string? study_objectives_en { get; set; }

    /// <summary>
    /// 药物成份或治疗方案详述（中文）
    /// </summary>
    [Column("treatment_description_Zh")]
    [StringLength(1000)]
    public string? treatment_description_zh { get; set; }

    /// <summary>
    /// 药物成份或治疗方案详述（英文）
    /// </summary>
    [Column("treatment_description_En")]
    [StringLength(1500)]
    public string? treatment_description_en { get; set; }

    /// <summary>
    /// 纳入标准（中文）
    /// </summary>
    [Column("inclusion_criteria_Zh")]
    [StringLength(1000)]
    public string? inclusion_criteria_zh { get; set; }

    /// <summary>
    /// 纳入标准（英文）
    /// </summary>
    [Column("inclusion_criteria_En")]
    [StringLength(2000)]
    public string? inclusion_criteria_en { get; set; }

    /// <summary>
    /// 排除标准（中文）
    /// </summary>
    [Column("exclusion_criteria_Zh")]
    [StringLength(1000)]
    public string? exclusion_criteria_zh { get; set; }

    /// <summary>
    /// 排除标准（英文）
    /// </summary>
    [Column("exclusion_criteria_En")]
    [StringLength(1500)]
    public string? exclusion_criteria_en { get; set; }

    /// <summary>
    /// 研究实施时间开始
    /// </summary>
    [Column("study_time_start")]
    public DateTime? study_time_start { get; set; }

    /// <summary>
    /// 研究实施时间结束
    /// </summary>
    [Column("study_time_end")]
    public DateTime? study_time_end { get; set; }

    /// <summary>
    /// 征募观察对象时间开始
    /// </summary>
    [Column("recruiting_time_start")]
    public DateTime? recruiting_time_start { get; set; }

    /// <summary>
    /// 征募观察对象时间结束
    /// </summary>
    [Column("recruiting_time_end")]
    public DateTime? recruiting_time_end { get; set; }

    /// <summary>
    /// 金标准或参考标准（中文）
    /// </summary>
    [Column("gold_standard_Zh")]
    [StringLength(1000)]
    public string? gold_standard_zh { get; set; }

    /// <summary>
    /// 金标准或参考标准（英文）
    /// </summary>
    [Column("gold_standard_En")]
    [StringLength(1500)]
    public string? gold_standard_en { get; set; }

    /// <summary>
    /// 指标试验（中文）
    /// </summary>
    [Column("index_test_Zh")]
    [StringLength(500)]
    public string? index_test_zh { get; set; }

    /// <summary>
    /// 指标试验（英文）
    /// </summary>
    [Column("index_test_En")]
    [StringLength(500)]
    public string? index_test_en { get; set; }

    /// <summary>
    /// 目标人群（中文）
    /// </summary>
    [Column("target_condition_Zh")]
    [StringLength(500)]
    public string? target_condition_zh { get; set; }

    /// <summary>
    /// 目标人群（英文）
    /// </summary>
    [Column("target_condition_En")]
    [StringLength(500)]
    public string? target_condition_en { get; set; }

    /// <summary>
    /// 目标人群例数
    /// </summary>
    [Column("target_sample_size")]
    public string? target_sample_size { get; set; }

    /// <summary>
    /// 容易混淆的疾病人群（中文）
    /// </summary>
    [Column("confounding_condition_Zh")]
    [StringLength(500)]
    public string? confounding_condition_zh { get; set; }

    /// <summary>
    /// 容易混淆的疾病人群（英文）
    /// </summary>
    [Column("confounding_condition_En")]
    [StringLength(500)]
    public string? confounding_condition_en { get; set; }

    /// <summary>
    /// 容易混淆的疾病人群例数
    /// </summary>
    [Column("confounding_sample_size")]
    [StringLength(500)]
    public string? confounding_sample_size { get; set; }

    /// <summary>
    /// 干预措施样本总量
    /// </summary>
    [Column("intervention_total_sample_size")]
    [StringLength(500)]
    public string? intervention_total_sample_size { get; set; }

    /// <summary>
    /// 征募研究对象状况
    /// </summary>
    [Column("recruiting_status")]
    [StringLength(500)]
    public string? recruiting_status { get; set; }

    /// <summary>
    /// 年龄范围开始
    /// </summary>
    [Column("age_range_min")]
    public int? age_range_min { get; set; }

    /// <summary>
    /// 年龄范围结束
    /// </summary>
    [Column("age_range_max")]
    public int? age_range_max { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    [Column("gender")]
    [StringLength(500)]
    public string? gender { get; set; }

    /// <summary>
    /// 随机方法（请说明由何人用什么方法产生随机序列）（中文）
    /// </summary>
    [Column("randomization_procedure_Zh")]
    [StringLength(1000)]
    public string? randomization_procedure_zh { get; set; }

    /// <summary>
    /// 随机方法（请说明由何人用什么方法产生随机序列）（英文）
    /// </summary>
    [Column("randomization_procedure_En")]
    [StringLength(1500)]
    public string? randomization_procedure_en { get; set; }

    /// <summary>
    /// 研究对象是否签署知情同意书
    /// </summary>
    [Column("sign_informed_consent")]
    [StringLength(500)]
    public string? sign_informed_consent { get; set; }

    /// <summary>
    /// 随访时间
    /// </summary>
    [Column("follow_up_length")]
    [StringLength(500)]
    public string? follow_up_length { get; set; }

    /// <summary>
    /// 随访时间单位
    /// </summary>
    [Column("follow_up_unit")]
    [StringLength(500)]
    public string? follow_up_unit { get; set; }

    /// <summary>
    /// 隐蔽分组方法和过程（中文）
    /// </summary>
    [Column("allocation_concealment_Zh")]
    [StringLength(1000)]
    public string? allocation_concealment_zh { get; set; }

    /// <summary>
    /// 隐蔽分组方法和过程（英文）
    /// </summary>
    [Column("allocation_concealment_En")]
    [StringLength(1500)]
    public string? allocation_concealment_en { get; set; }

    /// <summary>
    /// 盲法（中文）
    /// </summary>
    [Column("blinding_Zh")]
    [StringLength(1000)]
    public string? blinding_zh { get; set; }

    /// <summary>
    /// 盲法（英文）
    /// </summary>
    [Column("blinding_En")]
    [StringLength(1500)]
    public string? blinding_en { get; set; }

    /// <summary>
    /// 揭盲或破盲原则和方法（中文）
    /// </summary>
    [Column("unblinding_rules_Zh")]
    [StringLength(1000)]
    public string? unblinding_rules_zh { get; set; }

    /// <summary>
    /// 揭盲或破盲原则和方法（英文）
    /// </summary>
    [Column("unblinding_rules_En")]
    [StringLength(1500)]
    public string? unblinding_rules_en { get; set; }

    /// <summary>
    /// 统计方法名称（中文）
    /// </summary>
    [Column("statistical_methods_Zh")]
    [StringLength(500)]
    public string? statistical_methods_zh { get; set; }

    /// <summary>
    /// 统计方法名称（英文）
    /// </summary>
    [Column("statistical_methods_En")]
    [StringLength(500)]
    public string? statistical_methods_en { get; set; }

    /// <summary>
    /// 试验完成后的统计结果（中文）
    /// </summary>
    [Column("calculated_results_Zh")]
    [StringLength(500)]
    public string? calculated_results_zh { get; set; }

    /// <summary>
    /// 试验完成后的统计结果（英文）
    /// </summary>
    [Column("calculated_results_En")]
    [StringLength(500)]
    public string? calculated_results_en { get; set; }

    /// <summary>
    /// 是否公开试验完成后的统计结果
    /// </summary>
    [Column("calculated_results_public")]
    [StringLength(20)]
    public string? calculated_results_public { get; set; }

    /// <summary>
    /// 全球唯一识别码
    /// </summary>
    [Column("utn")]
    [StringLength(500)]
    public string? utn { get; set; }

    /// <summary>
    /// 是否共享原始数据
    /// </summary>
    [Column("ipd_sharing")]
    [StringLength(100)]
    public string? ipd_sharing { get; set; }

    /// <summary>
    /// 共享原始数据的方式（中文）
    /// </summary>
    [Column("ipd_sharing_way_Zh")]
    [StringLength(500)]
    public string? ipd_sharing_way_zh { get; set; }

    /// <summary>
    /// 共享原始数据的方式（英文）
    /// </summary>
    [Column("ipd_sharing_way_En")]
    [StringLength(500)]
    public string? ipd_sharing_way_en { get; set; }

    /// <summary>
    /// 数据采集和管理（中文）
    /// </summary>
    [Column("data_collection_Zh")]
    [StringLength(500)]
    public string? data_collection_zh { get; set; }

    /// <summary>
    /// 数据采集和管理（英文）
    /// </summary>
    [Column("data_collection_En")]
    [StringLength(500)]
    public string? data_collection_en { get; set; }

    /// <summary>
    /// 数据与安全监察委员会
    /// </summary>
    [Column("safety_committee")]
    [StringLength(100)]
    public string? safety_committee { get; set; }

    /// <summary>
    /// 研究计划书或研究结果报告发表信息（中文）
    /// </summary>
    [Column("publication_info_Zh")]
    [StringLength(500)]
    public string? publication_info_zh { get; set; }

    /// <summary>
    /// 研究计划书或研究结果报告发表信息（英文）
    /// </summary>
    [Column("publication_info_En")]
    [StringLength(500)]
    public string? publication_info_en { get; set; }

    /// <summary>
    /// 发号时间
    /// </summary>
    [Column("send_number_time")]
    public DateTimeOffset? send_number_time { get; set; }

    /// <summary>
    /// 首次提交时间
    /// </summary>
    [Column("first_submit_time")]
    public DateTimeOffset? first_submit_time { get; set; }
}
