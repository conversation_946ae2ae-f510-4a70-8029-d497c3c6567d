using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using XJ.Framework.Itmctr.Application.Contract.Interfaces;
using XJ.Framework.Itmctr.Application.Options;
using XJ.Framework.Itmctr.Domain.Shared.Dtos;
using XJ.Framework.Library.Infrastructure.ApiClient.Options;
using XJ.Framework.Library.Logging.Abstraction.DI;

namespace XJ.Framework.Itmctr.Application.Services;

public class TokenProvider : ITokenProvider
{
    private readonly HttpClient _httpClient;

    private readonly string _baseUrl;

    private readonly object _lock = new();

    private string? _accessToken;

    private DateTime _expireTime = DateTime.MinValue;

    private ILogger<TokenProvider> _logger;

    // 你的认证信息
    private readonly string _clientId = "your_client_id";
    private readonly string _clientSecret = "your_client_secret";
    private readonly IOptions<UnicomOption> _options;
    private readonly bool _enableAuth = false;

    public TokenProvider(IHttpClientFactory httpClientFactory, IOptions<EndpointOption> endpointOption,
        IOptions<UnicomOption> options, ILogger<TokenProvider> logger)
    {
        _options = options;
        _logger = logger;
        _baseUrl = endpointOption.Value["UnicomAuthorization"]!.Url.TrimEnd('/');
        _httpClient = httpClientFactory.CreateClient();

        this._clientId = _options.Value.ClientId;
        this._clientSecret = _options.Value.ClientSecret;
        this._enableAuth = _options.Value.EnableAuthorization;
    }

    public async Task<string> GetTokenAsync()
    {
        if (!this._enableAuth)
        {
            _logger.LogInformation("Authorization is disabled, returning empty token.");
            return string.Empty;
        }

        if (_accessToken != null && DateTime.UtcNow < _expireTime)
        {
            _logger.LogInformation("Returning cached access token.");
            return _accessToken;
        }

        lock (_lock)
        {
            if (_accessToken != null && DateTime.UtcNow < _expireTime)
            {
                _logger.LogInformation("Returning cached access token from lock.");
                return _accessToken;
            }
        }

        var byteArray = Encoding.ASCII.GetBytes($"{_clientId}:{_clientSecret}");
        _httpClient.DefaultRequestHeaders.Authorization =
            new AuthenticationHeaderValue("Basic", Convert.ToBase64String(byteArray));

        var content = new FormUrlEncodedContent([
            new KeyValuePair<string, string>("grant_type", "client_credentials")
        ]);

        try
        {
            var response = await _httpClient.PostAsync($"{_baseUrl}/oauth2/token", content);
            response.EnsureSuccessStatusCode();

            var json = await response.Content.ReadAsStringAsync();
            var tokenResponse = JsonSerializer.Deserialize<TokenResponse>(json)!;

            lock (_lock)
            {
                _logger.LogDebug("Acquired new access token.");
                _accessToken = tokenResponse.AccessToken;
                _expireTime = DateTime.UtcNow.AddSeconds(tokenResponse.ExpiresIn - 60); // 提前1分钟刷新
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to acquire access token.");
            throw new Exception("获取访问令牌失败/ Failed to acquire access token", ex);
            _accessToken = null;
            _expireTime = DateTime.MinValue;
        }

        return _accessToken;
    }
}