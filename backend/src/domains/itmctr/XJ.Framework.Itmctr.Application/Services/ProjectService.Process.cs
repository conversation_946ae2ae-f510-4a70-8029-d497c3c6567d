using System.Text.Json;
using XJ.Framework.DynamicForm.Application.Contract.OperationDtos;
using XJ.Framework.DynamicForm.Application.Contract.Services;
using XJ.Framework.DynamicForm.Domain.Shared.Dtos;
using XJ.Framework.DynamicForm.Domain.Shared.Enums;
using XJ.Framework.Itmctr.Application.Contract.OperationDtos;
using XJ.Framework.Itmctr.Domain.Shared.Dtos;
using XJ.Framework.Library.Common.Abstraction.Extensions;

namespace XJ.Framework.Itmctr.Application.Services;

public partial class ProjectService
{
    public async Task<bool> RejectLevel4Async(string businessId, FormRejectDto formRejectDto)
    {
        var formInstance = await _dynamicFormMgtApiClient.GetFormInstanceDtoAsync(businessId);
        if (formInstance.Status != FormInstanceStatus.Submitted)
        {
            throw new ValidationException("表单状态不正确/ Form status is incorrect");
        }

        var formData = await _dynamicFormMgtApiClient.GetFormDataAsync(businessId);

        if (formData["ProcessStatus"] != "PendingFourthApproval")
        {
            throw new ValidationException("表单状态不正确/ Form status is incorrect");
        }

        if (formData["FourthApprovalUserId"] != _currentUserContext.GetCurrentUser()?.Key.ToString())
        {
            throw new ValidationException("您不是初级审核员/ You are not a junior auditor");
        }

        await _dynamicFormMgtApiClient.RejectInstanceAsync(businessId, formRejectDto.AnnotationValues);

        formData["FourthApprovalTime"] =
            DateTimeOffset.UtcNow.ToUnixTimeMilliseconds().ToString();

        formData["ProcessStatus"] = "RejectToApply";

        formData["FourthRejectReason"] = formRejectDto.Description;

        formData["RejectCount"] = formData.TryGetValue("RejectCount", out var rejectCount)
            ? (int.Parse(rejectCount ?? "0") + 1).ToString()
            : "1";
        formData["LastApprovalUserId"] = _currentUserContext.GetCurrentUser()?.Key.ToString();

        AddApprovalHistory("FourthApproval", "Reject", formRejectDto.Description, formData);

        await _dynamicFormMgtApiClient.SetFormDataAsync(businessId, formData);

        return true;
    }

    public async Task<bool> ApprovalLevel4Async(string businessId)
    {
        var formInstance = await _dynamicFormMgtApiClient.GetFormInstanceDtoAsync(businessId);
        if (formInstance.Status != FormInstanceStatus.Submitted)
        {
            throw new ValidationException("表单状态不正确/ Form status is incorrect");
        }

        var formData = await _dynamicFormMgtApiClient.GetFormDataAsync(businessId);

        if (formData["ProcessStatus"] != "PendingFourthApproval")
        {
            throw new ValidationException("表单状态不正确/ Form status is incorrect");
        }

        if (formData["FourthApprovalUserId"] != _currentUserContext.GetCurrentUser()?.Key.ToString())
        {
            throw new ValidationException("您不是初级审核员/ You are not a junior auditor");
        }

        formData["FourthApprovalTime"] =
            DateTimeOffset.UtcNow.ToUnixTimeMilliseconds().ToString();

        if (formData.TryGetValue("RegistrationNumber", out var registrationNumber)
            && !string.IsNullOrEmpty(registrationNumber)
           )
        {
            formData["ProcessStatus"] = "PendingSecondApproval";
        }
        else
        {
            if (formData["SkipThirdAssignment"] == "True")
            {
                formData["ProcessStatus"] = "PendingSecondApproval";
            }
            else
            {
                formData["ProcessStatus"] = "PendingThirdApproval";
            }
        }


        formData["LastApprovalUserId"] = _currentUserContext.GetCurrentUser()?.Key.ToString();

        AddApprovalHistory("FourthApproval", "Approval", string.Empty, formData);

        await _dynamicFormMgtApiClient.SetFormDataAsync(businessId, formData);

        await _dynamicFormMgtApiClient.ClearFormAnnotationAsync(businessId);
        return true;
    }

    public async Task<bool> ApprovalLevel3Async(string businessId)
    {
        var formInstance = await _dynamicFormMgtApiClient.GetFormInstanceDtoAsync(businessId);
        if (formInstance.Status != FormInstanceStatus.Submitted)
        {
            throw new ValidationException("表单状态不正确/ Form status is incorrect");
        }

        var formData = await _dynamicFormMgtApiClient.GetFormDataAsync(businessId);

        if (formData["ProcessStatus"] != "PendingThirdApproval")
        {
            throw new ValidationException("表单状态不正确/ Form status is incorrect");
        }

        if (formData["ThirdApprovalUserId"] != _currentUserContext.GetCurrentUser()?.Key.ToString())
        {
            throw new ValidationException("您不是中级审核员/ You are not a assistant-senior auditor");
        }

        formData["ThirdApprovalTime"] =
            DateTimeOffset.UtcNow.ToUnixTimeMilliseconds().ToString();

        formData["ProcessStatus"] = "PendingSecondApproval";
        formData["LastApprovalUserId"] = _currentUserContext.GetCurrentUser()?.Key.ToString();

        AddApprovalHistory("ThirdApproval", "Approval", string.Empty, formData);

        await _dynamicFormMgtApiClient.SetFormDataAsync(businessId, formData);
        return true;
    }

    public async Task<bool> RejectLevel3Async(string businessId, FormRejectDto formRejectDto)
    {
        var formInstance = await _dynamicFormMgtApiClient.GetFormInstanceDtoAsync(businessId);
        if (formInstance.Status != FormInstanceStatus.Submitted)
        {
            throw new ValidationException("表单状态不正确/ Form status is incorrect");
        }

        var formData = await _dynamicFormMgtApiClient.GetFormDataAsync(businessId);

        if (formData["ProcessStatus"] != "PendingThirdApproval")
        {
            throw new ValidationException("表单状态不正确/ Form status is incorrect");
        }

        if (formData["ThirdApprovalUserId"] != _currentUserContext.GetCurrentUser()?.Key.ToString())
        {
            throw new ValidationException("您不是中级审核员/ You are not a assistant-senior auditor");
        }

        formData["ThirdApprovalTime"] =
            DateTimeOffset.UtcNow.ToUnixTimeMilliseconds().ToString();

        formData["ProcessStatus"] = "PendingFourthApproval";
        formData["LastApprovalUserId"] = _currentUserContext.GetCurrentUser()?.Key.ToString();

        AddApprovalHistory("ThirdApproval", "Reject", formRejectDto.Description, formData);

        await _dynamicFormMgtApiClient.SetFormDataAsync(businessId, formData);
        return true;
    }

    public async Task<bool> ApprovalLevel2Async(string businessId)
    {
        var formInstance = await _dynamicFormMgtApiClient.GetFormInstanceDtoAsync(businessId);
        if (formInstance.Status != FormInstanceStatus.Submitted)
        {
            throw new ValidationException("表单状态不正确/ Form status is incorrect");
        }

        var formData = await _dynamicFormMgtApiClient.GetFormDataAsync(businessId);

        if (formData["ProcessStatus"] != "PendingSecondApproval")
        {
            throw new ValidationException("表单状态不正确/ Form status is incorrect");
        }

        if (formData["SecondApprovalUserId"] != _currentUserContext.GetCurrentUser()?.Key.ToString())
        {
            throw new ValidationException("您不是高级审核员/ You are not senior auditor");
        }

        formData["SecondApprovalTime"] =
            DateTimeOffset.UtcNow.ToUnixTimeMilliseconds().ToString();

        formData["ProcessStatus"] = "PendingFirstApproval";
        formData["LastApprovalUserId"] = _currentUserContext.GetCurrentUser()?.Key.ToString();

        AddApprovalHistory("SecondApproval", "Approval", string.Empty, formData);

        await _dynamicFormMgtApiClient.SetFormDataAsync(businessId, formData);
        return true;
    }

    public async Task<bool> RejectLevel2Async(string businessId, FormRejectDto formRejectDto)
    {
        var formInstance = await _dynamicFormMgtApiClient.GetFormInstanceDtoAsync(businessId);
        if (formInstance.Status != FormInstanceStatus.Submitted)
        {
            throw new ValidationException("表单状态不正确/ Form status is incorrect");
        }

        var formData = await _dynamicFormMgtApiClient.GetFormDataAsync(businessId);

        if (formData["ProcessStatus"] != "PendingSecondApproval")
        {
            throw new ValidationException("表单状态不正确/ Form status is incorrect");
        }

        if (formData["SecondApprovalUserId"] != _currentUserContext.GetCurrentUser()?.Key.ToString())
        {
            throw new ValidationException("您不是高级审核员/ You are not senior auditor");
        }

        formData["SecondApprovalTime"] =
            DateTimeOffset.UtcNow.ToUnixTimeMilliseconds().ToString();

        if (formData.TryGetValue("RegistrationNumber", out var registrationNumber)
            && !string.IsNullOrEmpty(registrationNumber)
           )
        {
            formData["ProcessStatus"] = "PendingFourthApproval";
        }
        else
        {
            if (formData["SkipThirdAssignment"] == "True")
            {
                formData["ProcessStatus"] = "PendingFourthApproval";
            }
            else
            {
                formData["ProcessStatus"] = "PendingThirdApproval";
            }
        }

        formData["LastApprovalUserId"] = _currentUserContext.GetCurrentUser()?.Key.ToString();
        AddApprovalHistory("SecondApproval", "Reject", formRejectDto.Description, formData);

        await _dynamicFormMgtApiClient.SetFormDataAsync(businessId, formData);
        return true;
    }

    public async Task<bool> RejectLevel1Async(string businessId, SendNumberDto input)
    {
        var formInstance = await _dynamicFormMgtApiClient.GetFormInstanceDtoAsync(businessId);
        if (formInstance.Status != FormInstanceStatus.Submitted)
        {
            throw new ValidationException("表单状态不正确/ Form status is incorrect");
        }

        var formData = await _dynamicFormMgtApiClient.GetFormDataAsync(businessId);

        if (formData["ProcessStatus"] != "PendingFirstApproval")
        {
            throw new ValidationException("表单状态不正确/ Form status is incorrect");
        }

        if (formData["FirstApprovalUserId"] != _currentUserContext.GetCurrentUser()?.Key.ToString())
        {
            throw new ValidationException("您不是总审核员/ You are not general auditor");
        }

        formData["FirstApprovalTime"] =
            DateTimeOffset.UtcNow.ToUnixTimeMilliseconds().ToString();

        formData["ProcessStatus"] = "PendingSecondApproval";

        formData["LastApprovalUserId"] = _currentUserContext.GetCurrentUser()?.Key.ToString();
        AddApprovalHistory("FirstApproval", "Reject", input.Description, formData);

        await _dynamicFormMgtApiClient.SetFormDataAsync(businessId, formData);
        return true;
    }

    public async Task<bool> ApprovalLevel1Async(string businessId, SendNumberDto input)
    {
        var formInstance = await _dynamicFormMgtApiClient.GetFormInstanceDtoAsync(businessId);
        if (formInstance.Status != FormInstanceStatus.Submitted)
        {
            throw new ValidationException("表单状态不正确/ Form status is incorrect");
        }


        var formDefinition = await _dynamicFormApiClient.GetNewestFormInstanceAsync(businessId);

        if (formDefinition.FormData["ProcessStatus"] != "PendingFirstApproval")
        {
            throw new ValidationException("表单状态不正确/ Form status is incorrect");
        }

        if (formDefinition.FormData["FirstApprovalUserId"] != _currentUserContext.GetCurrentUser()?.Key.ToString())
        {
            throw new ValidationException("您不是总审核员/ You are not general auditor");
        }

        // 如果没有发号时间 则赋值
        if (!formDefinition.FormData.ContainsKey("SendNumberTime"))
        {
            formDefinition.FormData["SendNumberTime"] = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds().ToString();
        }

        formDefinition.FormData.TryGetValue("RegistrationNumber", out var originalRegistrationNumber);

        if (string.IsNullOrEmpty(originalRegistrationNumber))
        {
            if (string.IsNullOrEmpty(input.Prefix) || input.Year == 0 || string.IsNullOrEmpty(input.Number))
            {
                throw new ValidationException(
                    "注册号前缀、年份和编号不能为空/ Registration number prefix, year, and number cannot be empty.");
            }

            var registrationNumber = $"{input.Prefix}{input.Year}{input.Number}";

            var exist = await _dynamicFormApiClient.ExistFormDataValueAsync(FormCode, "RegistrationNumber",
                registrationNumber);
            if (exist)
            {
                throw new ValidationException("注册号已存在，请重新输入/ Registration number already exists, please re-enter.");
            }


            formDefinition.SetFieldValue("registration_number",
                new MultiTypeValue() { StringValue = registrationNumber });

            formDefinition.FormData["RegistrationNumber"] = registrationNumber;
        }


        formDefinition.FormData["FirstApprovalTime"] =
            DateTimeOffset.UtcNow.ToUnixTimeMilliseconds().ToString();

        formDefinition.FormData["ProcessStatus"] = "Approved";

        //清空再修改相关值
        formDefinition.FormData["EditProcessStatus"] = string.Empty;
        formDefinition.FormData.Remove("EditDescription");
        formDefinition.FormData.Remove("StudyProtocol");
        formDefinition.FormData.Remove("Ethic");
        formDefinition.FormData.Remove("EditApplyTime");

        formDefinition.FormData["LastApprovalUserId"] = _currentUserContext.GetCurrentUser()?.Key.ToString();

        AddApprovalHistory("FirstApproval", "Approval", input.Description, formDefinition.FormData);

        await _dynamicFormMgtApiClient.SaveInstanceAsync(businessId, formDefinition);

        await _dynamicFormMgtApiClient.ConfirmInstanceAsync(businessId);

        await _asyncTaskService.CreateTaskAsync(businessId, "ProcessProjectToResult", new Dictionary<string, object?>()
        {
            { "BusinessId", businessId },
            { "Version", formDefinition.Version },
            { "FormCode", formInstance.FormCode },
            { "Language", formDefinition.Language! },
            { "DataValue", await FormDefinitionHelper.GetFieldValuesAsync(formDefinition) }
        });

        return true;
    }

    public async Task<bool> RejectLevel1EditAsync(string businessId)
    {
        var formInstance = await _dynamicFormMgtApiClient.GetFormInstanceDtoAsync(businessId);
        if (formInstance.Status != FormInstanceStatus.Submitted)
        {
            throw new ValidationException("表单状态不正确/ Form status is incorrect");
        }

        var formData = await _dynamicFormMgtApiClient.GetFormDataAsync(businessId);

        if (formData["ProcessStatus"] != "PendFirstConfirmation")
        {
            throw new ValidationException("表单状态不正确/ Form status is incorrect");
        }

        formData["FirstEditApprovalTime"] =
            DateTimeOffset.UtcNow.ToUnixTimeMilliseconds().ToString();

        formData["ProcessStatus"] = "RejectToApplyEdit";

        AddApprovalHistory("FirstApproval", "Reject", string.Empty, formData);

        await _dynamicFormMgtApiClient.SetFormDataAsync(businessId, formData);
        return true;
    }

    public async Task<bool> ApprovalLevel1EditAsync(string businessId)
    {
        var formInstance = await _dynamicFormMgtApiClient.GetFormInstanceDtoAsync(businessId);
        if (formInstance.Status != FormInstanceStatus.Submitted)
        {
            throw new ValidationException("表单状态不正确/ Form status is incorrect");
        }

        var formData = await _dynamicFormMgtApiClient.GetFormDataAsync(businessId);

        if (formData["ProcessStatus"] != "PendFirstConfirmation")
        {
            throw new ValidationException("表单状态不正确/ Form status is incorrect");
        }

        formData["FirstEditApprovalTime"] =
            DateTimeOffset.UtcNow.ToUnixTimeMilliseconds().ToString();

        formData["ProcessStatus"] = "PendingFourthApproval";

        AddApprovalHistory("FirstApproval", "Approval", string.Empty, formData);

        await _dynamicFormMgtApiClient.SetFormDataAsync(businessId, formData);
        return true;
    }

    public async Task<bool> EditApplyAsync(string businessId, EditApplyProjectOperationDto input)
    {
        var formInstance = await _dynamicFormApiClient.GetFormInstanceDtoAsync(businessId);
        if (formInstance.Status != FormInstanceStatus.Confirmed && formInstance.Status != FormInstanceStatus.Draft)
        {
            throw new ValidationException("表单状态不正确/Form status is incorrect");
        }

        var formData = await _dynamicFormApiClient.GetFormDataAsync(businessId);

        if (formData["ProcessStatus"] != "Approved")
        {
            throw new ValidationException("表单状态不正确/ Form status is incorrect");
        }

        if (formData.TryGetValue("EditProcessStatus", out var editProcessStatus) &&
            !(editProcessStatus?.Equals("Rejected") ?? false) && !string.IsNullOrEmpty(editProcessStatus))
        {
            throw new ValidationException("您已经发起过再修改申请/ You have already initiated a modification request");
        }

        var reApply = (editProcessStatus?.Equals("Rejected") ?? false) &&
                      formInstance.Status == FormInstanceStatus.Draft;


        formData["EditDescription"] = input.Description;
        formData["StudyProtocol"] = input.StudyProtocol.ToJson();
        formData["Ethic"] = input.Ethic.ToJson();
        formData["EditProcessStatus"] = "PendingFirstConfirmation";
        formData["EditApplyTime"] = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds().ToString();

        formData.Remove("EditRejectReason");

        AddApprovalHistory("User", "EditApply", input.Description, formData);


        if (reApply)
        {
            await _dynamicFormApiClient.SetFormDataAsync(businessId, formData);
        }
        else
        {
            var originalFormDefinition = await GetAsync(businessId, false);
            originalFormDefinition.FormData = formData;
            await _dynamicFormApiClient.SaveInstanceAsync(businessId, originalFormDefinition);
        }


        return true;
    }

    public async Task<bool> EditConfirmedAsync(string businessId)
    {
        var formInstance = await _dynamicFormMgtApiClient.GetFormInstanceDtoAsync(businessId);
        if (formInstance.Status != FormInstanceStatus.Draft)
        {
            throw new ValidationException("表单状态不正确/Form status is incorrect");
        }

        // var formData = await _dynamicFormMgtApiClient.GetFormDataAsync(businessId);

        var formDefinition = await _dynamicFormApiClient.GetNewestFormInstanceAsync(businessId);

        if (formDefinition.FormData["ProcessStatus"] != "Approved" ||
            formDefinition.FormData["EditProcessStatus"] != "PendingFirstConfirmation")
        {
            throw new ValidationException("表单状态不正确/ Form status is incorrect");
        }

        var positions = (await _userApiClient.GetUserPositionsAsync()).Select(q => q.Code).ToList();

        if (!positions.Exists(q => q == "CHECKER_LEVEL_1"))
        {
            throw new ValidationException("您不是总审核员/ You are not general auditor");
        }

        formDefinition.FormData["EditProcessStatus"] = "Approved";

        AddApprovalHistory("FirstApproval", "Approval", string.Empty, formDefinition.FormData);


        // --这里要清空掉三要素附件
        formDefinition.Groups.SelectMany(f => f.Fields).ForEach(field =>
        {
            if (field.Code == "ethic_committee_approved_file" || field.Code == "study_protocol" ||
                field.Code == "informed_consent_file")
            {
                field.Value = null;
            }
        });

        await _dynamicFormMgtApiClient.SaveInstanceAsync(businessId, formDefinition);

        return true;
    }

    public async Task<bool> EditRejectedAsync(string businessId, EditProjectRejectOperationDto input)
    {
        var formInstance = await _dynamicFormMgtApiClient.GetFormInstanceDtoAsync(businessId);
        if (formInstance.Status != FormInstanceStatus.Draft)
        {
            throw new ValidationException("表单状态不正确/Form status is incorrect");
        }

        var formData = await _dynamicFormMgtApiClient.GetFormDataAsync(businessId);

        if (formData["ProcessStatus"] != "Approved" || formData["EditProcessStatus"] != "PendingFirstConfirmation")
        {
            throw new ValidationException("表单状态不正确/ Form status is incorrect");
        }

        var positions = (await _userApiClient.GetUserPositionsAsync()).Select(q => q.Code).ToList();

        if (!positions.Exists(q => q == "CHECKER_LEVEL_1"))
        {
            throw new ValidationException("您不是总审核员/ You are not general auditor");
        }

        formData["EditProcessStatus"] = "Rejected";
        formData["EditRejectReason"] = input.RejectReason;

        AddApprovalHistory("FirstApproval", "RejectedEditApply", input.RejectReason, formData);

        await _dynamicFormMgtApiClient.SetFormDataAsync(businessId, formData);

        return true;
    }


    public async Task<bool> AssignAsync(string businessId, AssignProjectOperationDto input)
    {
        var formInstance = await _dynamicFormMgtApiClient.GetFormInstanceDtoAsync(businessId);
        if (formInstance.Status != FormInstanceStatus.Submitted)
        {
            throw new ValidationException("表单状态不正确/Form status is incorrect");
        }

        var formData = await _dynamicFormMgtApiClient.GetFormDataAsync(businessId);

        if (formData["ProcessStatus"] != "PendingSecondAssignment" &&
            formData["ProcessStatus"] != "PendingThirdAssignment")
        {
            throw new ValidationException("表单状态不正确/ Form status is incorrect");
        }

        var level3Users = (await _userMgtApiClient.GetManagedPositionUsersAsync("CHECKER_LEVEL_3"))
            .SelectMany(u => u.Users).ToList();

        var level4Users = (await _userMgtApiClient.GetManagedPositionUsersAsync("CHECKER_LEVEL_4"))
            .SelectMany(u => u.Users).ToList();


        if (!level3Users.Any(u => u.Key.Equals(input.UserId)) && !level4Users.Any(u => u.Key.Equals(input.UserId)))
        {
            throw new ValidationException("用户不在审核组内/ User is not in the review group");
        }

        formData["SecondAssignmentTime"] =
            DateTimeOffset.UtcNow.ToUnixTimeMilliseconds().ToString();

        formData["OrganizationCode"] = input.OrganizationCode;


        if (input.PositionCode == "CHECKER_LEVEL_3")
        {
            var level3User = level3Users.FirstOrDefault(x => x.Key == input.UserId);
            if (level3User == null)
            {
                throw new ValidationException("用户不在审核组内/ User is not in the review group");
            }

            formData["ThirdApprovalUserName"] = level3User?.RealName;
            formData["ThirdApprovalUserId"] = level3User?.Key.ToString();
            formData["ThirdApprovalUserAccount"] = level3User?.Username;
            formData["ProcessStatus"] = "PendingThirdAssignment";
            formData["SkipThirdAssignment"] = false.ToString();

            formData["FourthApprovalUserName"] = null;
            formData["FourthApprovalUserId"] = null;
            formData["FourthApprovalUserAccount"] = null;
        }
        else if (input.PositionCode == "CHECKER_LEVEL_4")
        {
            if (level4Users.All(x => x.Key != input.UserId))
            {
                throw new ValidationException("用户不在审核组内/ User is not in the review group");
            }

            var level4User = level4Users.FirstOrDefault(x => x.Key == input.UserId);

            formData["ThirdApprovalUserName"] = null;
            formData["ThirdApprovalUserId"] = null;
            formData["ThirdApprovalUserAccount"] = null;

            formData["FourthApprovalUserName"] = level4User?.RealName;
            formData["FourthApprovalUserId"] = level4User?.Key.ToString();
            formData["FourthApprovalUserAccount"] = level4User?.Username;
            formData["ProcessStatus"] = "PendingFourthApproval";
            formData["SkipThirdAssignment"] = true.ToString();
        }
        else
        {
            throw new ValidationException("无效的审核组/ Invalid review group");
        }

        formData["LastApprovalUserId"] = _currentUserContext.GetCurrentUser()?.Key.ToString();

        await _dynamicFormMgtApiClient.SetFormDataAsync(businessId, formData);

        return true;
    }

    public async Task<bool> AssignReviewAsync(string businessId, AssignReviewProjectOperationDto input)
    {
        var formInstance = await _dynamicFormMgtApiClient.GetFormInstanceDtoAsync(businessId);
        if (formInstance.Status != FormInstanceStatus.Submitted)
        {
            throw new ValidationException("表单状态不正确/ Form status is incorrect");
        }

        var formData = await _dynamicFormMgtApiClient.GetFormDataAsync(businessId);

        if (formData["ProcessStatus"] != "PendingThirdAssignment")
        {
            throw new ValidationException("表单状态不正确/ Form status is incorrect");
        }

        var level4Users = (await _userMgtApiClient.GetManagedPositionUsersAsync("CHECKER_LEVEL_4"))
            .SelectMany(u => u.Users).ToList();


        if (!level4Users.Any(u => u.Key.Equals(input.UserId)))
        {
            throw new ValidationException("用户不在审核组内/ User is not in the review group");
        }

        formData["ThirdAssignmentTime"] =
            DateTimeOffset.UtcNow.ToUnixTimeMilliseconds().ToString();


        if (level4Users.All(x => x.Key != input.UserId))
        {
            throw new ValidationException("用户不在审核组内/ User is not in the review group");
        }

        var level4User = level4Users.FirstOrDefault(x => x.Key == input.UserId);


        formData["FourthApprovalUserName"] = level4User?.RealName;
        formData["FourthApprovalUserId"] = level4User?.Key.ToString();
        formData["FourthApprovalUserAccount"] = level4User?.Username;
        formData["ProcessStatus"] = "PendingFourthApproval";
        formData["SkipThirdAssignment"] = false.ToString();
        formData["LastApprovalUserId"] = _currentUserContext.GetCurrentUser()?.Key.ToString();
        await _dynamicFormMgtApiClient.SetFormDataAsync(businessId, formData);

        return true;
    }

    public async Task<bool> ReturnLevel2Async(string businessId, AssignProjectReturnOperationDto input)
    {
        var formInstance = await _dynamicFormMgtApiClient.GetFormInstanceDtoAsync(businessId);
        if (formInstance.Status != FormInstanceStatus.Submitted)
        {
            throw new ValidationException("表单状态不正确/Form status is incorrect");
        }

        var formData = await _dynamicFormMgtApiClient.GetFormDataAsync(businessId);

        if (formData["ProcessStatus"] != "PendingSecondAssignment" &&
            formData["ProcessStatus"] != "PendingThirdAssignment")
        {
            throw new ValidationException("表单状态不正确/ Form status is incorrect");
        }

        formData["TraditionalProject"] = string.Empty;

        formData["ProcessStatus"] = string.Empty;

        AddApprovalHistory("SecondApproval", "Return", input.Description, formData);

        await _dynamicFormMgtApiClient.SetFormDataAsync(businessId, formData);

        return true;
    }

    private void AddApprovalHistory(string nodeType, string action, string description,
        Dictionary<string, string?> formData)
    {
        formData.TryGetValue("ApprovalHistory", out var historyString);

        var history = JsonSerializer.Deserialize<List<ApprovalHistoryDto>>(historyString ?? "[]")!;

        history.Add(new ApprovalHistoryDto()
        {
            OperatorId = _currentUserContext.GetCurrentUser()!.Key,
            OperatorName = _currentUserContext.GetCurrentUser()!.RealName,
            OperatorAccount = _currentUserContext.GetCurrentUser()!.Username,
            OperateTime = DateTimeOffset.UtcNow,
            Description = description,
            NodeType = nodeType,
            Action = action
        });

        formData["ApprovalHistory"] = JsonSerializer.Serialize(history);
    }

    public async Task<bool> JudgeAsync(string businessId, JudgeProjectOperationDto input)
    {
        var formInstance = await _dynamicFormMgtApiClient.GetFormInstanceDtoAsync(businessId);
        if (formInstance.Status != FormInstanceStatus.Submitted)
        {
            throw new ValidationException("表单状态不正确/Form status is incorrect");
        }

        var formData = await _dynamicFormMgtApiClient.GetFormDataAsync(businessId);
        formData["TraditionalProject"] = input.TraditionalProject.ToString();
        if (input.TraditionalProject)
        {
            var secondaryAuditors = (await _userMgtApiClient.GetManagedPositionUsersAsync("CHECKER_LEVEL_2"))
                .SelectMany(u => u.Users).ToList();

            var secondaryAuditor = secondaryAuditors.FirstOrDefault(x => x.Key == input.UserId);

            if (secondaryAuditor == null)
            {
                throw new ValidationException("用户不在审核组内/ User is not in the review group");
            }

            formData["SecondApprovalUserName"] = secondaryAuditor.RealName;
            formData["SecondApprovalUserId"] = secondaryAuditor.Key.ToString();
            formData["SecondApprovalUserAccount"] = secondaryAuditor.Username;
            formData["FirstApprovalUserName"] = _currentUserContext.GetCurrentUser()?.RealName;
            formData["FirstApprovalUserId"] = _currentUserContext.GetCurrentUser()?.Key.ToString();
            formData["FirstApprovalUserAccount"] = _currentUserContext.GetCurrentUser()?.Username;
            formData["FirstApprovalTime"] =
                DateTimeOffset.UtcNow.ToUnixTimeMilliseconds().ToString();
            formData["ProcessStatus"] = "PendingSecondAssignment";

            formData["LastApprovalUserId"] = _currentUserContext.GetCurrentUser()?.Key.ToString();
        }


        await _dynamicFormMgtApiClient.SetFormDataAsync(businessId, formData);

        return true;
    }

    /// <summary>
    /// 项目召回
    /// </summary>
    /// <param name="businessId"></param>
    /// <returns></returns>
    public async Task<bool> RecallAsync(string businessId)
    {
        var formInstance = await _dynamicFormMgtApiClient.GetFormInstanceDtoAsync(businessId);
        if (formInstance.Status != FormInstanceStatus.Submitted)
        {
            throw new ValidationException("表单状态不正确/Form status is incorrect");
        }

        var formData = await _dynamicFormMgtApiClient.GetFormDataAsync(businessId);
        formData["TraditionalProject"] = string.Empty;
        formData["ProcessStatus"] = string.Empty;
        AddApprovalHistory("FirstApproval", "Recall", string.Empty, formData);

        await _dynamicFormMgtApiClient.SetFormDataAsync(businessId, formData);

        return true;
    }
}
