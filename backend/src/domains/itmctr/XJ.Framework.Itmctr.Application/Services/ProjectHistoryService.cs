using AutoMapper;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using Microsoft.SqlServer.Server;
using System.Drawing.Printing;
using System.Linq.Expressions;
using System.Reflection;
using System.Text.Json;
using XJ.Framework.DynamicForm.Application.Contract.OperationDtos;
using XJ.Framework.DynamicForm.Application.Contract.QueryCriteria;
using XJ.Framework.DynamicForm.Application.Contract.Services;
using XJ.Framework.DynamicForm.Domain.Shared.Dtos;
using XJ.Framework.DynamicForm.Domain.Shared.Enums;
using XJ.Framework.Files.ApiClient;
using XJ.Framework.Itmctr.Application.Contract.Interfaces;
using XJ.Framework.Itmctr.Application.Contract.OperationDtos;
using XJ.Framework.Itmctr.Application.Contract.QueryCriteria;
using XJ.Framework.Itmctr.Application.Options;
using XJ.Framework.Itmctr.Domain.Entities;
using XJ.Framework.Itmctr.Domain.Repositories.Interfaces;
using XJ.Framework.Itmctr.Domain.Shared.Dtos;
using XJ.Framework.Itmctr.Domain.Shared.Enums;
using XJ.Framework.Library.Application.Contract.Extensions;
using XJ.Framework.Library.Application.Contract.QueryCriteria;
using XJ.Framework.Library.Common.Abstraction.Extensions;
using XJ.Framework.Library.Domain.Entities;
using XJ.Framework.Library.Domain.Id;
using XJ.Framework.Library.Domain.Repositories.Interfaces;
using XJ.Framework.Library.Domain.Shared.Dtos;
using XJ.Framework.Library.Domain.Shared.Interfaces;
using XJ.Framework.Library.Domain.UOW;
using XJ.Framework.Rbac.ApiClient;
using XJ.Framework.Rbac.EntityFrameworkCore.Repositories;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory;
using static System.Runtime.InteropServices.JavaScript.JSType;

namespace XJ.Framework.Itmctr.Application.Services;

public partial class ProjectHistoryService : IProjectHistoryService
{
    private const string FormCode = "PROJECT";
    private readonly IMapper _mapper;
    private readonly JsonSerializerOptions _jsonSerializerOptions;
    private readonly IProjectAttachHistoryRepository _projectAttachHistoryRepository;
    private readonly IProjectSponsorHistoryRepository _projectSponsorHistoryRepository;
    private readonly IProjectResearchSiteHistoryRepository _projectResearchSiteHistoryRepository;
    private readonly IProjectInterventionHistoryRepository _projectInterventionHistoryRepository;
    private readonly IProjectMeasurementHistoryRepository _projectMeasurementHistoryRepository;
    private readonly IProjectHumanSampleHistoryRepository _projectHumanSampleHistoryRepository;
    private readonly IProjectHistoryRepository _projectHistoryRepository;
    private readonly IProjectRepository _projectRepository;
    private readonly IUnitOfWork _unitOfWork;

    public ProjectHistoryService(
        IMapper mapper,
        IOptions<JsonOptions> jsonOptions,
        IUnitOfWork unitOfWork,
        IProjectHistoryRepository projectHistoryRepository,
        IProjectAttachHistoryRepository projectAttachHistoryRepository,
        IProjectSponsorHistoryRepository projectSponsorHistoryRepository,
        IProjectResearchSiteHistoryRepository projectResearchSiteHistoryRepository,
        IProjectInterventionHistoryRepository projectInterventionHistoryRepository,
        IProjectMeasurementHistoryRepository projectMeasurementHistoryRepository,
        IProjectHumanSampleHistoryRepository projectHumanSampleHistoryRepository,
        IProjectRepository projectRepository
    )
    {
        _mapper = mapper;
        _jsonSerializerOptions = jsonOptions.Value.JsonSerializerOptions;
        _projectHistoryRepository = projectHistoryRepository;
        _projectAttachHistoryRepository = projectAttachHistoryRepository;
        _projectSponsorHistoryRepository = projectSponsorHistoryRepository;
        _projectResearchSiteHistoryRepository = projectResearchSiteHistoryRepository;
        _projectInterventionHistoryRepository = projectInterventionHistoryRepository;
        _projectMeasurementHistoryRepository = projectMeasurementHistoryRepository;
        _projectHumanSampleHistoryRepository = projectHumanSampleHistoryRepository;
        _unitOfWork = unitOfWork;
        _projectRepository = projectRepository;
    }

    public async Task<ProjectPageDto> GetProjectHistoryPageAsync(
       [FromQuery] PagedQueryCriteria<ProjectQueryCriteria> criteria)
    {
        var whereExpr = criteria.Condition.BuildExpression<long, ProjectHistoryEntity, ProjectQueryCriteria>();
        whereExpr = whereExpr.And(x => x.BusinessId == criteria.Condition.businessId);
        
        var history = await _projectHistoryRepository.GetListAsync(x => x.BusinessId == criteria.Condition.businessId);

        var projects = await _projectRepository.GetListAsync(x => x.BusinessId == criteria.Condition.businessId);

        return await ConvertProjectPageDataAsync(history, projects,criteria.PageParams.ToRowIndex(), criteria.PageParams.PageSize);
    }

    private async Task<ProjectPageDto> ConvertProjectPageDataAsync(IEnumerable<ProjectHistoryEntity> history, IEnumerable<ProjectEntity> projects,int pageIndex,int pageSize)
    {
        // 获取所有业务ID
        var businessIds = history.Select(x => x.BusinessId).Distinct().ToList();
        // 批量查询所有相关历史记录
        var historyList = await _projectHistoryRepository.GetListAsync(q => businessIds.Contains(q.BusinessId));

        // 查找最早的 CreatedTime
        var minHistoryTime = history.Any() ? history.Min(h => h.CreatedTime) : (DateTimeOffset?)null;
        var minEntityTime = projects.Any() ? projects.Min(e => e.CreatedTime) : (DateTimeOffset?)null;
        var earliestCreatedTime = new[] { minHistoryTime, minEntityTime }
            .Where(t => t.HasValue)
            .OrderBy(t => t)
            .FirstOrDefault();

        // 映射并赋值 first_submit_time
        var dtoList = history.Select(project =>
        {
            var dto = _mapper.Map<ProjectsDto>(project);
            dto.first_submit_time = earliestCreatedTime;
            return dto;
        }).ToList();

        foreach (var project in projects)
        {
            // 判断是否已存在相同 businessId+version 的记录
            if (!dtoList.Any(d => d.BusinessId == project.BusinessId && d.Version == project.Version))
            {
                var dto = _mapper.Map<ProjectsDto>(project);
                dto.first_submit_time = earliestCreatedTime;
                dto.IsHistory = false;
                dtoList.Add(dto);
            }
        }

        // 构造 ProjectPageDto
        var result = new ProjectPageDto
        {
            Totals = dtoList.Count,
            Rows = dtoList.Skip(pageIndex).Take(pageSize).ToList()
        };
        return result;
    }

    public async Task<ProjectsDto?> GetProjectHistoryInfoAsync(long key)
    {
        var project = await _projectHistoryRepository.GetAsync(key);
        if (project == null)
        {
            throw new ValidationException("记录不存在/Record does not exist");
        }
        ProjectsDto dto = new ProjectsDto() { Key = 0 };
        _mapper.Map(project, dto);
        //files
        //var files = await _projectAttachRepository.GetListAsync(q => q.ProjectId == project.Key);
        //试验主办单位
        var existSponsorList = await _projectSponsorHistoryRepository.GetListAsync(q => q.ProjectId == project.Key && q.BusinessId == project.BusinessId);
        dto.sponsor = _mapper.Map<List<ProjectSponsorDto>>(existSponsorList);
        // 研究实施地点
        var existSiteList = await _projectResearchSiteHistoryRepository.GetListAsync(q => q.ProjectId == project.Key && q.BusinessId == project.BusinessId);
        dto.researchSite = _mapper.Map<List<ProjectResearchSiteDto>>(existSiteList);
        // 干预措施
        var existInterventionList =
            await _projectInterventionHistoryRepository.GetListAsync(q => q.ProjectId == project.Key && q.BusinessId == project.BusinessId);
        dto.intervention = _mapper.Map<List<ProjectInterventionDto>>(existInterventionList);
        // 测量指标
        var existMeasurementList =
            await _projectMeasurementHistoryRepository.GetListAsync(q => q.ProjectId == project.Key && q.BusinessId == project.BusinessId);
        dto.measurement = _mapper.Map<List<ProjectMeasurementDto>>(existMeasurementList);
        // 采集人体标本
        var existHumanSampleList =
            await _projectHumanSampleHistoryRepository.GetListAsync(q => q.ProjectId == project.Key && q.BusinessId == project.BusinessId);
        dto.humanSample = _mapper.Map<List<ProjectHumanSampleDto>>(existHumanSampleList);
        return dto;
    }
}
