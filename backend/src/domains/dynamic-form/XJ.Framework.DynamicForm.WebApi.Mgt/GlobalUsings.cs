
global using XJ.Framework.Library.Application.Extensions;
global using XJ.Framework.Library.WebApi;
global using XJ.Framework.Library.WebApi.Controllers;

global using Microsoft.AspNetCore.Mvc;
global using XJ.Framework.Library.Application.Contract.QueryCriteria;
global using XJ.Framework.Library.Domain.Shared.Dtos;
global using XJ.Framework.Library.WebApi.Extensions;
global using XJ.Framework.Library.Application.Services;
global using XJ.Framework.Library.Domain.Attributes;
global using XJ.Framework.Library.WebApi.Services;

global using XJ.Framework.DynamicForm.Application.Contract.Interfaces;
global using XJ.Framework.DynamicForm.Application.Contract.OperationDtos;
global using XJ.Framework.DynamicForm.Application.Contract.QueryCriteria;
global using XJ.Framework.DynamicForm.Domain.Shared.Dtos;
global using XJ.Framework.DynamicForm.Application;
global using XJ.Framework.DynamicForm.EntityFrameworkCore;
global using XJ.Framework.DynamicForm.WebApi;
global using XJ.Framework.Rbac.ApiClient;