using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace XJ.Framework.DynamicForm.EntityFrameworkCore.Migrations
{
    /// <inheritdoc />
    public partial class reject_reason_Initial : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.EnsureSchema(
                name: "d");

            migrationBuilder.CreateTable(
                name: "form_field_groups",
                schema: "d",
                columns: table => new
                {
                    id = table.Column<long>(type: "bigint", nullable: false),
                    form_code = table.Column<string>(type: "nvarchar(450)", nullable: false, comment: "表单ID"),
                    version = table.Column<string>(type: "nvarchar(450)", nullable: false, comment: "表单版本"),
                    code = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false, comment: "分组编码"),
                    title_zh = table.Column<string>(type: "nvarchar(400)", maxLength: 400, nullable: true, comment: "中文标题"),
                    title_en = table.Column<string>(type: "nvarchar(400)", maxLength: 400, nullable: true, comment: "英文标题"),
                    sort_order = table.Column<int>(type: "int", nullable: false, comment: "排序"),
                    created_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    last_modified_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    last_modified_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_form_field_groups", x => x.id);
                },
                comment: "FormFieldGroup 实体");

            migrationBuilder.CreateTable(
                name: "form_field_instances",
                schema: "d",
                columns: table => new
                {
                    id = table.Column<long>(type: "bigint", nullable: false),
                    form_code = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false, comment: "表单编码"),
                    form_version = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false, comment: "表单版本"),
                    version = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false, comment: "当前实例版本"),
                    business_id = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false, comment: "当前实例业务id"),
                    code = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false, comment: "字段编码"),
                    json_value = table.Column<string>(type: "nvarchar(max)", nullable: true, comment: "字段实例值"),
                    annotations = table.Column<string>(type: "nvarchar(max)", nullable: true, comment: "批注"),
                    display = table.Column<string>(type: "nvarchar(400)", maxLength: 400, nullable: true, comment: "字段实例值显示"),
                    parent_code = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true, comment: "上级字段编码"),
                    type = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false, comment: "字段类型"),
                    row_index = table.Column<int>(type: "int", nullable: true, comment: "值所在行 该值仅在多行子表单时有效"),
                    created_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    last_modified_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    last_modified_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_form_field_instances", x => x.id);
                },
                comment: "FormFieldInstance 实体");

            migrationBuilder.CreateTable(
                name: "form_fields",
                schema: "d",
                columns: table => new
                {
                    id = table.Column<long>(type: "bigint", nullable: false),
                    form_code = table.Column<string>(type: "nvarchar(450)", nullable: false, comment: "表单ID，关联[d].[forms].[code]"),
                    version = table.Column<string>(type: "nvarchar(450)", nullable: false, comment: "表单版本"),
                    parent_code = table.Column<string>(type: "nvarchar(450)", nullable: true, comment: "父字段编码，支持子表单结构，根字段为NULL"),
                    group_code = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false, comment: "分组编码，关联[d].[form_field_groups].[code]，可为空"),
                    code = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false, comment: "字段唯一编码，同一form_id下唯一"),
                    label_zh = table.Column<string>(type: "nvarchar(400)", maxLength: 400, nullable: true, comment: "中文标签"),
                    label_en = table.Column<string>(type: "nvarchar(400)", maxLength: 400, nullable: true, comment: "英文标签"),
                    type = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false, comment: "控件类型，可选值：text（单行文本）、text_multilang（双语单行文本）、textarea（多行文本）、textarea_multilang（双语多行文本）、select（下拉）、checkbox（多选）、radio（单选）、date（日期）、number（数字）、unit_select（数字+单位下拉）、int_range（整数范围）、date_range（日期范围）、form_single（单行子表单）、form_multi（多行子表单）"),
                    required = table.Column<bool>(type: "bit", nullable: false, comment: "是否必填，0-否，1-是"),
                    newLine = table.Column<bool>(type: "bit", nullable: false, comment: "是否新起一行，0-否，1-是"),
                    options = table.Column<string>(type: "nvarchar(max)", nullable: true, comment: "选项（json），type为select/checkbox/radio/unit_select等有选项的控件时使用，支持中英文双语。\\nselect/checkbox/radio格式如：[{'label_zh':'选项1', 'label_en':'Option 1', 'value':'1'}]；\\nunit_select格式如：[{'label_zh':'天', 'label_en':'Day', 'value':'day'}]；\\nint_range格式如：{\"min\":0, \"max\":100, \"step\":1}；\\ndate_range格式如：{\"min\":\"2020-01-01\", \"max\":\"2030-12-31\"}"),
                    default_value = table.Column<string>(type: "nvarchar(max)", nullable: true, comment: "默认值，类型与控件type对应。\\nselect/checkbox/radio为选项value，checkbox为数组；\\nunit_select为{\"value\":1,\"unit\":\"day\"}；\\nint_range为{\"min\":10, \"max\":20}；\\ndate_range为{\"start\":\"2023-01-01\", \"end\":\"2023-12-31\"}；\\ntext_multilang/textarea_multilang为{\"zh\":\"中文值\", \"en\":\"英文值\"}"),
                    colspan = table.Column<int>(type: "int", nullable: false, comment: "控件占用列数，默认1，范围1-3"),
                    sort_order = table.Column<int>(type: "int", nullable: false, comment: "排序，正序排列"),
                    extra_config = table.Column<string>(type: "nvarchar(max)", nullable: true, comment: "额外配置（json），如校验规则、动态行为等"),
                    description = table.Column<string>(type: "nvarchar(800)", maxLength: 800, nullable: true, comment: "字段备注"),
                    reject_reason = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: true, comment: "字段驳回时的批注信息"),
                    created_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    last_modified_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    last_modified_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_form_fields", x => x.id);
                },
                comment: "FormField 实体");

            migrationBuilder.CreateTable(
                name: "form_instance_datas",
                schema: "d",
                columns: table => new
                {
                    id = table.Column<long>(type: "bigint", nullable: false),
                    form_code = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false, comment: "表单编码"),
                    form_version = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false, comment: "表单版本"),
                    version = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false, comment: "当前实例版本"),
                    business_id = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false, comment: "当前实例业务id"),
                    code = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false, comment: "数据编码"),
                    value = table.Column<string>(type: "nvarchar(max)", nullable: true, comment: "数据值"),
                    created_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    last_modified_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    last_modified_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_form_instance_datas", x => x.id);
                },
                comment: "表单数据实体");

            migrationBuilder.CreateTable(
                name: "form_instances",
                schema: "d",
                columns: table => new
                {
                    id = table.Column<long>(type: "bigint", nullable: false),
                    language = table.Column<string>(type: "nvarchar(max)", nullable: true, comment: "填写语言"),
                    form_code = table.Column<string>(type: "nvarchar(max)", nullable: false, comment: "表单code"),
                    form_version = table.Column<string>(type: "nvarchar(max)", nullable: false, comment: "表单版本"),
                    business_id = table.Column<string>(type: "nvarchar(450)", nullable: false, comment: "关联业务id"),
                    version = table.Column<string>(type: "nvarchar(450)", nullable: false, comment: "版本号"),
                    previous_instance_id = table.Column<long>(type: "bigint", nullable: true, comment: "前一个版本的表单实例id"),
                    version_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false, comment: "版本创建时间"),
                    previous_version = table.Column<string>(type: "nvarchar(max)", nullable: true, comment: "前一个版本的版本号"),
                    status = table.Column<int>(type: "int", nullable: false, comment: "表单状态 1- 草稿 2- 已提交 3- 已确认 4- 已作废 5- 已驳回"),
                    apply_user_id = table.Column<long>(type: "bigint", nullable: false, comment: "发起人"),
                    obsoleted = table.Column<bool>(type: "bit", nullable: false, comment: "是否已失效"),
                    created_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    last_modified_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    last_modified_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_form_instances", x => x.id);
                },
                comment: "FormInstance 实体");

            migrationBuilder.CreateTable(
                name: "forms",
                schema: "d",
                columns: table => new
                {
                    id = table.Column<long>(type: "bigint", nullable: false),
                    code = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false, comment: "表单唯一编码"),
                    name = table.Column<string>(type: "nvarchar(400)", maxLength: 400, nullable: false, comment: "表单名称"),
                    version = table.Column<string>(type: "nvarchar(40)", maxLength: 40, nullable: false, comment: "表单版本"),
                    description = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true, comment: "表单描述"),
                    json_config = table.Column<string>(type: "nvarchar(max)", nullable: true, comment: "全局配置（json）"),
                    newest_version = table.Column<bool>(type: "bit", nullable: false, comment: "是否最新版本"),
                    created_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    created_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    last_modified_time = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    last_modified_by = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_forms", x => x.id);
                },
                comment: "Form 实体");

            migrationBuilder.InsertData(
                schema: "d",
                table: "form_field_groups",
                columns: new[] { "id", "code", "created_by", "created_time", "is_deleted", "form_code", "last_modified_by", "last_modified_time", "sort_order", "title_en", "title_zh", "version" },
                values: new object[] { 1932608575046221825L, "grp_1746628396941-8913", "System", new DateTimeOffset(new DateTime(2025, 5, 7, 14, 40, 8, 936, DateTimeKind.Unspecified).AddTicks(7730), new TimeSpan(0, 8, 0, 0, 0)), false, "demo", null, null, 0, "Basic Information", "基本信息", "20250507144008890" });

            migrationBuilder.InsertData(
                schema: "d",
                table: "form_fields",
                columns: new[] { "id", "code", "colspan", "created_by", "created_time", "default_value", "is_deleted", "description", "extra_config", "form_code", "group_code", "label_en", "label_zh", "last_modified_by", "last_modified_time", "newLine", "options", "parent_code", "reject_reason", "required", "sort_order", "type", "version" },
                values: new object[,]
                {
                    { 1932608575046221826L, "name", 2, "System", new DateTimeOffset(new DateTime(2025, 5, 7, 14, 40, 8, 936, DateTimeKind.Unspecified).AddTicks(7730), new TimeSpan(0, 8, 0, 0, 0)), "{\"zh\":\"\",\"en\":\"\"}", false, null, "{}", "demo", "grp_1746628396941-8913", "Name", "姓名", null, null, false, null, null, null, true, 0, "text_multilang", "20250507144008890" },
                    { 1932608575046221827L, "sex", 2, "System", new DateTimeOffset(new DateTime(2025, 5, 7, 14, 40, 8, 936, DateTimeKind.Unspecified).AddTicks(7730), new TimeSpan(0, 8, 0, 0, 0)), "\"\"", false, null, "{}", "demo", "grp_1746628396941-8913", "Sex", "性别", null, null, false, "[{\"labelZh\":\"男性\",\"labelEn\":\"Male\",\"value\":\"male\"},{\"labelZh\":\"女性\",\"labelEn\":\"Female\",\"value\":\"female\"}]", null, null, true, 1, "radio", "20250507144008890" },
                    { 1932608575046221828L, "location", 3, "System", new DateTimeOffset(new DateTime(2025, 5, 7, 14, 40, 8, 936, DateTimeKind.Unspecified).AddTicks(7730), new TimeSpan(0, 8, 0, 0, 0)), "\"\"", false, null, "{}", "demo", "grp_1746628396941-8913", "Location", "所在地", null, null, false, null, null, null, false, 2, "textarea", "20250507144008890" },
                    { 1932608575046221829L, "hobby", 2, "System", new DateTimeOffset(new DateTime(2025, 5, 7, 14, 40, 8, 936, DateTimeKind.Unspecified).AddTicks(7730), new TimeSpan(0, 8, 0, 0, 0)), "[]", false, null, "{}", "demo", "grp_1746628396941-8913", "Hobby", "爱好", null, null, false, "[{\"labelZh\":\"游泳\",\"labelEn\":\"Swimming\",\"value\":\"swimming\"},{\"labelZh\":\"跑步\",\"labelEn\":\"Running\",\"value\":\"running\"},{\"labelZh\":\"滑雪\",\"labelEn\":\"Skiing\",\"value\":\"skiing\"}]", null, null, false, 3, "checkbox", "20250507144008890" },
                    { 1932608575046221830L, "mobile", 2, "System", new DateTimeOffset(new DateTime(2025, 5, 7, 14, 40, 8, 936, DateTimeKind.Unspecified).AddTicks(7730), new TimeSpan(0, 8, 0, 0, 0)), "\"\"", false, null, "{}", "demo", "grp_1746628396941-8913", "Mobile", "手机号", null, null, true, null, null, null, true, 4, "input", "20250507144008890" },
                    { 1932608575046221831L, "trialTime", 2, "System", new DateTimeOffset(new DateTime(2025, 5, 7, 14, 40, 8, 936, DateTimeKind.Unspecified).AddTicks(7730), new TimeSpan(0, 8, 0, 0, 0)), "{\"start\":\"\",\"end\":\"\"}", false, null, "{\"format\":\"YYYY-MM-DD\"}", "demo", "grp_1746628396941-8913", "TrialTime", "试验参与时间", null, null, false, null, null, null, true, 5, "date_range", "20250507144008890" },
                    { 1932608575046221832L, "period", 2, "System", new DateTimeOffset(new DateTime(2025, 5, 7, 14, 40, 8, 936, DateTimeKind.Unspecified).AddTicks(7730), new TimeSpan(0, 8, 0, 0, 0)), "{\"value\":\"\",\"unit\":\"\"}", false, null, "{}", "demo", "grp_1746628396941-8913", "Period", "访试周期", null, null, false, "[{\"labelZh\":\"月\",\"labelEn\":\"Month\",\"value\":\"month\"},{\"labelZh\":\"周\",\"labelEn\":\"Week\",\"value\":\"week\"},{\"labelZh\":\"天\",\"labelEn\":\"Day\",\"value\":\"day\"}]", null, null, true, 6, "unit_select", "20250507144008890" },
                    { 1932608575046221833L, "edu_group", 4, "System", new DateTimeOffset(new DateTime(2025, 5, 12, 15, 10, 40, 559, DateTimeKind.Unspecified).AddTicks(8890), new TimeSpan(0, 8, 0, 0, 0)), "[{\"edu_school_name\":\"\",\"edu_level\":\"\",\"edu_end\":\"\"}]", false, null, "{}", "demo", "grp_1746628396941-8913", "", "教育经历", null, null, false, null, null, null, false, 7, "multiSubForm", "20250507144008890" },
                    { 1932608575046221834L, "edu_school_name", 2, "System", new DateTimeOffset(new DateTime(2025, 5, 12, 15, 10, 40, 559, DateTimeKind.Unspecified).AddTicks(8890), new TimeSpan(0, 8, 0, 0, 0)), "\"\"", false, null, "{}", "demo", "grp_1746628396941-8913", "", "学校名称", null, null, false, null, "edu_group", null, false, 8, "input", "20250507144008890" },
                    { 1932608575046221835L, "edu_level", 2, "System", new DateTimeOffset(new DateTime(2025, 5, 12, 15, 10, 40, 559, DateTimeKind.Unspecified).AddTicks(8890), new TimeSpan(0, 8, 0, 0, 0)), "\"\"", false, null, "{}", "demo", "grp_1746628396941-8913", "", "学历", null, null, false, "[{\"labelZh\":\"高中\",\"labelEn\":\"Option 1\",\"value\":\"option1\"},{\"labelZh\":\"专科\",\"labelEn\":\"Option 2\",\"value\":\"option2\"},{\"labelZh\":\"本科\",\"labelEn\":\"Option 3\",\"value\":\"option3\"}]", "edu_group", null, false, 9, "select", "20250507144008890" },
                    { 1932608575046221836L, "edu_end", 2, "System", new DateTimeOffset(new DateTime(2025, 5, 12, 15, 10, 40, 559, DateTimeKind.Unspecified).AddTicks(8890), new TimeSpan(0, 8, 0, 0, 0)), "\"\"", false, null, "{\"format\":\"YYYY-MM-DD\"}", "demo", "grp_1746628396941-8913", "", "毕业时间", null, null, false, null, "edu_group", null, false, 10, "date", "20250507144008890" },
                    { 1932608575046221837L, "random_group", 4, "System", new DateTimeOffset(new DateTime(2025, 5, 12, 15, 10, 40, 559, DateTimeKind.Unspecified).AddTicks(8890), new TimeSpan(0, 8, 0, 0, 0)), "null", false, null, "{}", "demo", "grp_1746628396941-8913", "Random Group", "随机分组", null, null, false, null, null, null, false, 11, "subForm", "20250507144008890" },
                    { 1932608575046221838L, "random_group_sex", 2, "System", new DateTimeOffset(new DateTime(2025, 5, 12, 15, 10, 40, 559, DateTimeKind.Unspecified).AddTicks(8890), new TimeSpan(0, 8, 0, 0, 0)), "\"\"", false, null, "{}", "demo", "grp_1746628396941-8913", "", "所属随机性别分组", null, null, false, "[{\"labelZh\":\"男\",\"labelEn\":\"Male\",\"value\":\"male\"},{\"labelZh\":\"女\",\"labelEn\":\"Female\",\"value\":\"female\"}]", "random_group", null, false, 12, "radio", "20250507144008890" },
                    { 1932608575046221839L, "random_group_name", 2, "System", new DateTimeOffset(new DateTime(2025, 5, 12, 15, 10, 40, 559, DateTimeKind.Unspecified).AddTicks(8890), new TimeSpan(0, 8, 0, 0, 0)), "\"\"", false, null, "{}", "demo", "grp_1746628396941-8913", "", "随机分组", null, null, false, "[{\"labelZh\":\"随机组\",\"labelEn\":\"Random Group\",\"value\":\"random_group\"},{\"labelZh\":\"对照组\",\"labelEn\":\"Control Group\",\"value\":\"control_group\"}]", "random_group", null, false, 13, "select", "20250507144008890" }
                });

            migrationBuilder.InsertData(
                schema: "d",
                table: "forms",
                columns: new[] { "id", "code", "created_by", "created_time", "is_deleted", "description", "json_config", "last_modified_by", "last_modified_time", "name", "newest_version", "version" },
                values: new object[] { 1932608575046221824L, "demo", "System", new DateTimeOffset(new DateTime(2025, 5, 7, 14, 40, 8, 936, DateTimeKind.Unspecified).AddTicks(7730), new TimeSpan(0, 8, 0, 0, 0)), false, null, null, null, null, "试验登记表", true, "20250507144008890" });

            migrationBuilder.CreateIndex(
                name: "IX_form_field_groups_form_code_version_code_not_deleted",
                schema: "d",
                table: "form_field_groups",
                columns: new[] { "form_code", "code", "version" },
                unique: true,
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_form_field_instance_form_code_version_code_not_deleted",
                schema: "d",
                table: "form_field_instances",
                columns: new[] { "form_code", "form_version", "code", "version", "row_index", "business_id" },
                unique: true,
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_form_fields_form_id_not_deleted",
                schema: "d",
                table: "form_fields",
                column: "form_code",
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "UX_form_fields_form_code_version_code_parent_code_not_deleted",
                schema: "d",
                table: "form_fields",
                columns: new[] { "form_code", "code", "version", "parent_code" },
                unique: true,
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_form_instance_data_form_code_version_code_not_deleted",
                schema: "d",
                table: "form_instance_datas",
                columns: new[] { "form_code", "form_version", "code", "version", "business_id" },
                unique: true,
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "IX_form_instances_business_id_not_deleted",
                schema: "d",
                table: "form_instances",
                column: "business_id",
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "UX_form_instances_version_business_id_not_deleted",
                schema: "d",
                table: "form_instances",
                columns: new[] { "version", "business_id" },
                unique: true,
                filter: "[is_deleted] = 0");

            migrationBuilder.CreateIndex(
                name: "UX_forms_code_version_not_deleted",
                schema: "d",
                table: "forms",
                columns: new[] { "code", "version" },
                unique: true,
                filter: "[is_deleted] = 0");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "form_field_groups",
                schema: "d");

            migrationBuilder.DropTable(
                name: "form_field_instances",
                schema: "d");

            migrationBuilder.DropTable(
                name: "form_fields",
                schema: "d");

            migrationBuilder.DropTable(
                name: "form_instance_datas",
                schema: "d");

            migrationBuilder.DropTable(
                name: "form_instances",
                schema: "d");

            migrationBuilder.DropTable(
                name: "forms",
                schema: "d");
        }
    }
}
