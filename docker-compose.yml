version: "3.7"
name: xj-framework

networks:
  xjframework-network:
    driver: bridge

services:
  rbac-webapi:
    image: xj-framework/rbac-webapi:latest
    build:
      context: backend
      dockerfile: src/domains/rbac/XJ.Framework.Rbac.WebApi/Dockerfile${STAGE}
    ports:
      - "8801:8080"
      - "9901:8081"
    environment:
      - ASPNETCORE_ENVIRONMENT=${Env:-DockerDev}
      - TZ=Asia/Shanghai
    volumes:
      - ./.usersecrets:/home/<USER>/.microsoft/usersecrets:ro

  rbac-webapi-mgt:
    image: xj-framework/rbac-webapi-mgt:latest
    build:
      context: backend
      dockerfile: src/domains/rbac/XJ.Framework.Rbac.WebApi.Mgt/Dockerfile${STAGE}
    ports:
      - "8802:8080"
      - "9902:8081"
    environment:
      - ASPNETCORE_ENVIRONMENT=${Env:-DockerDev}
      - TZ=Asia/Shanghai
    volumes:
      - ./.usersecrets:/home/<USER>/.microsoft/usersecrets:ro

  files-webapi:
    image: xj-framework/files-webapi:latest
    build:
      context: backend
      dockerfile: src/domains/files/XJ.Framework.Files.WebApi/Dockerfile${STAGE}
    ports:
      - "8803:8080"
      - "9903:8081"
    volumes:
      - ./default-files:/app/default:rw
      - ./.usersecrets:/home/<USER>/.microsoft/usersecrets:ro
    environment:
      - ASPNETCORE_ENVIRONMENT=${Env:-DockerDev}
      - TZ=Asia/Shanghai

  files-webapi-mgt:
    image: xj-framework/files-webapi-mgt:latest
    build:
      context: backend
      dockerfile: src/domains/files/XJ.Framework.Files.WebApi.Mgt/Dockerfile${STAGE}
    ports:
      - "8804:8080"
      - "9904:8081"
    environment:
      - ASPNETCORE_ENVIRONMENT=${Env:-DockerDev}
      - TZ=Asia/Shanghai
    volumes:
      - ./.usersecrets:/home/<USER>/.microsoft/usersecrets:ro

  dynamic-form-webapi:
    image: xj-framework/dynamic-form-webapi:latest
    build:
      context: backend
      dockerfile: src/domains/dynamic-form/XJ.Framework.DynamicForm.WebApi/Dockerfile${STAGE}
    ports:
      - "8805:8080"
      - "9905:8081"
    environment:
      - ASPNETCORE_ENVIRONMENT=${Env:-DockerDev}
      - TZ=Asia/Shanghai
    volumes:
      - ./.usersecrets:/home/<USER>/.microsoft/usersecrets:ro
  dynamic-form-webapi-mgt:
    image: xj-framework/dynamic-form-webapi-mgt:latest
    build:
      context: backend
      dockerfile: src/domains/dynamic-form/XJ.Framework.DynamicForm.WebApi.Mgt/Dockerfile${STAGE}
    ports:
      - "8806:8080"
      - "9906:8081"
    environment:
      - ASPNETCORE_ENVIRONMENT=${Env:-DockerDev}
      - TZ=Asia/Shanghai
    volumes:
      - ./.usersecrets:/home/<USER>/.microsoft/usersecrets:ro

  itmctr-webapi:
    image: xj-framework/itmctr-webapi:latest
    build:
      context: backend
      dockerfile: src/domains/itmctr/XJ.Framework.Itmctr.WebApi/Dockerfile${STAGE}
    ports:
      - "8807:8080"
      - "9907:8081"
    environment:
      - ASPNETCORE_ENVIRONMENT=${Env:-DockerDev}
      - TZ=Asia/Shanghai
    volumes:
      - ./.usersecrets:/home/<USER>/.microsoft/usersecrets:ro
  itmctr-webapi-mgt:
    image: xj-framework/itmctr-webapi-mgt:latest
    build:
      context: backend
      dockerfile: src/domains/itmctr/XJ.Framework.Itmctr.WebApi.Mgt/Dockerfile${STAGE}
    ports:
      - "8808:8080"
      - "9908:8081"
    environment:
      - ASPNETCORE_ENVIRONMENT=${Env:-DockerDev}
      - TZ=Asia/Shanghai
    volumes:
      - ./.usersecrets:/home/<USER>/.microsoft/usersecrets:ro
  logging-webapi-mgt:
    image: xj-framework/logging-webapi-mgt:latest
    build:
      context: backend
      dockerfile: src/domains/logging/XJ.Framework.Logging.WebApi.Mgt/Dockerfile${STAGE}
    ports:
      - "8810:8080"
      - "9910:8081"
    environment:
      - ASPNETCORE_ENVIRONMENT=${Env:-DockerDev}
      - TZ=Asia/Shanghai
    volumes:
      - ./.usersecrets:/home/<USER>/.microsoft/usersecrets:ro




  messaging-webapi:
    image: xj-framework/messaging-webapi:latest
    build:
      context: backend
      dockerfile: src/domains/messaging/XJ.Framework.Messaging.WebApi/Dockerfile${STAGE}
    ports:
      - "8811:8080"
      - "9911:8081"
    environment:
      - ASPNETCORE_ENVIRONMENT=${Env:-DockerDev}
      - TZ=Asia/Shanghai
    volumes:
      - ./.usersecrets:/home/<USER>/.microsoft/usersecrets:ro
  messaging-webapi-mgt:
    image: xj-framework/messaging-webapi-mgt:latest
    build:
      context: backend
      dockerfile: src/domains/messaging/XJ.Framework.Messaging.WebApi.Mgt/Dockerfile${STAGE}
    ports:
      - "8812:8080"
      - "9912:8081"
    environment:
      - ASPNETCORE_ENVIRONMENT=${Env:-DockerDev}
      - TZ=Asia/Shanghai
    volumes:
      - ./.usersecrets:/home/<USER>/.microsoft/usersecrets:ro




  itmctr-web:
    image: xj-framework/itmctr-web:latest
    build:
      context: frontend
      dockerfile: Dockerfile
      args:
        Env: ${Env:-DockerDev}
        WEB_BASE_URL: ${WEB_BASE_URL}
        MGT_PROXY_PATH: ${MGT_PROXY_PATH}
    ports:
      - "8880:80"
    environment:
      - TZ=Asia/Shanghai

  itmctr-portal:
    image: xj-framework/itmctr-portal:latest
    ports:
      - "8800:80"
    build:
      context: portal
      dockerfile: Dockerfile
      args:
        WEB_BASE_URL: ${WEB_BASE_URL}
        MGT_PROXY_PATH: ${MGT_PROXY_PATH}
        HOST: ${HOST}
    depends_on:
      - itmctr-web

  redis:
    image: redis:alpine
    ports:
      - "6379:6379"